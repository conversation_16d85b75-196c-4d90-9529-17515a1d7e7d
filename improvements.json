[{"id": "retrieval_augmented_memory", "title": "Retrieval-Augmented Memory", "description": "Store and retrieve contextually relevant chat history using vector embeddings.", "implemented": false}, {"id": "plugin_ecosystem", "title": "Plugin Ecosystem Enhancements", "description": "Support async plugin hooks, custom commands, and UI extensions.", "implemented": false}, {"id": "multi_backend_llm", "title": "Multi-Backend LLM Support", "description": "Allow switching between OpenAI, Ollama, and local models.", "implemented": false}, {"id": "interactive_cli_tui", "title": "Interactive CLI & TUI", "description": "Add readline shell with history, tab completion, and a curses dashboard.", "implemented": false}, {"id": "long_term_summarization", "title": "Long-term Summarization & Chunking", "description": "Summarize and chunk chat history to stay within token limits.", "implemented": false}, {"id": "automated_testing", "title": "Automated Testing & CI/CD", "description": "Generate unit tests and add CI workflows.", "implemented": false}, {"id": "safety_first_execution", "title": "Safety-First Command Execution", "description": "Whitelist/blacklist commands and require confirmation for risky actions.", "implemented": false}, {"id": "telemetry_analytics", "title": "Configurable Telemetry & Analytics", "description": "Track usage metrics and expose a stats command.", "implemented": false}, {"id": "gui_enhancements", "title": "GUI Enhancements", "description": "Add a simple GUI window with live GPU graph and plugin buttons.", "implemented": false}, {"id": "developer_tooling", "title": "Developer <PERSON>", "description": "Expose a JSON/RPC or REST endpoint for programmatic control.", "implemented": false}]