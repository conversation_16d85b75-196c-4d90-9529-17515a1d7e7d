#!/usr/bin/env python3
"""
Example Plugin for Setup Agent
Demonstrates how to create plugins for the setup agent.
"""

def register(plugins_dict):
    """Register this plugin with the setup agent."""
    plugins_dict['example'] = {
        'description': 'Example plugin demonstrating plugin system',
        'version': '1.0.0',
        'author': 'Setup Agent',
        'commands': {
            'hello': hello_command,
            'system_info': system_info_command
        }
    }

def hello_command():
    """Simple hello command."""
    print("👋 Hello from the example plugin!")
    print("This demonstrates how plugins can extend the setup agent.")

def system_info_command():
    """Display basic system information."""
    import platform
    import os
    
    print("💻 System Information:")
    print(f"  • OS: {platform.system()} {platform.release()}")
    print(f"  • Python: {platform.python_version()}")
    print(f"  • Architecture: {platform.machine()}")
    print(f"  • Current Directory: {os.getcwd()}")
    print(f"  • User: {os.getenv('USERNAME', 'Unknown')}")
