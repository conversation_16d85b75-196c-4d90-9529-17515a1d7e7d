#!/usr/bin/env python3
"""
Migration script to transition from monolithic setup_agent.py to modular architecture.
"""

import os
import shutil
import json
import logging
from pathlib import Path
from typing import Dict, Any

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)


def backup_existing_files():
    """Create backup of existing files."""
    backup_dir = Path('backup_before_migration')
    backup_dir.mkdir(exist_ok=True)
    
    files_to_backup = [
        'setup_agent.py',
        'config.json',
        'chat_history.json',
        'command_history.json',
        'important_conversations.json'
    ]
    
    for file_name in files_to_backup:
        file_path = Path(file_name)
        if file_path.exists():
            backup_path = backup_dir / file_name
            shutil.copy2(file_path, backup_path)
            logger.info(f"📦 Backed up {file_name} to {backup_path}")
    
    logger.info(f"✅ Backup completed in {backup_dir}")


def create_env_file():
    """Create .env file from existing config.json."""
    config_path = Path('config.json')
    env_path = Path('.env')
    
    if env_path.exists():
        logger.info("📄 .env file already exists, skipping creation")
        return
    
    if not config_path.exists():
        logger.warning("⚠️  config.json not found, creating basic .env file")
        shutil.copy('.env.example', '.env')
        return
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        env_content = [
            "# SetupAgent Environment Configuration",
            "# Generated from config.json during migration",
            "",
            "# Ollama Configuration"
        ]
        
        # Extract Ollama settings
        ollama_config = config.get('ollama', {})
        env_content.extend([
            f"OLLAMA_BASE_URL={ollama_config.get('url', 'http://localhost:11434')}",
            f"OLLAMA_DEFAULT_MODEL={ollama_config.get('default_model', 'mistral')}",
            f"OLLAMA_TIMEOUT={ollama_config.get('timeout', 90)}"
        ])
        
        # Extract GPU settings
        gpu_config = config.get('gpu', {})
        env_content.extend([
            "",
            "# GPU Configuration",
            f"GPU_TARGET_DEVICE={gpu_config.get('target_gpu', 0)}",
            f"GPU_MEMORY_RESERVE_MB={gpu_config.get('memory_reserve_mb', 512)}",
            f"OLLAMA_GPU_LAYERS={gpu_config.get('gpu_layers', -1)}"
        ])
        
        # Extract logging settings
        logging_config = config.get('logging', {})
        env_content.extend([
            "",
            "# Logging Configuration",
            f"LOG_LEVEL={logging_config.get('level', 'INFO')}",
            f"LOG_FILE={logging_config.get('file', 'agent.log')}"
        ])
        
        # Extract memory settings
        memory_config = config.get('memory', {})
        if 'database_path' in memory_config:
            env_content.extend([
                "",
                "# Memory Configuration",
                f"MEMORY_DATABASE_PATH={memory_config['database_path']}"
            ])
        
        env_content.extend([
            "",
            "# Add your API keys here:",
            "# OPENAI_API_KEY=your_openai_api_key_here",
            "# GOOGLE_SEARCH_API_KEY=your_google_api_key_here",
            "# BING_SEARCH_API_KEY=your_bing_api_key_here",
            "",
            "# Security (generate with: python -c \"from cryptography.fernet import Fernet; print(Fernet.generate_key().decode())\")",
            "# ENCRYPTION_KEY=your_encryption_key_here"
        ])
        
        with open(env_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(env_content))
        
        logger.info(f"✅ Created .env file from config.json")
        
    except Exception as e:
        logger.error(f"❌ Failed to create .env file: {e}")
        logger.info("📄 Copying .env.example as fallback")
        shutil.copy('.env.example', '.env')


def update_imports_in_existing_scripts():
    """Update import statements in existing scripts."""
    scripts_to_update = [
        'demo_advanced_memory.py',
        'test_advanced_memory.py',
        'test_embeddings_integration.py',
        'test_enhanced_search.py'
    ]
    
    for script_name in scripts_to_update:
        script_path = Path(script_name)
        if not script_path.exists():
            continue
        
        try:
            with open(script_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Update imports
            updated_content = content.replace(
                'from setup_agent import',
                'from setup_agent.core.config import config\nfrom setup_agent.memory.manager import memory_manager\nfrom setup_agent.llm.factory import LLMProviderFactory\n# Legacy import:'
            )
            
            if updated_content != content:
                with open(script_path, 'w', encoding='utf-8') as f:
                    f.write(updated_content)
                logger.info(f"📝 Updated imports in {script_name}")
            
        except Exception as e:
            logger.error(f"❌ Failed to update {script_name}: {e}")


def create_new_main_script():
    """Create new main script using modular architecture."""
    main_script_content = '''#!/usr/bin/env python3
"""
🤖 SetupAgent - Modular LLM-Powered Intelligent Assistant
Main entry point using the new modular architecture.
"""

import sys
import logging
from pathlib import Path

# Add the current directory to Python path for imports
sys.path.insert(0, str(Path(__file__).parent))

from setup_agent.core.config import config
from setup_agent.memory.manager import memory_manager
from setup_agent.llm.factory import LLMProviderFactory
from setup_agent.core.exceptions import SetupAgentError

# Setup logging
log_level = config.get(['logging', 'level'], 'INFO')
log_file = config.get(['logging', 'file'], 'agent.log')

logging.basicConfig(
    level=getattr(logging, log_level.upper(), logging.INFO),
    format='%(asctime)s %(levelname)s %(name)s %(message)s',
    handlers=[
        logging.FileHandler(log_file, encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger("SetupAgent")


def main():
    """Main function for the modular SetupAgent."""
    print("🤖 SetupAgent v2.0 - Modular Architecture")
    print("=" * 50)
    
    try:
        # Initialize components
        logger.info("🚀 Initializing SetupAgent components...")
        
        # Show configuration info
        print(f"📋 Configuration loaded from: {config.get_all()}")
        print(f"🧠 Memory manager initialized")
        
        # Show available LLM providers
        providers = LLMProviderFactory.get_available_providers()
        if providers:
            print(f"🤖 Available LLM providers: {', '.join(providers)}")
            default_provider, default_model = LLMProviderFactory.auto_select_model()
            print(f"🎯 Default provider: {default_provider} ({default_model})")
        else:
            print("⚠️  No LLM providers available")
        
        # Show memory stats
        stats = memory_manager.get_memory_stats()
        print(f"💾 Memory: {stats['chat_messages']} chats, {stats['command_executions']} commands")
        
        print("\\nType 'help' for available commands, 'exit' to quit")
        print("=" * 50)
        
        # Simple CLI loop (you can expand this)
        while True:
            try:
                user_input = input("agent> ").strip()
                
                if not user_input:
                    continue
                
                if user_input.lower() in ['exit', 'quit']:
                    print("👋 Goodbye!")
                    break
                
                if user_input.lower() == 'help':
                    print_help()
                    continue
                
                if user_input.lower() == 'providers':
                    show_provider_info()
                    continue
                
                if user_input.lower() == 'stats':
                    show_stats()
                    continue
                
                # For now, just echo the input
                print(f"📝 You said: {user_input}")
                print("💡 This is the new modular architecture!")
                print("   Add your command processing logic here.")
                
                # Store in memory
                memory_manager.add_chat_message(user_input, "Modular response")
                
            except (EOFError, KeyboardInterrupt):
                print("\\n👋 Goodbye!")
                break
            except Exception as e:
                print(f"❌ Error: {e}")
                logger.error(f"Main loop error: {e}")
    
    except SetupAgentError as e:
        logger.error(f"SetupAgent error: {e}")
        print(f"❌ SetupAgent error: {e}")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        print(f"❌ Unexpected error: {e}")
        sys.exit(1)
    finally:
        # Save memory data
        try:
            memory_manager.save_all()
            logger.info("💾 Memory data saved")
        except Exception as e:
            logger.error(f"Failed to save memory data: {e}")


def print_help():
    """Print help information."""
    help_text = \"\"\"
🤖 SetupAgent v2.0 Commands:
    help        - Show this help
    providers   - Show LLM provider information  
    stats       - Show memory statistics
    exit/quit   - Exit the application
    
💡 This is the new modular architecture. You can extend this
   by adding more commands and integrating with the existing
   functionality from the original setup_agent.py
\"\"\"
    print(help_text)


def show_provider_info():
    """Show LLM provider information."""
    try:
        info = LLMProviderFactory.get_provider_info()
        print("\\n🤖 LLM Provider Information:")
        for name, details in info.items():
            status = "✅ Available" if details.get('available') else "❌ Not available"
            print(f"  {name}: {status}")
            if details.get('available'):
                print(f"    Default model: {details.get('default_model', 'Unknown')}")
                models = details.get('models', [])
                if models:
                    print(f"    Models: {', '.join(models[:3])}{'...' if len(models) > 3 else ''}")
    except Exception as e:
        print(f"❌ Error getting provider info: {e}")


def show_stats():
    """Show memory and system statistics."""
    try:
        stats = memory_manager.get_memory_stats()
        print("\\n📊 System Statistics:")
        print(f"  💬 Chat messages: {stats['chat_messages']}")
        print(f"  ⚡ Command executions: {stats['command_executions']}")
        print(f"  ⭐ Important conversations: {stats['important_conversations']}")
        
        embeddings_stats = stats.get('embeddings', {})
        if embeddings_stats.get('available'):
            print(f"  🧠 Embeddings: Available")
        else:
            print(f"  🧠 Embeddings: Not available")
    except Exception as e:
        print(f"❌ Error getting stats: {e}")


if __name__ == "__main__":
    main()
'''
    
    with open('setup_agent_modular.py', 'w', encoding='utf-8') as f:
        f.write(main_script_content)
    
    logger.info("✅ Created setup_agent_modular.py")


def main():
    """Main migration function."""
    print("🔄 SetupAgent Migration to Modular Architecture")
    print("=" * 50)
    
    try:
        # Step 1: Backup existing files
        print("\\n📦 Step 1: Creating backup...")
        backup_existing_files()
        
        # Step 2: Create .env file
        print("\\n🔧 Step 2: Creating environment configuration...")
        create_env_file()
        
        # Step 3: Update existing scripts
        print("\\n📝 Step 3: Updating existing scripts...")
        update_imports_in_existing_scripts()
        
        # Step 4: Create new main script
        print("\\n🚀 Step 4: Creating new main script...")
        create_new_main_script()
        
        print("\\n" + "=" * 50)
        print("✅ Migration completed successfully!")
        print("\\n📋 Next steps:")
        print("1. Review and update your .env file with API keys")
        print("2. Test the new modular architecture: python setup_agent_modular.py")
        print("3. Install new dependencies: pip install -r requirements.txt")
        print("4. Run tests: pytest tests/")
        print("\\n💡 Your original files are backed up in 'backup_before_migration/'")
        
    except Exception as e:
        logger.error(f"❌ Migration failed: {e}")
        print(f"\\n❌ Migration failed: {e}")
        print("💡 Check the logs and try again, or restore from backup")


if __name__ == "__main__":
    main()
