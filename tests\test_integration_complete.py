"""
Comprehensive integration tests for all SetupAgent modules.
"""

import pytest
import tempfile
import time
from pathlib import Path
from unittest.mock import patch, Mock

from setup_agent.core.config import config
from setup_agent.core.events import event_manager, EventType
from setup_agent.llm.factory import LL<PERSON><PERSON>iderFactory
from setup_agent.memory.manager import memory_manager
from setup_agent.search.web_search import WebSearchManager, SearchEngine
from setup_agent.commands.executor import CommandExecutor
from setup_agent.commands.safety import SafetyValidator, CommandSafetyLevel
from setup_agent.utils.validation import input_validator

# Optional monitoring imports
try:
    from setup_agent.utils.monitoring import PerformanceMonitor, MemoryMonitor
    has_monitoring = True
except ImportError:
    has_monitoring = False
    PerformanceMonitor = None
    MemoryMonitor = None


class TestCompleteIntegration:
    """Test complete system integration."""
    
    def setup_method(self):
        """Setup for each test."""
        # Use temporary directory for test files
        self.temp_dir = tempfile.mkdtemp()
        self.temp_path = Path(self.temp_dir)
        
        # Clear any existing state
        event_manager.clear_history()
        memory_manager.chat_history = []
        memory_manager.command_history = []
    
    def test_configuration_system(self):
        """Test configuration system with environment variables."""
        # Test basic configuration access
        assert config.get(['ollama', 'url']) is not None
        assert config.get(['logging', 'level']) is not None
        
        # Test setting and getting values
        config.set(['test', 'value'], 'test_data')
        assert config.get(['test', 'value']) == 'test_data'
        
        # Test development mode detection
        assert isinstance(config.is_development_mode(), bool)
    
    def test_llm_provider_factory(self):
        """Test LLM provider factory functionality."""
        # Test getting available providers
        providers = LLMProviderFactory.get_available_providers()
        assert isinstance(providers, list)
        
        # Test provider info
        info = LLMProviderFactory.get_provider_info()
        assert isinstance(info, dict)
        
        # Test auto-selection
        try:
            provider_name, model = LLMProviderFactory.auto_select_model()
            assert isinstance(provider_name, str)
            assert isinstance(model, str)
        except Exception:
            # No providers available in test environment
            pass
    
    def test_memory_management(self):
        """Test memory management system."""
        # Test adding chat message
        memory_manager.add_chat_message("Test question", "Test answer")
        assert len(memory_manager.chat_history) == 1
        
        # Test adding command execution
        memory_manager.add_command_execution("ls", "file1.txt", True)
        assert len(memory_manager.command_history) == 1
        
        # Test getting recent context
        context = memory_manager.get_recent_context(max_messages=1)
        assert "Test question" in context
        
        # Test memory stats
        stats = memory_manager.get_memory_stats()
        assert stats['chat_messages'] == 1
        assert stats['command_executions'] == 1
    
    def test_event_system(self):
        """Test observer pattern event system."""
        # Test event publishing
        event_manager.publish(
            EventType.SYSTEM_START,
            "test_source",
            {"test": "data"}
        )
        
        # Check event history
        history = event_manager.get_event_history()
        assert len(history) > 0
        assert history[-1].type == EventType.SYSTEM_START
        
        # Test event statistics
        stats = event_manager.get_stats()
        assert stats['events_published'] > 0
    
    def test_search_system(self):
        """Test web search functionality."""
        search_manager = WebSearchManager()
        
        # Test available engines
        engines = search_manager.get_available_engines()
        assert SearchEngine.DUCKDUCKGO in engines
        
        # Test search validation (without actual network call)
        with pytest.raises(Exception):  # Should raise SearchError for empty query
            search_manager.search("")
    
    def test_command_safety_system(self):
        """Test command safety validation."""
        safety_validator = SafetyValidator()
        
        # Test safe command
        result = safety_validator.validate_command("ls -la")
        assert result.allowed is True
        assert result.level in [CommandSafetyLevel.SAFE, CommandSafetyLevel.CAUTION]
        
        # Test dangerous command
        result = safety_validator.validate_command("rm -rf /")
        assert result.allowed is False
        assert result.level == CommandSafetyLevel.BLOCKED
        
        # Test safety summary
        summary = safety_validator.get_safety_summary()
        assert 'enabled' in summary
        assert 'blocked_patterns_count' in summary
    
    def test_command_execution_system(self):
        """Test command execution with safety."""
        executor = CommandExecutor()
        
        # Test command validation only
        validation = executor.validate_only("echo hello")
        assert 'command' in validation
        assert 'safety_level' in validation
        assert 'overall_allowed' in validation
        
        # Test execution statistics
        stats = executor.get_stats()
        assert 'total_executed' in stats
        assert 'success_rate' in stats
    
    def test_input_validation_system(self):
        """Test input validation and sanitization."""
        # Test text validation
        result = input_validator.validate_text_input("Hello world")
        assert result.valid is True
        assert result.sanitized_input == "Hello world"
        
        # Test dangerous input
        result = input_validator.validate_text_input("<script>alert('xss')</script>")
        assert len(result.warnings) > 0 or len(result.errors) > 0
        
        # Test file path validation
        result = input_validator.validate_file_path("./test.txt")
        assert result.valid is True
        
        # Test dangerous path
        result = input_validator.validate_file_path("../../../etc/passwd")
        assert result.valid is False or len(result.warnings) > 0
        
        # Test command validation
        result = input_validator.validate_command("echo hello")
        assert result.valid is True
        
        # Test JSON validation
        result = input_validator.validate_json_input({"key": "value"})
        assert result.valid is True
        assert result.sanitized_input == {"key": "value"}
    
    def test_performance_monitoring(self):
        """Test performance monitoring system."""
        if not has_monitoring:
            pytest.skip("Monitoring not available (psutil not installed)")

        monitor = PerformanceMonitor()

        # Test metric recording
        monitor.record_metric("test_metric", 1.5)
        stats = monitor.get_metric_stats("test_metric")
        assert stats is not None
        assert stats['count'] == 1
        assert stats['avg'] == 1.5

        # Test function call recording
        monitor.record_function_call("test_function", 0.1)
        func_stats = monitor.get_function_stats("test_function")
        assert func_stats['count'] == 1
        assert func_stats['avg_time'] == 0.1

    def test_memory_monitoring(self):
        """Test memory monitoring system."""
        if not has_monitoring:
            pytest.skip("Monitoring not available (psutil not installed)")

        memory_monitor = MemoryMonitor()

        # Test memory info
        info = memory_monitor.get_memory_info()
        assert 'rss_mb' in info
        assert 'percent' in info

        # Test trend analysis (with minimal data)
        trend = memory_monitor.get_memory_trend()
        assert 'trend' in trend
    
    def test_end_to_end_workflow(self):
        """Test complete end-to-end workflow."""
        # 1. Validate user input
        user_input = "What is Python?"
        validation_result = input_validator.validate_text_input(user_input)
        assert validation_result.valid
        
        # 2. Store in memory
        memory_manager.add_chat_message(user_input, "Python is a programming language")
        
        # 3. Publish event
        event_manager.publish(
            EventType.LLM_REQUEST_START,
            "integration_test",
            {"query": user_input}
        )
        
        # 4. Check memory stats
        stats = memory_manager.get_memory_stats()
        assert stats['chat_messages'] > 0
        
        # 5. Check event history
        history = event_manager.get_event_history(EventType.LLM_REQUEST_START)
        assert len(history) > 0
        
        # 6. Validate command (if needed)
        command_validation = input_validator.validate_command("ls")
        assert command_validation.valid
    
    def test_error_handling_and_recovery(self):
        """Test error handling across modules."""
        # Test invalid configuration access
        result = config.get(['nonexistent', 'key'], 'default')
        assert result == 'default'
        
        # Test invalid memory operations
        try:
            memory_manager.search_history("")  # Empty query
            # Should not crash
        except Exception:
            pass
        
        # Test invalid event publishing
        try:
            event_manager.publish(None, "test", {})  # Invalid event type
        except Exception:
            pass  # Expected to fail gracefully
    
    def test_configuration_integration(self):
        """Test configuration integration across modules."""
        # Test that modules respect configuration
        assert isinstance(config.get(['logging', 'level'], 'INFO'), str)
        assert isinstance(config.get(['security', 'strict_mode'], False), bool)
        assert isinstance(config.get(['commands', 'execution_enabled'], True), bool)
    
    def test_lazy_loading_behavior(self):
        """Test lazy loading functionality."""
        from setup_agent.memory.lazy_embeddings import lazy_embeddings
        
        # Test embeddings availability check
        available = lazy_embeddings.is_available()
        assert isinstance(available, bool)
        
        # Test stats retrieval
        stats = lazy_embeddings.get_stats()
        assert isinstance(stats, dict)
        assert 'available' in stats


class TestModuleInteroperability:
    """Test how modules work together."""
    
    def test_memory_and_events_integration(self):
        """Test memory system with event publishing."""
        # Clear previous state
        event_manager.clear_history()
        
        # Add memory item (should trigger events if integrated)
        memory_manager.add_chat_message("Integration test", "Response")
        
        # Check if events were published (if integration is implemented)
        history = event_manager.get_event_history()
        # Note: This might be 0 if memory-event integration isn't implemented yet
        assert isinstance(history, list)
    
    def test_command_and_validation_integration(self):
        """Test command execution with validation."""
        executor = CommandExecutor()
        
        # Test that executor uses validation
        validation = executor.validate_only("echo test")
        assert 'safety_level' in validation
        assert 'whitelist_allowed' in validation
    
    def test_search_and_cache_integration(self):
        """Test search with caching."""
        search_manager = WebSearchManager()
        
        # Test cache functionality
        search_manager.clear_cache()
        # Cache should be empty after clearing
        assert True  # Basic test that clear_cache doesn't crash


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
