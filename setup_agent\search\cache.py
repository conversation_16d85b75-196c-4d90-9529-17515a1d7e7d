"""
Search cache with T<PERSON> and LRU eviction.
"""

import time
import logging
from typing import Dict, Any, List, Optional, Tuple
from collections import OrderedDict
from dataclasses import dataclass

from ..core.config import config

logger = logging.getLogger(__name__)


@dataclass
class CacheEntry:
    """Cache entry with TTL support."""
    data: Any
    timestamp: float
    access_count: int = 0
    last_access: float = 0


class SearchCache:
    """LRU cache with TTL for search results."""
    
    def __init__(self):
        self.max_size = config.get(['search', 'cache_max_size'], 1000)
        self.ttl_seconds = config.get(['search', 'cache_ttl_seconds'], 3600)  # 1 hour
        self.cache: OrderedDict[str, CacheEntry] = OrderedDict()
        
    def get(self, query: str, engine: Optional[Any] = None) -> Optional[List[Any]]:
        """Get cached search results."""
        cache_key = self._make_key(query, engine)
        
        if cache_key not in self.cache:
            return None
        
        entry = self.cache[cache_key]
        
        # Check TTL
        if self._is_expired(entry):
            del self.cache[cache_key]
            logger.debug(f"Cache entry expired for key: {cache_key}")
            return None
        
        # Update access info
        entry.access_count += 1
        entry.last_access = time.time()
        
        # Move to end (most recently used)
        self.cache.move_to_end(cache_key)
        
        logger.debug(f"Cache hit for key: {cache_key}")
        return entry.data
    
    def set(self, query: str, engine: Optional[Any], results: List[Any]) -> None:
        """Cache search results."""
        cache_key = self._make_key(query, engine)
        current_time = time.time()
        
        # Create cache entry
        entry = CacheEntry(
            data=results,
            timestamp=current_time,
            access_count=1,
            last_access=current_time
        )
        
        # Add to cache
        self.cache[cache_key] = entry
        
        # Move to end (most recently used)
        self.cache.move_to_end(cache_key)
        
        # Evict if necessary
        self._evict_if_needed()
        
        logger.debug(f"Cached {len(results)} results for key: {cache_key}")
    
    def clear(self) -> None:
        """Clear all cache entries."""
        self.cache.clear()
        logger.info("Search cache cleared")
    
    def cleanup_expired(self) -> int:
        """Remove expired entries and return count removed."""
        expired_keys = []
        
        for key, entry in self.cache.items():
            if self._is_expired(entry):
                expired_keys.append(key)
        
        for key in expired_keys:
            del self.cache[key]
        
        if expired_keys:
            logger.info(f"Cleaned up {len(expired_keys)} expired cache entries")
        
        return len(expired_keys)
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        total_entries = len(self.cache)
        expired_count = 0
        total_access_count = 0
        
        for entry in self.cache.values():
            if self._is_expired(entry):
                expired_count += 1
            total_access_count += entry.access_count
        
        return {
            'total_entries': total_entries,
            'expired_entries': expired_count,
            'valid_entries': total_entries - expired_count,
            'max_size': self.max_size,
            'ttl_seconds': self.ttl_seconds,
            'total_access_count': total_access_count,
            'average_access_count': total_access_count / total_entries if total_entries > 0 else 0
        }
    
    def _make_key(self, query: str, engine: Optional[Any]) -> str:
        """Create cache key from query and engine."""
        engine_str = engine.value if hasattr(engine, 'value') else str(engine) if engine else 'default'
        return f"{engine_str}:{query.lower().strip()}"
    
    def _is_expired(self, entry: CacheEntry) -> bool:
        """Check if cache entry is expired."""
        return (time.time() - entry.timestamp) > self.ttl_seconds
    
    def _evict_if_needed(self) -> None:
        """Evict entries if cache is full."""
        while len(self.cache) > self.max_size:
            # Remove least recently used (first item)
            oldest_key = next(iter(self.cache))
            del self.cache[oldest_key]
            logger.debug(f"Evicted cache entry: {oldest_key}")


# Global cache instance
search_cache = SearchCache()
