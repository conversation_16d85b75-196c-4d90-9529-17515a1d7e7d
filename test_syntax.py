#!/usr/bin/env python3

# Quick syntax test for setup_agent
import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(__file__))

try:
    import setup_agent
    print("✅ setup_agent.py imports successfully!")
    
    # Test basic functions
    if hasattr(setup_agent, 'print_help'):
        setup_agent.print_help()
    else:
        print("❌ print_help function not found")
        
except SyntaxError as e:
    print(f"❌ Syntax Error: {e}")
    print(f"File: {e.filename}, Line: {e.lineno}")
except ImportError as e:
    print(f"❌ Import Error: {e}")
except Exception as e:
    print(f"❌ Other Error: {e}")
