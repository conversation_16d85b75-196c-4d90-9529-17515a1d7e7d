#!/usr/bin/env python3
"""
🧪 Test script for Mistral integration with SetupAgent
Tests Mistral model configuration, prompt formatting, and response quality.
"""

import sys
import os
import json
import time

# Add the current directory to the path
sys.path.insert(0, os.path.dirname(__file__))

def test_mistral_availability():
    """Test if Mistral model is available in Ollama."""
    print("🔍 Testing Mistral availability...")
    
    try:
        import requests
        
        # Check Ollama connection
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        response.raise_for_status()
        
        models = response.json().get('models', [])
        model_names = [model.get('name', '').split(':')[0] for model in models]
        
        if 'mistral' in model_names:
            print("✅ Mistral model is available in Ollama")
            
            # Get model details
            for model in models:
                if 'mistral' in model.get('name', '').lower():
                    print(f"   Model: {model.get('name', 'Unknown')}")
                    print(f"   Size: {model.get('size', 'Unknown')} bytes")
                    print(f"   Modified: {model.get('modified_at', 'Unknown')}")
                    break
        else:
            print("⚠️  Mistral model not found in Ollama")
            print(f"   Available models: {model_names}")
            print("   To install Mistral, run: ollama pull mistral")
            
    except requests.exceptions.ConnectionError:
        print("❌ Could not connect to Ollama (is it running?)")
        print("   Start Ollama with: ollama serve")
    except Exception as e:
        print(f"❌ Error checking Ollama: {e}")

def test_mistral_configuration():
    """Test Mistral-specific configuration."""
    print("\n⚙️  Testing Mistral configuration...")
    
    try:
        with open('config.json', 'r') as f:
            config = json.load(f)
        
        ollama_config = config.get('ollama', {})
        
        # Check default model
        default_model = ollama_config.get('default_model', '')
        if 'mistral' in default_model.lower():
            print(f"✅ Default model set to: {default_model}")
        else:
            print(f"⚠️  Default model is: {default_model} (not Mistral)")
        
        # Check Mistral-specific settings
        mistral_config = ollama_config.get('mistral_specific', {})
        if mistral_config:
            print("✅ Mistral-specific configuration found:")
            for key, value in mistral_config.items():
                print(f"   {key}: {value}")
        else:
            print("⚠️  No Mistral-specific configuration found")
        
        # Check context size
        num_ctx = ollama_config.get('options', {}).get('num_ctx', 4096)
        print(f"✅ Context size: {num_ctx} tokens")
        
        # Check timeout settings
        timeout = ollama_config.get('timeout', 60)
        print(f"✅ Timeout: {timeout} seconds")
        
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")

def test_mistral_prompt_formatting():
    """Test Mistral prompt formatting."""
    print("\n📝 Testing Mistral prompt formatting...")
    
    try:
        from setup_agent import call_ollama, CONFIG
        
        # Test basic prompt
        test_prompt = "What is Python?"
        
        # Check if Mistral formatting is applied
        ollama_config = CONFIG.get('ollama', {})
        mistral_config = ollama_config.get('mistral_specific', {})
        
        if mistral_config.get('use_chat_template', True):
            expected_format = f"<s>[INST] {test_prompt} [/INST]"
            print(f"✅ Mistral instruction format enabled")
            print(f"   Original: {test_prompt}")
            print(f"   Formatted: {expected_format}")
        else:
            print("⚠️  Mistral instruction format disabled")
        
    except Exception as e:
        print(f"❌ Prompt formatting test failed: {e}")

def test_mistral_response():
    """Test Mistral response quality."""
    print("\n🤖 Testing Mistral response...")
    
    try:
        from setup_agent import call_ollama
        
        # Test with a simple question
        test_prompt = "Explain what Python is in one sentence."
        
        print(f"   Prompt: {test_prompt}")
        print("   Generating response...")
        
        start_time = time.time()
        response = call_ollama(test_prompt, model="mistral")
        end_time = time.time()
        
        if response and not response.startswith("Error"):
            print(f"✅ Response generated successfully")
            print(f"   Response time: {end_time - start_time:.2f} seconds")
            print(f"   Response length: {len(response)} characters")
            print(f"   Response preview: {response[:100]}...")
        else:
            print(f"❌ Failed to generate response: {response}")
            
    except Exception as e:
        print(f"❌ Response test failed: {e}")

def test_mistral_conversation():
    """Test Mistral conversation handling."""
    print("\n💬 Testing Mistral conversation...")
    
    try:
        from setup_agent import handle_conversation
        
        # Test conversation with context
        test_input = "How do I install Python packages?"
        
        print(f"   Input: {test_input}")
        print("   Processing conversation...")
        
        start_time = time.time()
        response = handle_conversation(test_input, model="mistral")
        end_time = time.time()
        
        if response and not response.startswith("Error"):
            print(f"✅ Conversation handled successfully")
            print(f"   Response time: {end_time - start_time:.2f} seconds")
            print(f"   Response length: {len(response)} characters")
            print(f"   Response preview: {response[:150]}...")
        else:
            print(f"❌ Conversation failed: {response}")
            
    except Exception as e:
        print(f"❌ Conversation test failed: {e}")

def test_mistral_with_embeddings():
    """Test Mistral with embeddings context."""
    print("\n🧠 Testing Mistral with embeddings...")
    
    try:
        import setup_agent
        
        if hasattr(setup_agent, 'has_embeddings') and setup_agent.has_embeddings:
            print("✅ Embeddings available")
            
            # Test conversation with embeddings
            test_input = "What's the best way to debug Python code?"
            
            print(f"   Input: {test_input}")
            print("   Processing with embeddings context...")
            
            response = setup_agent.handle_conversation(test_input, model="mistral")
            
            if response:
                print(f"✅ Mistral + embeddings working")
                print(f"   Response length: {len(response)} characters")
                
                # Check if embeddings context was used
                if "Similar Past" in response or "📚" in response:
                    print("✅ Embeddings context detected in response")
                else:
                    print("ℹ️  No embeddings context in this response")
            else:
                print("❌ No response generated")
        else:
            print("⚠️  Embeddings not available")
            
    except Exception as e:
        print(f"⚠️  Embeddings test failed: {e}")

def test_mistral_performance():
    """Test Mistral performance metrics."""
    print("\n⚡ Testing Mistral performance...")
    
    try:
        from setup_agent import call_ollama
        
        # Test multiple requests to measure consistency
        test_prompts = [
            "What is machine learning?",
            "How do I use Git?",
            "Explain Docker containers.",
            "What are Python decorators?",
            "How to optimize code performance?"
        ]
        
        response_times = []
        successful_responses = 0
        
        for i, prompt in enumerate(test_prompts, 1):
            print(f"   Test {i}/5: {prompt[:30]}...")
            
            start_time = time.time()
            response = call_ollama(prompt, model="mistral")
            end_time = time.time()
            
            response_time = end_time - start_time
            response_times.append(response_time)
            
            if response and not response.startswith("Error"):
                successful_responses += 1
                print(f"     ✅ Success ({response_time:.2f}s)")
            else:
                print(f"     ❌ Failed ({response_time:.2f}s)")
        
        # Calculate statistics
        if response_times:
            avg_time = sum(response_times) / len(response_times)
            min_time = min(response_times)
            max_time = max(response_times)
            
            print(f"\n📊 Performance Summary:")
            print(f"   Successful responses: {successful_responses}/{len(test_prompts)}")
            print(f"   Average response time: {avg_time:.2f} seconds")
            print(f"   Fastest response: {min_time:.2f} seconds")
            print(f"   Slowest response: {max_time:.2f} seconds")
            
            if avg_time < 10:
                print("✅ Good performance")
            elif avg_time < 20:
                print("⚠️  Moderate performance")
            else:
                print("❌ Slow performance")
        
    except Exception as e:
        print(f"❌ Performance test failed: {e}")

def main():
    """Run all Mistral integration tests."""
    print("🤖 Mistral Integration Test Suite for SetupAgent")
    print("=" * 60)
    
    test_mistral_availability()
    test_mistral_configuration()
    test_mistral_prompt_formatting()
    test_mistral_response()
    test_mistral_conversation()
    test_mistral_with_embeddings()
    test_mistral_performance()
    
    print("\n" + "=" * 60)
    print("🎉 Mistral integration test completed!")
    print("\n💡 Tips for optimal Mistral usage:")
    print("   • Use clear, specific prompts")
    print("   • Leverage the instruction format for better responses")
    print("   • Monitor response times and adjust context size if needed")
    print("   • Use embeddings for enhanced context awareness")
    print("   • Consider GPU acceleration for faster inference")

if __name__ == "__main__":
    main()
