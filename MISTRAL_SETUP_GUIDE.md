# 🤖 Mistral Setup Guide for SetupAgent

## 📋 **Overview**

SetupAgent is now optimized to work with Mistral, a powerful open-source language model. This guide will help you set up and configure Mistral for optimal performance with your SetupAgent.

## 🚀 **Quick Setup**

### 1. **Install Ollama**
```bash
# Download and install Ollama from https://ollama.ai
# Or use package manager (if available)
```

### 2. **Install Mistral Model**
```bash
# Pull the Mistral model
ollama pull mistral

# Verify installation
ollama list
```

### 3. **Start Ollama Service**
```bash
# Start Ollama server
ollama serve

# Verify it's running
curl http://localhost:11434/api/tags
```

### 4. **Configure SetupAgent**
Your `config.json` is already configured for Mistral! Key settings:

```json
{
  "ollama": {
    "default_model": "mistral",
    "num_ctx": 8192,
    "timeout": 90,
    "mistral_specific": {
      "use_chat_template": true,
      "max_tokens": 2048
    }
  }
}
```

## ⚙️ **Configuration Details**

### **Mistral-Optimized Settings**

| Setting | Value | Purpose |
|---------|-------|---------|
| `num_ctx` | 8192 | Larger context window for Mistral |
| `timeout` | 90 | Longer timeout for complex responses |
| `temperature` | 0.7 | Balanced creativity/consistency |
| `use_chat_template` | true | Uses Mistral's instruction format |

### **Instruction Format**

SetupAgent automatically formats prompts for Mistral using the instruction template:

```
Original: "How do I install Python?"
Formatted: "<s>[INST] How do I install Python? [/INST]"
```

This format helps Mistral understand that it's receiving an instruction and should provide a helpful response.

## 🎯 **Usage Examples**

### **Basic Conversation**
```bash
python setup_agent.py
> How do I set up a Python development environment?
```

### **Command Execution**
```bash
> search python virtual environments
> help
> analytics
```

### **Advanced Features**
```bash
> search_ai machine learning setup
> chat  # Enter interactive chat mode
```

## 🔧 **Performance Optimization**

### **GPU Acceleration**
If you have a compatible GPU:

```json
{
  "gpu": {
    "target_gpu": 0,
    "gpu_layers": -1,
    "memory_reserve_mb": 512
  }
}
```

### **Memory Management**
For systems with limited RAM:

```json
{
  "ollama": {
    "options": {
      "num_ctx": 4096,
      "num_batch": 256
    }
  }
}
```

### **Response Speed**
For faster responses:

```json
{
  "ollama": {
    "options": {
      "temperature": 0.5,
      "top_p": 0.8,
      "num_thread": 8
    }
  }
}
```

## 🧠 **Mistral + Embeddings**

SetupAgent's embeddings system works seamlessly with Mistral:

1. **Automatic Context**: Past conversations are automatically included
2. **Command Memory**: Previous command executions are referenced
3. **Project Awareness**: Current working directory context

Example enhanced response:
```
You: "How do I debug Python code?"

Mistral: "📚 Based on our previous conversation about Python development...
🔧 I see you previously used 'python -m pdb script.py'...

Here are the best debugging approaches for Python..."
```

## 🎨 **Customization**

### **Prompt Templates**
Customize how SetupAgent formats prompts for Mistral:

```python
# In setup_agent.py, modify the system_prompt
system_prompt = """You are SetupAgent, an expert assistant for:
- Development environment setup
- Programming guidance  
- System administration
- Troubleshooting

Respond concisely and practically."""
```

### **Response Style**
Adjust Mistral's response style:

```json
{
  "ollama": {
    "options": {
      "temperature": 0.3,  // More focused responses
      "top_p": 0.7,        // More deterministic
      "repeat_penalty": 1.2 // Reduce repetition
    }
  }
}
```

## 🔍 **Troubleshooting**

### **Common Issues**

1. **Mistral Not Found**
   ```bash
   # Install Mistral
   ollama pull mistral
   
   # Check available models
   ollama list
   ```

2. **Slow Responses**
   ```json
   // Reduce context size
   {"num_ctx": 4096}
   
   // Increase threads
   {"num_thread": 8}
   ```

3. **Connection Errors**
   ```bash
   # Check if Ollama is running
   ollama serve
   
   # Test connection
   curl http://localhost:11434/api/tags
   ```

4. **Memory Issues**
   ```json
   // Reduce batch size
   {"num_batch": 256}
   
   // Lower context window
   {"num_ctx": 2048}
   ```

### **Debug Mode**
Enable detailed logging:

```python
import logging
logging.getLogger('setup_agent').setLevel(logging.DEBUG)
```

## 📊 **Performance Monitoring**

### **Response Time Tracking**
SetupAgent automatically tracks:
- Average response time
- Success rate
- Context usage
- Memory consumption

### **Usage Analytics**
View usage statistics:
```bash
> analytics
> command_freq
```

## 🔄 **Model Switching**

### **Temporary Model Change**
```bash
> set_model llama2  # Switch to different model
> set_model mistral # Switch back to Mistral
```

### **Permanent Model Change**
Update `config.json`:
```json
{
  "ollama": {
    "default_model": "your_preferred_model"
  }
}
```

## 🎯 **Best Practices**

### **Prompt Engineering**
1. **Be Specific**: "Install Python 3.11 on Ubuntu" vs "Install Python"
2. **Provide Context**: Include relevant details about your system
3. **Use Examples**: Show what you've tried or what you expect

### **Conversation Flow**
1. **Start Simple**: Begin with basic questions
2. **Build Context**: Let the conversation develop naturally
3. **Reference History**: Mistral can reference previous interactions

### **Performance Tips**
1. **Monitor Resources**: Keep an eye on CPU/GPU usage
2. **Adjust Settings**: Tune parameters based on your hardware
3. **Use Embeddings**: Enable for better context awareness

## 🔮 **Advanced Features**

### **Streaming Responses**
For real-time responses:
```bash
> llm_stream "Explain machine learning concepts"
```

### **Batch Processing**
Process multiple queries efficiently:
```python
queries = ["Setup Docker", "Configure Git", "Install Node.js"]
for query in queries:
    response = handle_conversation(query, model="mistral")
```

### **Custom Plugins**
Extend functionality with custom plugins:
```bash
> generate_plugin mistral_helper
> load_plugin mistral_helper
```

## 📈 **Performance Benchmarks**

Typical performance on modern hardware:

| Hardware | Response Time | Context Size | Quality |
|----------|---------------|--------------|---------|
| CPU Only | 5-15 seconds | 4096 tokens | Good |
| GPU (8GB) | 2-5 seconds | 8192 tokens | Excellent |
| GPU (16GB+) | 1-3 seconds | 16384 tokens | Outstanding |

## 🎉 **Getting Started**

1. **Test Installation**:
   ```bash
   python test_mistral_integration.py
   ```

2. **Start SetupAgent**:
   ```bash
   python setup_agent.py
   ```

3. **Try Basic Commands**:
   ```
   > help
   > How do I set up a development environment?
   > search python best practices
   ```

4. **Explore Features**:
   ```
   > chat  # Interactive mode
   > analytics  # Usage statistics
   > embeddings  # Memory features
   ```

Enjoy using Mistral with SetupAgent! 🚀
