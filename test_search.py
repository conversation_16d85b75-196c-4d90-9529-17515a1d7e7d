#!/usr/bin/env python3
"""
Test script for Setup Agent's web search capabilities
"""

import sys
import os

# Add the current directory to path to import setup_agent
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from setup_agent import perform_web_search, format_search_results, search_enhanced_conversation

def test_basic_search():
    """Test basic web search functionality."""
    print("🔍 Testing basic web search...")
    print("=" * 50)
    
    query = "Python 3.12 new features"
    results = perform_web_search(query, max_results=3)
    formatted = format_search_results(results)
    
    print(f"Query: {query}")
    print("\nResults:")
    print(formatted)

def test_search_commands():
    """Show available search commands."""
    print("\n🤖 Setup Agent Web Search Commands:")
    print("=" * 50)
    print("1. search <query>     - Basic web search")
    print("2. search_ai <query>  - AI-enhanced search with analysis")
    print("3. Natural conversation with automatic search triggers")
    print("\nExample usage:")
    print("  agent> search Python 3.12 features")
    print("  agent> search_ai latest AI developments 2025")
    print("  agent> what are the latest Python updates?")
    print("\nThe agent automatically searches when you ask about:")
    print("  • Current/latest information")
    print("  • Recent news or updates")
    print("  • 2024/2025 specific information")
    print("  • Version information")

if __name__ == "__main__":
    print("🌐 Setup Agent Web Search Test")
    print("=" * 50)
    
    test_search_commands()
    
    # Only run actual search test if requests is available
    try:
        import requests
        test_basic_search()
        print("\n✅ Web search functionality is working!")
    except ImportError:
        print("\n⚠️  Requests library not available for testing actual search.")
        print("Install with: pip install requests")
    
    print("\n🎉 Setup Agent now has internet search capabilities!")
    print("Start the agent with: python setup_agent.py")
