"""
Tests for configuration management.
"""

import os
import pytest
from pathlib import Path
from unittest.mock import patch

from setup_agent.core.config import Config


class TestConfig:
    """Test configuration management."""
    
    def test_singleton_pattern(self):
        """Test that Config follows singleton pattern."""
        config1 = Config()
        config2 = Config()
        assert config1 is config2
    
    def test_default_config(self):
        """Test default configuration values."""
        # Clear singleton to test fresh config
        Config._instance = None

        # Mock no config file to test defaults
        with patch('pathlib.Path.exists', return_value=False):
            config = Config()

            # Test basic structure
            assert 'ollama' in config._config
            assert 'openai' in config._config
            assert 'gpu' in config._config
            assert 'memory' in config._config

            # Test default values
            assert config.get(['ollama', 'url']) == "http://localhost:11434"
            assert config.get(['ollama', 'default_model']) == "mistral"
            assert config.get(['gpu', 'target_gpu']) == 0

        # Reset singleton
        Config._instance = None
    
    def test_get_method(self):
        """Test configuration get method."""
        config = Config()
        
        # Test nested path
        assert config.get(['ollama', 'url']) is not None
        
        # Test string path
        assert config.get('ollama') is not None
        
        # Test default value
        assert config.get(['nonexistent', 'key'], 'default') == 'default'
        assert config.get('nonexistent', 'default') == 'default'
    
    def test_set_method(self):
        """Test configuration set method."""
        config = Config()
        
        # Test setting nested value
        config.set(['test', 'nested', 'value'], 'test_data')
        assert config.get(['test', 'nested', 'value']) == 'test_data'
        
        # Test setting string path
        config.set('test_key', 'test_value')
        assert config.get('test_key') == 'test_value'
    
    def test_environment_override(self):
        """Test environment variable overrides."""
        # Set environment variable
        os.environ['OLLAMA_DEFAULT_MODEL'] = 'test_model'
        
        # Create new config instance (clear singleton)
        Config._instance = None
        config = Config()
        
        # Check if environment variable was applied
        assert config.get(['ollama', 'default_model']) == 'test_model'
        
        # Cleanup
        del os.environ['OLLAMA_DEFAULT_MODEL']
        Config._instance = None
    
    def test_development_mode(self):
        """Test development mode detection."""
        config = Config()
        
        # Test default (should be False)
        assert config.is_development_mode() is False
        
        # Test with environment variable
        os.environ['DEVELOPMENT_MODE'] = 'true'
        assert config.is_development_mode() is True
        
        # Cleanup
        del os.environ['DEVELOPMENT_MODE']
    
    def test_get_all_hides_sensitive(self):
        """Test that get_all() hides sensitive values."""
        config = Config()
        config.set(['openai', 'api_key'], 'secret_key')
        config.set(['security', 'encryption_key'], 'secret_encryption')
        
        all_config = config.get_all()
        
        # Check that sensitive values are hidden
        assert all_config['openai']['api_key'] == "***HIDDEN***"
        assert all_config['security']['encryption_key'] == "***HIDDEN***"
