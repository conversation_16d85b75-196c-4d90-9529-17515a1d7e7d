"""
Custom exceptions for SetupAgent.
"""


class SetupAgentError(Exception):
    """Base exception for Setup Agent errors."""
    pass


class ConfigurationError(SetupAgentError):
    """Raised when there's a configuration issue."""
    pass


class EmbeddingsError(SetupAgentError):
    """Raised when there's an embeddings-related error."""
    pass


class SearchError(SetupAgentError):
    """Raised when there's a search-related error."""
    pass


class AgentMemoryError(SetupAgentError):
    """Raised when there's a memory management error."""
    pass


class OllamaError(SetupAgentError):
    """Raised when there's an Ollama API error."""
    pass


class LLMProviderError(SetupAgentError):
    """Raised when there's an LLM provider error."""
    pass


class CommandExecutionError(SetupAgentError):
    """Raised when there's a command execution error."""
    pass


class SecurityError(SetupAgentError):
    """Raised when there's a security-related error."""
    pass


class PluginError(SetupAgentError):
    """Raised when there's a plugin-related error."""
    pass
