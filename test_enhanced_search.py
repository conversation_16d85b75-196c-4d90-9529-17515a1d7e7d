#!/usr/bin/env python3
"""
Setup Agent - Enhanced Search Functionality Test
This is a cleaned up version to test the search features.
"""

import os
import json
import requests
import urllib.parse
from typing import Dict, Any, List, Optional
from datetime import datetime

# Configuration
SEARCH_CACHE_FILE = 'search_cache.json'
SEARCH_HISTORY_FILE = 'search_history.json'
SEARCH_FAVORITES_FILE = 'search_favorites.json'
SEARCH_CACHE_DURATION = 3600  # 1 hour

# Global search data
search_cache = {}
search_history = []
search_favorites = []

def load_search_data():
    """Load search cache, history, and favorites from files."""
    global search_cache, search_history, search_favorites
    
    # Load cache
    try:
        if os.path.exists(SEARCH_CACHE_FILE):
            with open(SEARCH_CACHE_FILE, 'r', encoding='utf-8') as f:
                search_cache = json.load(f)
    except Exception as e:
        print(f"Failed to load search cache: {e}")
        search_cache = {}
    
    # Load history
    try:
        if os.path.exists(SEARCH_HISTORY_FILE):
            with open(SEARCH_HISTORY_FILE, 'r', encoding='utf-8') as f:
                search_history = json.load(f)
    except Exception as e:
        print(f"Failed to load search history: {e}")
        search_history = []
    
    # Load favorites
    try:
        if os.path.exists(SEARCH_FAVORITES_FILE):
            with open(SEARCH_FAVORITES_FILE, 'r', encoding='utf-8') as f:
                search_favorites = json.load(f)
    except Exception as e:
        print(f"Failed to load search favorites: {e}")
        search_favorites = []

def save_search_data():
    """Save search cache, history, and favorites to files."""
    # Save cache
    try:
        with open(SEARCH_CACHE_FILE, 'w', encoding='utf-8') as f:
            json.dump(search_cache, f, indent=2)
    except Exception as e:
        print(f"Failed to save search cache: {e}")
    
    # Save history
    try:
        with open(SEARCH_HISTORY_FILE, 'w', encoding='utf-8') as f:
            json.dump(search_history[-100:], f, indent=2)  # Keep last 100 searches
    except Exception as e:
        print(f"Failed to save search history: {e}")
    
    # Save favorites
    try:
        with open(SEARCH_FAVORITES_FILE, 'w', encoding='utf-8') as f:
            json.dump(search_favorites, f, indent=2)
    except Exception as e:
        print(f"Failed to save search favorites: {e}")

def search_duckduckgo(query: str, max_results: int = 5) -> List[Dict[str, str]]:
    """Search DuckDuckGo and return results."""
    try:
        # DuckDuckGo instant answer API
        encoded_query = urllib.parse.quote(query)
        url = f"https://api.duckduckgo.com/?q={encoded_query}&format=json&no_html=1&skip_disambig=1"
        
        response = requests.get(url, timeout=10)
        response.raise_for_status()
        data = response.json()
        
        results = []
        
        # Add instant answer if available
        if data.get('Abstract'):
            results.append({
                "title": data.get('AbstractText', 'DuckDuckGo Summary'),
                "snippet": data.get('Abstract', ''),
                "url": data.get('AbstractURL', ''),
                "source": "DuckDuckGo Instant Answer"
            })
        
        # Add related topics
        for topic in data.get('RelatedTopics', [])[:max_results-1]:
            if isinstance(topic, dict) and 'Text' in topic:
                results.append({
                    "title": topic.get('Text', '')[:100] + "...",
                    "snippet": topic.get('Text', ''),
                    "url": topic.get('FirstURL', ''),
                    "source": "DuckDuckGo Related"
                })
        
        return results[:max_results] if results else [{"error": "No results found"}]
        
    except Exception as e:
        return [{"error": f"Search error: {e}"}]

def perform_web_search(query: str, max_results: int = 5) -> List[Dict[str, str]]:
    """Perform web search using DuckDuckGo."""
    print(f"🔍 Searching the web for: {query}")
    
    results = search_duckduckgo(query, max_results)
    
    # Add to search history if successful
    if results and not (len(results) == 1 and "error" in results[0]):
        search_entry = {
            "timestamp": datetime.now().isoformat(),
            "query": query,
            "results_count": len(results),
            "engine": "duckduckgo"
        }
        search_history.append(search_entry)
        
        # Keep only recent searches
        if len(search_history) > 100:
            search_history = search_history[-100:]
    
    return results

def format_search_results(results: List[Dict[str, str]]) -> str:
    """Format search results for display."""
    if not results:
        return "❌ No search results found."
    
    if len(results) == 1 and "error" in results[0]:
        return f"❌ Search error: {results[0]['error']}"
    
    formatted = "🔍 **Search Results:**\n\n"
    
    for i, result in enumerate(results, 1):
        if "error" in result:
            continue
            
        title = result.get('title', 'No title')
        snippet = result.get('snippet', 'No description')
        url = result.get('url', '')
        source = result.get('source', 'Web')
        
        formatted += f"**{i}. {title}**\n"
        if snippet:
            formatted += f"   {snippet[:200]}{'...' if len(snippet) > 200 else ''}\n"
        if url:
            formatted += f"   🔗 {url}\n"
        formatted += f"   📄 Source: {source}\n\n"
    
    return formatted

def show_search_history(limit: int = 10):
    """Show recent search history."""
    if not search_history:
        print("📜 No search history found.")
        return
    
    print(f"📜 Recent Search History (last {limit}):")
    print("=" * 50)
    
    for entry in search_history[-limit:]:
        timestamp = datetime.fromisoformat(entry['timestamp']).strftime("%Y-%m-%d %H:%M")
        query = entry['query']
        results_count = entry['results_count']
        engine = entry['engine']
        
        print(f"🕒 {timestamp}")
        print(f"🔍 Query: {query}")
        print(f"📊 Results: {results_count} (via {engine})")
        print("-" * 30)

def add_search_favorite(query: str, name: Optional[str] = None, description: Optional[str] = None):
    """Add a search query to favorites."""
    if name is None:
        name = query[:50] + "..." if len(query) > 50 else query
    
    favorite = {
        "name": name,
        "query": query,
        "timestamp": datetime.now().isoformat(),
        "description": description
    }
    
    search_favorites.append(favorite)
    save_search_data()
    print(f"⭐ Added '{name}' to search favorites.")

def show_search_favorites():
    """Show saved search favorites."""
    if not search_favorites:
        print("⭐ No search favorites found.")
        return
    
    print("⭐ Search Favorites:")
    print("=" * 40)
    
    for i, favorite in enumerate(search_favorites, 1):
        print(f"{i}. {favorite['name']}")
        print(f"   Query: {favorite['query']}")
        print(f"   Added: {datetime.fromisoformat(favorite['timestamp']).strftime('%Y-%m-%d %H:%M')}")
        if favorite.get('description'):
            print(f"   Description: {favorite['description']}")
        print("-" * 30)

def test_search_functionality():
    """Test the search functionality."""
    print("🧪 Testing Enhanced Search Functionality")
    print("=" * 50)
    
    # Load existing data
    load_search_data()
    
    # Test search
    query = "Python programming"
    results = perform_web_search(query, 3)
    formatted = format_search_results(results)
    print(formatted)
    
    # Test favorites
    add_search_favorite(query, "Python Programming Search")
    
    # Show history
    show_search_history(5)
    
    # Show favorites
    show_search_favorites()
    
    # Save data
    save_search_data()
    
    print("\n✅ Search functionality test completed!")

if __name__ == "__main__":
    test_search_functionality()
