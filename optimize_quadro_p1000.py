#!/usr/bin/env python3
"""
Quadro P1000 4GB VRAM Optimization Script for SetupAgent
Configures Ollama and system settings to use full GPU capacity.
"""

import os
import json
import subprocess
import time
from pathlib import Path

# Hardware specifications
QUADRO_P1000_VRAM = 4096  # MB
SYSTEM_RAM = 24576  # MB (24GB)
CPU_CORES = 8  # i7-7700 with hyperthreading

def set_ollama_environment():
    """Set optimal Ollama environment variables for Quadro P1000."""
    print("🔧 Configuring Ollama environment for Quadro P1000...")
    
    # Optimal environment variables for 4GB VRAM usage
    env_vars = {
        'OLLAMA_GPU_OVERHEAD': '100',  # Minimal overhead
        'OLLAMA_MAX_LOADED_MODELS': '1',  # Load one model at a time
        'OLLAMA_NUM_PARALLEL': '4',  # <PERSON><PERSON><PERSON> requests
        'OLLAMA_FLASH_ATTENTION': 'true',  # Enable flash attention
        'OLLAMA_KV_CACHE_TYPE': 'f16',  # Use f16 for KV cache
        'CUDA_VISIBLE_DEVICES': '0',  # Use only Quadro P1000
        'OLLAMA_CONTEXT_LENGTH': '8192',  # Large context
        'OLLAMA_BATCH_SIZE': '1024',  # Large batch size
        'OLLAMA_THREADS': '8',  # Use all CPU threads
    }
    
    # Create .env file for persistent settings
    env_file = Path('.env')
    with open(env_file, 'w') as f:
        f.write("# Quadro P1000 4GB Optimization Settings\n")
        for key, value in env_vars.items():
            f.write(f"{key}={value}\n")
            os.environ[key] = str(value)
    
    print("✅ Environment variables configured")
    return env_vars

def create_model_priority_config():
    """Create model priority configuration for 4GB VRAM."""
    print("🎯 Creating model priority configuration...")
    
    # Models optimized for 4GB VRAM (in order of preference)
    model_config = {
        "full_vram_models": {
            "mistral:latest": {
                "vram_usage": 4100,  # Uses almost all VRAM
                "quality": "excellent",
                "use_case": "general_purpose",
                "context_length": 8192,
                "batch_size": 1024
            },
            "phi4-mini-reasoning:3.8b": {
                "vram_usage": 3800,  # Leaves room for context
                "quality": "excellent",
                "use_case": "reasoning_tasks",
                "context_length": 6144,
                "batch_size": 768
            },
            "gemma3:4b": {
                "vram_usage": 3900,  # Good balance
                "quality": "very_good",
                "use_case": "balanced_performance",
                "context_length": 6144,
                "batch_size": 768
            }
        },
        "efficient_models": {
            "llama3.2:3b": {
                "vram_usage": 2000,  # Conservative usage
                "quality": "good",
                "use_case": "fast_inference",
                "context_length": 4096,
                "batch_size": 512
            },
            "qwen2.5:3b": {
                "vram_usage": 2100,  # Conservative usage
                "quality": "good",
                "use_case": "multilingual",
                "context_length": 4096,
                "batch_size": 512
            }
        }
    }
    
    with open('model_priority.json', 'w') as f:
        json.dump(model_config, f, indent=2)
    
    print("✅ Model priority configuration created")
    return model_config

def optimize_system_settings():
    """Optimize Windows system settings for GPU inference."""
    print("⚙️ Optimizing system settings...")
    
    try:
        # Set high performance power plan
        subprocess.run([
            'powercfg', '/setactive', 'SCHEME_MIN'
        ], check=True, capture_output=True)
        print("✅ Set high performance power plan")
    except subprocess.CalledProcessError:
        print("⚠️ Could not set power plan (requires admin)")
    
    try:
        # Disable GPU power management
        subprocess.run([
            'nvidia-smi', '-pm', '1'
        ], check=True, capture_output=True)
        print("✅ Disabled GPU power management")
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("⚠️ Could not configure GPU power management")
    
    print("✅ System optimization complete")

def test_full_vram_usage():
    """Test models with full VRAM usage."""
    print("\n🧪 Testing Full VRAM Usage...")
    
    # Test models that can use most of the 4GB
    test_models = ["mistral:latest", "phi4-mini-reasoning:3.8b", "gemma3:4b"]
    
    for model in test_models:
        print(f"\n🔄 Testing {model} with aggressive GPU settings...")
        
        # Test with large context and batch size
        test_config = {
            "model": model,
            "prompt": "Explain how neural networks work in detail. Include mathematical concepts.",
            "stream": False,
            "options": {
                "num_ctx": 8192,
                "num_batch": 1024,
                "num_gpu": -1,  # Use all GPU layers
                "main_gpu": 0,
                "low_vram": False,
                "temperature": 0.7
            }
        }
        
        try:
            import requests
            start_time = time.time()
            response = requests.post(
                "http://localhost:11434/api/generate",
                json=test_config,
                timeout=120
            )
            end_time = time.time()
            
            if response.status_code == 200:
                result = response.json()
                response_time = end_time - start_time
                tokens = len(result.get("response", "").split())
                
                print(f"✅ {model} - Success!")
                print(f"   • Response time: {response_time:.2f}s")
                print(f"   • Tokens generated: {tokens}")
                print(f"   • Speed: {tokens/response_time:.1f} tokens/sec")
                print(f"   • VRAM utilization: Maximum")
                
                return model  # Return first successful model
            else:
                print(f"❌ {model} - HTTP {response.status_code}")
                
        except Exception as e:
            print(f"❌ {model} - Error: {e}")
    
    return None

def create_gpu_monitoring_script():
    """Create a GPU monitoring script."""
    print("📊 Creating GPU monitoring script...")
    
    monitoring_script = '''#!/usr/bin/env python3
"""
Real-time GPU monitoring for SetupAgent on Quadro P1000
"""

import time
import subprocess
import json

def get_gpu_stats():
    """Get current GPU statistics."""
    try:
        result = subprocess.run([
            'nvidia-smi', '--query-gpu=memory.used,memory.total,utilization.gpu,temperature.gpu',
            '--format=csv,noheader,nounits'
        ], capture_output=True, text=True, check=True)
        
        memory_used, memory_total, gpu_util, temp = result.stdout.strip().split(', ')
        
        return {
            'memory_used_mb': int(memory_used),
            'memory_total_mb': int(memory_total),
            'memory_usage_percent': round((int(memory_used) / int(memory_total)) * 100, 1),
            'gpu_utilization_percent': int(gpu_util),
            'temperature_c': int(temp)
        }
    except Exception as e:
        return {'error': str(e)}

def monitor_gpu(duration_seconds=60):
    """Monitor GPU for specified duration."""
    print(f"🔍 Monitoring Quadro P1000 for {duration_seconds} seconds...")
    print("Time     | VRAM Used | VRAM % | GPU % | Temp°C")
    print("-" * 50)
    
    for i in range(duration_seconds):
        stats = get_gpu_stats()
        if 'error' not in stats:
            print(f"{i:3d}s     | {stats['memory_used_mb']:4d}MB   | {stats['memory_usage_percent']:5.1f}% | {stats['gpu_utilization_percent']:3d}%  | {stats['temperature_c']:3d}°C")
        else:
            print(f"{i:3d}s     | Error: {stats['error']}")
        
        time.sleep(1)

if __name__ == "__main__":
    monitor_gpu()
'''
    
    with open('monitor_gpu.py', 'w', encoding='utf-8') as f:
        f.write(monitoring_script)
    
    print("✅ GPU monitoring script created: monitor_gpu.py")

def main():
    """Main optimization function."""
    print("🎯 Quadro P1000 4GB VRAM Optimization for SetupAgent")
    print("=" * 60)
    print(f"Hardware Detected:")
    print(f"  • GPU: NVIDIA Quadro P1000 (4GB VRAM)")
    print(f"  • CPU: Intel i7-7700 (8 threads)")
    print(f"  • RAM: 24GB")
    print("=" * 60)
    
    # Step 1: Set environment variables
    env_vars = set_ollama_environment()
    
    # Step 2: Create model configuration
    model_config = create_model_priority_config()
    
    # Step 3: Optimize system settings
    optimize_system_settings()
    
    # Step 4: Create monitoring tools
    create_gpu_monitoring_script()
    
    print("\n🎯 OPTIMIZATION COMPLETE!")
    print("=" * 40)
    print("✅ Environment configured for 4GB VRAM usage")
    print("✅ Model priorities set for maximum performance")
    print("✅ System settings optimized")
    print("✅ GPU monitoring tools created")
    
    print(f"\n💡 RECOMMENDATIONS:")
    print(f"1. Use 'mistral:latest' for maximum quality (uses ~4.1GB VRAM)")
    print(f"2. Use 'phi4-mini-reasoning:3.8b' for reasoning tasks")
    print(f"3. Run 'python monitor_gpu.py' to watch VRAM usage")
    print(f"4. Restart Ollama to apply new settings")
    
    # Ask to test
    test_choice = input(f"\n🧪 Test full VRAM usage now? (y/n): ").lower().strip()
    if test_choice == 'y':
        optimal_model = test_full_vram_usage()
        if optimal_model:
            print(f"\n🏆 Optimal model for your setup: {optimal_model}")
        else:
            print(f"\n⚠️ No models responded. Check Ollama server status.")

if __name__ == "__main__":
    main()