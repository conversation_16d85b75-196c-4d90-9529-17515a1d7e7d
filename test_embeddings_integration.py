#!/usr/bin/env python3
"""
🧪 Test script for SetupAgent embeddings integration
Tests the complete embeddings workflow with different models and vector stores.
"""

import sys
import os
import json
import time

# Add the current directory to the path
sys.path.insert(0, os.path.dirname(__file__))

def test_embeddings_config():
    """Test embeddings configuration loading."""
    print("🔧 Testing embeddings configuration...")
    
    try:
        with open('config.json', 'r') as f:
            config = json.load(f)
        
        embeddings_config = config.get('embeddings', {})
        
        if embeddings_config.get('enabled'):
            print("✅ Embeddings enabled in config")
            print(f"   Backend: {embeddings_config.get('backend', 'not set')}")
            print(f"   Model: {embeddings_config.get('model', 'not set')}")
            print(f"   Vector Store: {embeddings_config.get('vector_store', 'not set')}")
        else:
            print("⚠️  Embeddings not enabled in config")
            
    except Exception as e:
        print(f"❌ Config test failed: {e}")

def test_ollama_embeddings():
    """Test Ollama embeddings functionality."""
    print("\n🧠 Testing Ollama embeddings...")
    
    try:
        from embeddings import OllamaEmbeddings
        
        # Test connection
        ollama_embeddings = OllamaEmbeddings(model_name='nomic-embed-text')
        
        # Test embedding generation
        test_text = "How do I install Python packages?"
        embedding = ollama_embeddings.get_embedding(test_text)
        
        if embedding:
            print(f"✅ Generated embedding of dimension {len(embedding)}")
            print(f"   Sample values: {embedding[:5]}...")
        else:
            print("⚠️  No embedding generated (model may not be available)")
            
    except ImportError:
        print("❌ Could not import OllamaEmbeddings")
    except Exception as e:
        print(f"⚠️  Ollama embeddings test failed: {e}")

def test_sentence_transformers():
    """Test sentence-transformers fallback."""
    print("\n📦 Testing sentence-transformers...")
    
    try:
        from embeddings import SentenceTransformersEmbeddings
        
        st_embeddings = SentenceTransformersEmbeddings()
        
        test_text = "How do I set up a development environment?"
        embedding = st_embeddings.get_embedding(test_text)
        
        if embedding:
            print(f"✅ Generated embedding of dimension {len(embedding)}")
        else:
            print("⚠️  No embedding generated")
            
    except ImportError:
        print("❌ sentence-transformers not available")
    except Exception as e:
        print(f"⚠️  Sentence transformers test failed: {e}")

def test_vector_stores():
    """Test different vector store implementations."""
    print("\n🗄️  Testing vector stores...")
    
    # Test FAISS
    try:
        from embeddings import FAISSVectorStore
        
        faiss_store = FAISSVectorStore("test_faiss_index")
        
        # Test adding a vector
        test_embedding = [0.1, 0.2, 0.3] * 256  # 768 dimensions
        test_metadata = {"text": "test document", "type": "test"}
        
        faiss_store.add_vector("test_id", test_embedding, test_metadata)
        print("✅ FAISS vector store working")
        
        # Test search
        results = faiss_store.search_similar(test_embedding, max_results=1)
        if results:
            print(f"✅ FAISS search working: {len(results)} results")
        
    except Exception as e:
        print(f"⚠️  FAISS test failed: {e}")
    
    # Test SQLite
    try:
        from embeddings import SQLiteVectorStore
        
        sqlite_store = SQLiteVectorStore("test_sqlite.db")
        
        # Test adding a vector
        test_embedding = [0.1, 0.2, 0.3] * 256
        test_metadata = {"text": "test document", "type": "test"}
        
        sqlite_store.add_vector("test_id", test_embedding, test_metadata)
        print("✅ SQLite vector store working")
        
        # Test search
        results = sqlite_store.search_similar(test_embedding, max_results=1)
        if results:
            print(f"✅ SQLite search working: {len(results)} results")
        
    except Exception as e:
        print(f"⚠️  SQLite test failed: {e}")

def test_embedding_manager():
    """Test the main EmbeddingManager class."""
    print("\n🎯 Testing EmbeddingManager...")
    
    try:
        from embeddings import EmbeddingManager
        
        # Initialize with test config
        test_config = {
            'embeddings': {
                'backend': 'sentence_transformers',
                'model': 'all-MiniLM-L6-v2',
                'vector_store': 'sqlite'
            }
        }
        manager = EmbeddingManager(test_config)
        
        print("✅ EmbeddingManager initialized")
        
        # Test storing an interaction
        manager.store_interaction(
            prompt="How do I install packages?",
            response="You can use pip install package_name",
            metadata={"type": "test", "project": "test_project"}
        )
        print("✅ Interaction stored")
        
        # Test finding similar interactions
        similar = manager.find_similar_interactions("package installation", max_results=1)
        if similar:
            print(f"✅ Found {len(similar)} similar interactions")
            print(f"   Example: {similar[0].get('prompt', '')[:50]}...")
        
        # Test command storage
        manager.store_command_execution(
            command="pip install requests",
            output="Successfully installed requests",
            success=True,
            metadata={"type": "test"}
        )
        print("✅ Command execution stored")
        
        # Test finding similar commands
        similar_cmds = manager.find_similar_commands("install requests", max_results=1)
        if similar_cmds:
            print(f"✅ Found {len(similar_cmds)} similar commands")
        
        # Test context generation
        context = manager.get_context_for_query("python package management")
        if context:
            print("✅ Context generation working")
            print(f"   Context length: {len(context)} characters")
        
    except Exception as e:
        print(f"❌ EmbeddingManager test failed: {e}")

def test_agent_integration():
    """Test integration with the main agent."""
    print("\n🤖 Testing agent integration...")
    
    try:
        import setup_agent
        
        # Check if embeddings are enabled
        if hasattr(setup_agent, 'has_embeddings') and setup_agent.has_embeddings:
            print("✅ Embeddings enabled in agent")
            
            if hasattr(setup_agent, 'embedding_manager') and setup_agent.embedding_manager:
                print("✅ EmbeddingManager available in agent")
                
                # Test conversation with embeddings
                test_input = "How do I debug Python code?"
                response = setup_agent.handle_conversation(test_input)
                
                if response:
                    print("✅ Conversation with embeddings working")
                    print(f"   Response length: {len(response)} characters")
                
            else:
                print("⚠️  EmbeddingManager not initialized in agent")
        else:
            print("⚠️  Embeddings not enabled in agent")
            
    except Exception as e:
        print(f"⚠️  Agent integration test failed: {e}")

def test_performance():
    """Test embeddings performance."""
    print("\n⚡ Testing performance...")
    
    try:
        from embeddings import EmbeddingManager
        
        test_config = {
            'embeddings': {
                'backend': 'sentence_transformers',
                'vector_store': 'sqlite'
            }
        }
        manager = EmbeddingManager(test_config)
        
        # Test embedding generation speed
        test_texts = [
            "How do I install Python?",
            "What is machine learning?",
            "How to debug code?",
            "Setting up development environment",
            "Package management with pip"
        ]
        
        start_time = time.time()
        
        for i, text in enumerate(test_texts):
            manager.store_interaction(
                prompt=text,
                response=f"Response to question {i+1}",
                metadata={"type": "performance_test"}
            )
        
        end_time = time.time()
        
        print(f"✅ Stored {len(test_texts)} interactions in {end_time - start_time:.2f} seconds")
        print(f"   Average: {(end_time - start_time) / len(test_texts):.2f} seconds per interaction")
        
        # Test search speed
        start_time = time.time()
        results = manager.find_similar_interactions("python development", max_results=3)
        end_time = time.time()
        
        print(f"✅ Search completed in {end_time - start_time:.2f} seconds")
        print(f"   Found {len(results)} results")
        
    except Exception as e:
        print(f"⚠️  Performance test failed: {e}")

def cleanup_test_files():
    """Clean up test files."""
    print("\n🧹 Cleaning up test files...")
    
    test_files = [
        "test_faiss_index",
        "test_faiss_index.metadata",
        "test_sqlite.db"
    ]
    
    for file in test_files:
        try:
            if os.path.exists(file):
                os.remove(file)
                print(f"   Removed {file}")
        except Exception as e:
            print(f"   Could not remove {file}: {e}")

def main():
    """Run all embeddings tests."""
    print("🧪 SetupAgent Embeddings Integration Test Suite")
    print("=" * 60)
    
    test_embeddings_config()
    test_ollama_embeddings()
    test_sentence_transformers()
    test_vector_stores()
    test_embedding_manager()
    test_agent_integration()
    test_performance()
    
    cleanup_test_files()
    
    print("\n" + "=" * 60)
    print("🎉 Embeddings integration test completed!")
    print("\n💡 Next steps:")
    print("   1. Install nomic-embed-text: ollama pull nomic-embed-text")
    print("   2. Enable embeddings in config.json")
    print("   3. Start using the agent to build up context")
    print("   4. Monitor performance and adjust settings as needed")

if __name__ == "__main__":
    main()
