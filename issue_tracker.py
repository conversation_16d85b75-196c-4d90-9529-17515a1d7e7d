#!/usr/bin/env python3
"""
🔧 SetupAgent Issue Tracker and Resolution Manager

This script helps track the progress of issue resolution and provides
automated checks for various system components.
"""

import json
import os
import sys
import subprocess
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path
from dataclasses import dataclass, asdict
from enum import Enum

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class IssueStatus(Enum):
    """Issue status enumeration."""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    RESOLVED = "resolved"
    VERIFIED = "verified"
    BLOCKED = "blocked"


class IssuePriority(Enum):
    """Issue priority enumeration."""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"


class IssueCategory(Enum):
    """Issue category enumeration."""
    CRITICAL_BUG = "critical_bug"
    SERVICE_DEPENDENCY = "service_dependency"
    IMPROVEMENT = "improvement"
    DOCUMENTATION = "documentation"
    PERFORMANCE = "performance"
    SECURITY = "security"


@dataclass
class Issue:
    """Issue data structure."""
    id: str
    title: str
    description: str
    category: IssueCategory
    priority: IssuePriority
    status: IssueStatus
    created_date: str
    resolved_date: Optional[str] = None
    verified_date: Optional[str] = None
    files_affected: Optional[List[str]] = None
    resolution_notes: str = ""
    verification_steps: Optional[List[str]] = None
    
    def __post_init__(self):
        if self.files_affected is None:
            self.files_affected = []
        if self.verification_steps is None:
            self.verification_steps = []


class IssueTracker:
    """Main issue tracking and resolution manager."""
    
    def __init__(self, data_file: str = "issue_tracker_data.json"):
        self.data_file = Path(data_file)
        self.issues: Dict[str, Issue] = {}
        self.load_data()
        self._initialize_known_issues()
    
    def _initialize_known_issues(self):
        """Initialize known issues if data file doesn't exist."""
        if not self.issues:
            self._add_critical_issues()
            self._add_service_dependencies()
            self._add_improvements()
            self.save_data()
    
    def _add_critical_issues(self):
        """Add known critical issues."""
        critical_issues = [
            {
                "id": "ISSUE-001",
                "title": "Syntax Error in migrate_to_modular.py",
                "description": "Unterminated triple-quoted string literal at line 378",
                "category": IssueCategory.CRITICAL_BUG,
                "priority": IssuePriority.CRITICAL,
                "status": IssueStatus.VERIFIED,
                "files_affected": ["migrate_to_modular.py"],
                "resolution_notes": "Removed stray ''' at end of file",
                "verification_steps": ["python -m py_compile migrate_to_modular.py"],
                "resolved_date": "2025-01-07",
                "verified_date": "2025-01-07"
            },
            {
                "id": "ISSUE-002",
                "title": "Circular Import in advanced_memory.py",
                "description": "Circular dependency between advanced_memory and setup_agent modules",
                "category": IssueCategory.CRITICAL_BUG,
                "priority": IssuePriority.CRITICAL,
                "status": IssueStatus.VERIFIED,
                "files_affected": ["advanced_memory.py"],
                "resolution_notes": "Updated to use modular setup_agent.search.web_search.search_manager",
                "verification_steps": ["python -c \"import advanced_memory; print('✅ Import successful')\""],
                "resolved_date": "2025-01-07",
                "verified_date": "2025-01-07"
            },
            {
                "id": "ISSUE-003",
                "title": "Memory Manager Configuration Bug",
                "description": "AdvancedMemorySystem receiving string instead of config dict",
                "category": IssueCategory.CRITICAL_BUG,
                "priority": IssuePriority.CRITICAL,
                "status": IssueStatus.VERIFIED,
                "files_affected": ["setup_agent/memory/manager.py"],
                "resolution_notes": "Fixed config dictionary construction with all required sections",
                "verification_steps": [
                    "python -c \"from setup_agent.memory.manager import memory_manager; print('✅ Memory manager working')\"",
                    "python setup_agent_modular.py --test"
                ],
                "resolved_date": "2025-01-07",
                "verified_date": "2025-01-07"
            },
            {
                "id": "ISSUE-004",
                "title": "Variable Name Inconsistency in Web Search",
                "description": "_HAS_REQUESTS defined but HAS_REQUESTS used",
                "category": IssueCategory.CRITICAL_BUG,
                "priority": IssuePriority.MEDIUM,
                "status": IssueStatus.VERIFIED,
                "files_affected": ["setup_agent/search/web_search.py"],
                "resolution_notes": "Updated all references to use consistent _HAS_REQUESTS naming",
                "verification_steps": ["python -c \"from setup_agent.search.web_search import search_manager; print('✅ Web search working')\""],
                "resolved_date": "2025-01-07",
                "verified_date": "2025-01-07"
            }
        ]
        
        for issue_data in critical_issues:
            issue = Issue(
                created_date="2025-01-07",
                **issue_data
            )
            self.issues[issue.id] = issue
    
    def _add_service_dependencies(self):
        """Add service dependency issues."""
        service_deps = [
            {
                "id": "DEP-001",
                "title": "Ollama Service Not Running",
                "description": "Connection refused to localhost:11434 - LLM features unavailable",
                "category": IssueCategory.SERVICE_DEPENDENCY,
                "priority": IssuePriority.HIGH,
                "status": IssueStatus.PENDING,
                "verification_steps": [
                    "curl http://localhost:11434/api/tags",
                    "python setup_agent_modular.py --test"
                ]
            },
            {
                "id": "DEP-002",
                "title": "GPU FAISS Acceleration Not Available",
                "description": "GPU acceleration not available for vector search - using CPU fallback",
                "category": IssueCategory.SERVICE_DEPENDENCY,
                "priority": IssuePriority.MEDIUM,
                "status": IssueStatus.PENDING,
                "verification_steps": [
                    "python -c \"import faiss; print(f'FAISS GPU support: {faiss.get_num_gpus() > 0}')\""
                ]
            }
        ]
        
        for issue_data in service_deps:
            issue = Issue(
                created_date="2025-01-07",
                **issue_data
            )
            self.issues[issue.id] = issue
    
    def _add_improvements(self):
        """Add improvement suggestions."""
        improvements = [
            {
                "id": "IMP-001",
                "title": "Add Configuration Validation",
                "description": "Implement configuration schema validation to prevent startup errors",
                "category": IssueCategory.IMPROVEMENT,
                "priority": IssuePriority.HIGH,
                "status": IssueStatus.PENDING,
                "files_affected": ["setup_agent/core/config.py", "setup_agent/core/config_schema.py"]
            },
            {
                "id": "IMP-002",
                "title": "Implement Unit Tests",
                "description": "Create comprehensive unit test suite for all modules",
                "category": IssueCategory.IMPROVEMENT,
                "priority": IssuePriority.HIGH,
                "status": IssueStatus.PENDING,
                "files_affected": ["tests/"]
            },
            {
                "id": "IMP-003",
                "title": "Add Performance Monitoring",
                "description": "Implement performance monitoring and metrics collection",
                "category": IssueCategory.PERFORMANCE,
                "priority": IssuePriority.MEDIUM,
                "status": IssueStatus.PENDING,
                "files_affected": ["setup_agent/utils/performance.py"]
            },
            {
                "id": "IMP-004",
                "title": "Create API Documentation",
                "description": "Generate comprehensive API documentation using Sphinx",
                "category": IssueCategory.DOCUMENTATION,
                "priority": IssuePriority.MEDIUM,
                "status": IssueStatus.PENDING,
                "files_affected": ["docs/"]
            },
            {
                "id": "IMP-005",
                "title": "Add Connection Pooling",
                "description": "Implement HTTP connection pooling for better performance",
                "category": IssueCategory.PERFORMANCE,
                "priority": IssuePriority.LOW,
                "status": IssueStatus.PENDING
            },
            {
                "id": "IMP-006",
                "title": "Implement Caching System",
                "description": "Add intelligent caching for frequently accessed data",
                "category": IssueCategory.PERFORMANCE,
                "priority": IssuePriority.LOW,
                "status": IssueStatus.PENDING
            },
            {
                "id": "IMP-007",
                "title": "Add Configuration Migration",
                "description": "Implement configuration migration system for version updates",
                "category": IssueCategory.IMPROVEMENT,
                "priority": IssuePriority.LOW,
                "status": IssueStatus.PENDING
            },
            {
                "id": "IMP-008",
                "title": "Create Troubleshooting Guide",
                "description": "Create comprehensive troubleshooting documentation",
                "category": IssueCategory.DOCUMENTATION,
                "priority": IssuePriority.MEDIUM,
                "status": IssueStatus.PENDING
            }
        ]
        
        for issue_data in improvements:
            issue = Issue(
                created_date="2025-01-07",
                **issue_data
            )
            self.issues[issue.id] = issue
    
    def load_data(self):
        """Load issue data from JSON file."""
        if self.data_file.exists():
            try:
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                for issue_id, issue_data in data.items():
                    # Convert enum strings back to enums
                    issue_data['category'] = IssueCategory(issue_data['category'])
                    issue_data['priority'] = IssuePriority(issue_data['priority'])
                    issue_data['status'] = IssueStatus(issue_data['status'])
                    
                    self.issues[issue_id] = Issue(**issue_data)
                
                logger.info(f"📊 Loaded {len(self.issues)} issues from {self.data_file}")
            except Exception as e:
                logger.error(f"Failed to load issue data: {e}")
    
    def save_data(self):
        """Save issue data to JSON file."""
        try:
            data = {}
            for issue_id, issue in self.issues.items():
                issue_dict = asdict(issue)
                # Convert enums to strings for JSON serialization
                issue_dict['category'] = issue.category.value
                issue_dict['priority'] = issue.priority.value
                issue_dict['status'] = issue.status.value
                data[issue_id] = issue_dict
            
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"💾 Saved issue data to {self.data_file}")
        except Exception as e:
            logger.error(f"Failed to save issue data: {e}")
    
    def get_issues_by_status(self, status: IssueStatus) -> List[Issue]:
        """Get issues filtered by status."""
        return [issue for issue in self.issues.values() if issue.status == status]
    
    def get_issues_by_category(self, category: IssueCategory) -> List[Issue]:
        """Get issues filtered by category."""
        return [issue for issue in self.issues.values() if issue.category == category]
    
    def get_issues_by_priority(self, priority: IssuePriority) -> List[Issue]:
        """Get issues filtered by priority."""
        return [issue for issue in self.issues.values() if issue.priority == priority]
    
    def update_issue_status(self, issue_id: str, status: IssueStatus, notes: str = ""):
        """Update issue status."""
        if issue_id not in self.issues:
            logger.error(f"Issue {issue_id} not found")
            return False
        
        issue = self.issues[issue_id]
        old_status = issue.status
        issue.status = status
        
        current_date = datetime.now().strftime("%Y-%m-%d")
        
        if status == IssueStatus.RESOLVED and not issue.resolved_date:
            issue.resolved_date = current_date
        elif status == IssueStatus.VERIFIED and not issue.verified_date:
            issue.verified_date = current_date
        
        if notes:
            issue.resolution_notes = notes
        
        logger.info(f"📝 Updated {issue_id}: {old_status.value} → {status.value}")
        self.save_data()
        return True
    
    def run_verification_checks(self, issue_id: str) -> bool:
        """Run verification checks for an issue."""
        if issue_id not in self.issues:
            logger.error(f"Issue {issue_id} not found")
            return False
        
        issue = self.issues[issue_id]
        if not issue.verification_steps:
            logger.warning(f"No verification steps defined for {issue_id}")
            return True
        
        logger.info(f"🧪 Running verification checks for {issue_id}")
        
        all_passed = True
        for i, step in enumerate(issue.verification_steps, 1):
            logger.info(f"  Step {i}: {step}")
            try:
                if step.startswith("python "):
                    result = subprocess.run(step, shell=True, capture_output=True, text=True, timeout=30)
                    if result.returncode == 0:
                        logger.info(f"    ✅ Passed")
                    else:
                        logger.error(f"    ❌ Failed: {result.stderr}")
                        all_passed = False
                elif step.startswith("curl "):
                    result = subprocess.run(step, shell=True, capture_output=True, text=True, timeout=10)
                    if result.returncode == 0:
                        logger.info(f"    ✅ Passed")
                    else:
                        logger.warning(f"    ⚠️ Service not available: {result.stderr}")
                        # Don't mark as failed for service checks
                else:
                    logger.info(f"    ℹ️ Manual verification required")
            except subprocess.TimeoutExpired:
                logger.error(f"    ❌ Timeout")
                all_passed = False
            except Exception as e:
                logger.error(f"    ❌ Error: {e}")
                all_passed = False
        
        if all_passed and issue.status == IssueStatus.RESOLVED:
            self.update_issue_status(issue_id, IssueStatus.VERIFIED)
        
        return all_passed
    
    def generate_progress_report(self) -> str:
        """Generate a progress report."""
        report = []
        report.append("🔧 SETUPAGENT ISSUE RESOLUTION PROGRESS REPORT")
        report.append("=" * 60)
        report.append(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("")
        
        # Summary by category
        categories = {}
        for issue in self.issues.values():
            cat = issue.category.value
            if cat not in categories:
                categories[cat] = {"total": 0, "resolved": 0, "verified": 0}
            categories[cat]["total"] += 1
            if issue.status in [IssueStatus.RESOLVED, IssueStatus.VERIFIED]:
                categories[cat]["resolved"] += 1
            if issue.status == IssueStatus.VERIFIED:
                categories[cat]["verified"] += 1
        
        report.append("📊 PROGRESS BY CATEGORY:")
        for cat, stats in categories.items():
            total = stats["total"]
            resolved = stats["resolved"]
            verified = stats["verified"]
            percentage = (resolved / total * 100) if total > 0 else 0
            status_icon = "✅" if percentage == 100 else "⏳" if percentage > 0 else "❌"
            report.append(f"  {status_icon} {cat.replace('_', ' ').title()}: {resolved}/{total} ({percentage:.0f}%)")
        
        report.append("")
        
        # Issues by status
        for status in IssueStatus:
            issues = self.get_issues_by_status(status)
            if issues:
                status_icon = {"pending": "⏳", "in_progress": "🔄", "resolved": "✅", "verified": "✅", "blocked": "🚫"}
                report.append(f"{status_icon.get(status.value, '❓')} {status.value.upper().replace('_', ' ')} ({len(issues)}):")
                for issue in sorted(issues, key=lambda x: x.priority.value):
                    priority_icon = {"critical": "🚨", "high": "⚠️", "medium": "📋", "low": "💡"}
                    report.append(f"  {priority_icon.get(issue.priority.value, '❓')} {issue.id}: {issue.title}")
                report.append("")
        
        # Overall progress
        total_issues = len(self.issues)
        resolved_issues = len([i for i in self.issues.values() if i.status in [IssueStatus.RESOLVED, IssueStatus.VERIFIED]])
        overall_percentage = (resolved_issues / total_issues * 100) if total_issues > 0 else 0
        
        report.append("🎯 OVERALL PROGRESS:")
        report.append(f"  Total Issues: {total_issues}")
        report.append(f"  Resolved: {resolved_issues}")
        report.append(f"  Progress: {overall_percentage:.1f}%")
        
        if overall_percentage == 100:
            report.append("  🎉 ALL ISSUES RESOLVED!")
        elif overall_percentage >= 75:
            report.append("  🚀 Excellent progress!")
        elif overall_percentage >= 50:
            report.append("  👍 Good progress!")
        else:
            report.append("  💪 Keep going!")
        
        return "\n".join(report)
    
    def show_dashboard(self):
        """Show interactive dashboard."""
        print(self.generate_progress_report())
    
    def run_all_verifications(self):
        """Run verification checks for all resolved issues."""
        resolved_issues = self.get_issues_by_status(IssueStatus.RESOLVED)
        
        if not resolved_issues:
            logger.info("No resolved issues to verify")
            return
        
        logger.info(f"🧪 Running verification for {len(resolved_issues)} resolved issues")
        
        for issue in resolved_issues:
            logger.info(f"\n--- Verifying {issue.id}: {issue.title} ---")
            self.run_verification_checks(issue.id)


def main():
    """Main function."""
    import argparse
    
    parser = argparse.ArgumentParser(description="SetupAgent Issue Tracker")
    parser.add_argument('--dashboard', action='store_true', help='Show progress dashboard')
    parser.add_argument('--verify', help='Verify specific issue by ID')
    parser.add_argument('--verify-all', action='store_true', help='Verify all resolved issues')
    parser.add_argument('--update', nargs=3, metavar=('ID', 'STATUS', 'NOTES'), 
                       help='Update issue status: ID STATUS NOTES')
    parser.add_argument('--report', action='store_true', help='Generate progress report')
    parser.add_argument('--list', choices=['pending', 'resolved', 'verified', 'all'], 
                       default='all', help='List issues by status')
    
    args = parser.parse_args()
    
    tracker = IssueTracker()
    
    if args.dashboard:
        tracker.show_dashboard()
    elif args.verify:
        tracker.run_verification_checks(args.verify)
    elif args.verify_all:
        tracker.run_all_verifications()
    elif args.update:
        issue_id, status_str, notes = args.update
        try:
            status = IssueStatus(status_str.lower())
            tracker.update_issue_status(issue_id, status, notes)
        except ValueError:
            logger.error(f"Invalid status: {status_str}")
            logger.info(f"Valid statuses: {[s.value for s in IssueStatus]}")
    elif args.report:
        print(tracker.generate_progress_report())
    elif args.list:
        if args.list == 'all':
            issues = list(tracker.issues.values())
        else:
            status = IssueStatus(args.list)
            issues = tracker.get_issues_by_status(status)
        
        for issue in sorted(issues, key=lambda x: (x.priority.value, x.id)):
            status_icon = {"pending": "⏳", "in_progress": "🔄", "resolved": "✅", "verified": "✅", "blocked": "🚫"}
            priority_icon = {"critical": "🚨", "high": "⚠️", "medium": "📋", "low": "💡"}
            print(f"{status_icon.get(issue.status.value, '❓')} {priority_icon.get(issue.priority.value, '❓')} "
                  f"{issue.id}: {issue.title}")
    else:
        tracker.show_dashboard()


if __name__ == "__main__":
    main()