# 🤖 LLM-Powered Setup Agent with NVIDIA Quadro P1000 GPU Optimization

**All-in-One** Python-based intelligent assistant that integrates with Ollama for command execution, focusing on Git operations and system setup tasks. **Optimized for NVIDIA Quadro P1000 GPU acceleration.**

## ✨ Features

🤖 **AI-Powered**: Uses Ollama (mistral, qwen3:8b, or other models) for intelligent command suggestions
🎯 **GPU-Optimized**: Dedicated NVIDIA Quadro P1000 optimization for maximum LLM performance
🖥️ **Dual Interface**: Both CLI and GUI modes available
🛡️ **Safety First**: User confirmation required for potentially dangerous commands
💾 **Memory Persistence**: Remembers user preferences and settings
📝 **Git Focus**: Specialized for Git operations and development workflows
🔄 **Interactive**: Continuous chat interface with real-time GPU monitoring
📊 **Performance Monitoring**: Real-time GPU utilization and memory tracking

## 📋 Prerequisites

1. **Python 3.7+** with virtual environment
2. **NVIDIA Quadro P1000** with updated drivers
3. **Ollama** running locally on `localhost:11434`
4. **Git** installed on your system

### Installing Ollama

```bash
# Download and install Ollama from https://ollama.ai
# Then pull a model:
ollama pull mistral
# OR
ollama pull qwen:7b-instruct
```

## Installation

1. **Clone/Setup the project**:
   ```bash
   cd /f/SetupAgent
   ```

2. **Activate virtual environment**:
   ```bash
   .venv/Scripts/activate  # Windows
   # or
   source .venv/bin/activate  # Linux/Mac
   ```

3. **Install dependencies**:
   ```bash
   .venv/Scripts/pip.exe install requests nvidia-ml-py3 psutil
   ```

## 🚀 Usage

The agent provides multiple interfaces:

### CLI Mode (Recommended)
```bash
python setup_agent.py
```

**Available Options:**
- 🧠 **LLM Interactive Mode** - AI-powered assistant
- 🖥️ **GUI Mode** - Graphical interface (if tkinter available)
- ⚙️ **Git Operations** - Setup, commit, log, rollback
- 🎯 **GPU Configuration** - Status and optimization
- 🧪 **Connection Testing** - Verify Ollama and GPU

### GUI Mode
```bash
python setup_agent.py
# Then select option 2 for GUI mode
```

### Direct LLM Chat
```bash
python setup_agent.py
# Then select option 1 for LLM Interactive Mode
```

## 🎯 GPU Optimization

### Automatic P1000 Configuration
The agent automatically detects and optimizes for the NVIDIA Quadro P1000:

- **Memory Management**: Uses 85% of 4GB VRAM (~3.4GB for models)
- **Batch Optimization**: Dynamic batch sizing based on available memory
- **Context Length**: Optimized for P1000's memory constraints
- **GPU Layers**: Automatically uses all available GPU layers
- **Process Isolation**: Reserves GPU exclusively for Ollama

### Recommended Models for P1000 (4GB VRAM)
```bash
# Fast inference (2.0GB)
ollama pull llama3.2:3b

# Reasoning tasks (3.2GB)
ollama pull phi4-mini-reasoning:3.8b

# Balanced performance (3.3GB)
ollama pull gemma3:4b

# High quality (4.1GB - uses most VRAM)
ollama pull mistral:latest
```

### Example Interactions

```
💬 You: Initialize a new git repository and make my first commit

🤖 AI Response: I'll help you initialize a Git repository and make your first commit.

🖥️ Suggested Commands:
  1. git init
  2. git add .
  3. git commit -m "Initial commit"

✅ Auto-executing safe command: git init
📤 Output: Initialized empty Git repository in /f/SetupAgent/.git/

✅ Auto-executing safe command: git add .
✅ Command completed successfully

✅ Auto-executing safe command: git commit -m "Initial commit"
📤 Output: [main (root-commit) abc1234] Initial commit
 2 files changed, 150 insertions(+)
```

```
💬 You: What's the current status of my repository?

🤖 AI Response: I'll check your Git repository status.

🖥️ Suggested Commands:
  1. git status

✅ Auto-executing safe command: git status
📤 Output: On branch main
nothing to commit, working tree clean
```

## Safety Features

### Auto-Executed Commands (Safe)
- `git *` - All Git commands
- `cd *` - Directory navigation  
- `python *` - Python commands
- `ls`, `dir`, `pwd` - File listing and navigation
- `echo`, `cat`, `head`, `tail` - File viewing

### Requires Confirmation
- File deletion commands
- System modification commands
- Network operations
- Package installations

### Command Timeout
- All commands timeout after 30 seconds
- Prevents hanging on long-running processes

## 📁 File Structure

```
SetupAgent/
├── setup_agent.py           # 🤖 All-in-One LLM Agent (GPU-optimized)
├── agent_memory.json        # 💾 User preferences and settings
├── chat_history.json        # 📝 Chat conversation history
├── README.md                # 📖 This documentation
└── .venv/                   # 🐍 Python virtual environment
```

**Single File Solution**: Everything is now consolidated into `setup_agent.py` for simplicity!

## Core Functions

### `run_shell(cmd)`
- Executes shell commands safely
- Returns structured result with success status, stdout, stderr
- 30-second timeout protection

### `call_ollama(prompt, model="mistral")`
- Communicates with Ollama API
- Fallback to urllib if requests unavailable
- Error handling for connection issues

### `main()`
- Interactive chat loop
- Command extraction and execution
- Safety confirmation system

## Troubleshooting

### Ollama Connection Issues
```bash
# Check if Ollama is running
curl http://localhost:11434/api/tags

# Start Ollama if not running
ollama serve

# Pull a model if none available
ollama pull mistral
```

### Python/Pip Issues
```bash
# Use virtual environment pip directly
.venv/Scripts/pip.exe install requests

# Check Python version
.venv/Scripts/python.exe --version
```

### Git Not Found
```bash
# Install Git from https://git-scm.com/downloads
# Or use package manager:
winget install Git.Git  # Windows
brew install git        # macOS
sudo apt install git    # Ubuntu
```

## Future Enhancements

- 🎨 **GUI Interface**: Tkinter-based graphical interface
- 💾 **Memory Persistence**: Remember previous conversations
- 🧪 **Test Automation**: Automated testing capabilities  
- 🎤 **Voice Control**: Speech-to-text integration
- 📁 **Project Templates**: Quick project setup templates
- 🔧 **Configuration Management**: Settings and preferences

## Contributing

This agent is designed for extensibility. Key areas for enhancement:

1. **Model Support**: Add support for other LLM providers
2. **Command Plugins**: Modular command handlers
3. **Safety Rules**: Enhanced command filtering
4. **UI Improvements**: Better formatting and interaction
5. **Integration**: IDE plugins and external tool support

## License

Open source - feel free to modify and extend for your needs!
