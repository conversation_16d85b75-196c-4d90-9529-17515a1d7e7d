#!/usr/bin/env python3
"""
🤖 LLM-Powered Setup Agent with NVIDIA Quadro P1000 GPU Optimization
A comprehensive Python-based intelligent assistant that integrates with Ollama for command execution.
Combines GUI and CLI interfaces with GPU acceleration, memory persistence, and safety features.
"""

# Standard library imports
import os
import json
import logging
import sys
import re
import urllib.parse
from datetime import datetime
from typing import Dict, Any, List, Callable, Optional

# Local imports
from file_utils import MEMORY_FILE_DEFAULT, CHAT_HISTORY_FILE_DEFAULT

# Optional external libraries with graceful fallback
# Embeddings functionality
embedding_manager = None
has_embeddings = False

# Stub class for type safety when embeddings are not available
class EmbeddingManagerStub:
    """Stub implementation for when embeddings are not available."""

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.interactions: List[Dict[str, Any]] = []

    def find_similar_interactions(self, query: str, max_results: int = 3) -> List[Dict[str, Any]]:
        """Stub method - returns empty list when embeddings not available."""
        logger.debug(f"Embeddings not available - would search for {max_results} interactions similar to: {query[:50]}...")
        return []

    def find_similar_commands(self, query: str, max_results: int = 2) -> List[Dict[str, Any]]:
        """Stub method - returns empty list when embeddings not available."""
        logger.debug(f"Embeddings not available - would search for {max_results} commands similar to: {query[:50]}...")
        return []

    def store_interaction(self, user_input: str, ai_response: str, metadata: Dict[str, Any]) -> None:
        """Stub method - does nothing when embeddings not available."""
        metadata_info = f"with metadata: {list(metadata.keys())}" if metadata else "without metadata"
        logger.debug(f"Embeddings not available - would store interaction: {user_input[:50]}... -> {ai_response[:50]}... {metadata_info}")

EmbeddingManager = None

# HTTP requests library
requests = None
urllib_request = None
should_use_requests_library = False

# Rich CLI library
use_rich = False
console = None
Prompt = None
Table = None

# GUI library
tk = None
has_gui = False

# GPU monitoring
pynvml = None
has_gpu_monitoring = False

# Initialize optional dependencies
def _init_optional_dependencies():
    """Initialize all optional dependencies with proper error handling."""
    global embedding_manager, has_embeddings, EmbeddingManager
    global requests, urllib_request, should_use_requests_library
    global use_rich, console, Prompt, Table
    global tk, has_gui
    global pynvml, has_gpu_monitoring

    # Embeddings
    try:
        from embeddings import EmbeddingManager
        has_embeddings = True
    except ImportError:
        logger.warning("Embeddings module not available - memory-enhanced features will be disabled")

    # HTTP requests
    try:
        import requests as _requests_module
        requests = _requests_module
        should_use_requests_library = True
    except ImportError:
        try:
            import urllib.request as _urllib_request_module
            urllib_request = _urllib_request_module
        except ImportError:
            logger.warning("No HTTP library available")

    # Rich CLI
    try:
        from rich.console import Console
        from rich.prompt import Prompt
        from rich.table import Table
        console = Console()
        use_rich = True
    except ImportError:
        logger.debug("Rich library not available - using basic CLI")

    # GUI
    try:
        import tkinter as _tk_module
        tk = _tk_module
        has_gui = True
    except ImportError:
        logger.debug("Tkinter not available - GUI features disabled")

    # GPU monitoring
    try:
        import pynvml as _pynvml_module  # type: ignore
        pynvml = _pynvml_module
        has_gpu_monitoring = True
    except ImportError:
        logger.debug("NVIDIA ML library not available - GPU monitoring disabled")

# Improvement hooks registry
HOOKS: Dict[str, List[Callable[..., Any]]] = {}

def register_hook(event: str, func: Callable[..., Any]) -> None:
    """Register a hook for a given event name."""
    HOOKS.setdefault(event, []).append(func)

# =============================
# CONFIGURATION LOADING
# =============================
CONFIG_FILE = "config.json"

def load_config() -> Dict[str, Any]:
    """Load configuration from config.json with error handling."""
    if os.path.exists(CONFIG_FILE):
        try:
            with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"⚠️  Failed to load config.json: {e}")
    return {}

CONFIG: Dict[str, Any] = load_config()

# =============================
# LOGGING SETUP (CONSOLIDATED)
# =============================
LOG_FILE: str = CONFIG.get('logging', {}).get('file', 'agent.log')
LOG_LEVEL: str = CONFIG.get('logging', {}).get('level', 'INFO').upper()

# Configure logging once with proper handlers
logging.basicConfig(
    level=getattr(logging, LOG_LEVEL, logging.INFO),
    format='%(asctime)s %(levelname)s %(name)s %(message)s',
    handlers=[
        logging.FileHandler(LOG_FILE, encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("SetupAgent")

# Initialize optional dependencies after logging is set up
_init_optional_dependencies()

# Initialize embeddings manager if available
if has_embeddings and EmbeddingManager:
    try:
        embedding_manager = EmbeddingManager(CONFIG)
        logger.info("🧠 Embeddings enabled - Enhanced memory and context retrieval available")
    except Exception as e:
        logger.error(f"Failed to initialize embeddings: {e}")
        has_embeddings = False
        embedding_manager = EmbeddingManagerStub(CONFIG)
else:
    # Use stub when embeddings are not available
    embedding_manager = EmbeddingManagerStub(CONFIG)

# =============================
# CONSTANTS AND CONFIGURATION
# =============================
# Plugin directory
PLUGINS_DIR = os.path.join(os.path.dirname(__file__), 'plugins')
os.makedirs(PLUGINS_DIR, exist_ok=True)

# Search configuration (consolidated)
SEARCH_CACHE_FILE = os.path.join(os.path.dirname(__file__), 'search_cache.json')
SEARCH_HISTORY_FILE = os.path.join(os.path.dirname(__file__), 'search_history.json')
SEARCH_FAVORITES_FILE = os.path.join(os.path.dirname(__file__), 'search_favorites.json')
SEARCH_CACHE_DURATION = CONFIG.get('search', {}).get('cache_duration', 3600)

# Global search data (single declaration)
search_cache: Dict[str, Any] = {}
search_history: List[Dict[str, Any]] = []
search_favorites: List[Dict[str, Any]] = []

# Memory file paths
MEMORY_FILE = MEMORY_FILE_DEFAULT
CHAT_HISTORY_FILE = CHAT_HISTORY_FILE_DEFAULT

# Memory management constants
MAX_CONVERSATION_HISTORY = CONFIG.get('memory', {}).get('max_conversation_history', 1000)
MAX_SEARCH_CACHE_SIZE = CONFIG.get('memory', {}).get('max_search_cache_size', 2000)
MAX_SEARCH_HISTORY = CONFIG.get('memory', {}).get('max_search_history', 500)
MEMORY_CLEANUP_INTERVAL = CONFIG.get('memory', {}).get('cleanup_interval_hours', 168)

# Type aliases
PluginStructure = Dict[str, Any]
PluginsRegistry = Dict[str, PluginStructure]
SearchResult = Dict[str, str]
SearchEntry = Dict[str, Any]
ConversationEntry = Dict[str, Any]

# =============================
# SECURITY UTILITIES
# =============================
def sanitize_for_logging(text: str) -> str:
    """Sanitize text for logging by masking sensitive information."""
    import re

    # Mask API keys (keep first 4 and last 4 characters)
    text = re.sub(r'([&?]key=)[^&\s]+', r'\1****', text)
    text = re.sub(r'([&?]api_key=)[^&\s]+', r'\1****', text)
    text = re.sub(r'(api_key["\']?\s*[:=]\s*["\']?)[^"\'&\s]+', r'\1****', text)
    text = re.sub(r'(password["\']?\s*[:=]\s*["\']?)[^"\'&\s]+', r'\1****', text)
    text = re.sub(r'(token["\']?\s*[:=]\s*["\']?)[^"\'&\s]+', r'\1****', text)
    text = re.sub(r'(secret["\']?\s*[:=]\s*["\']?)[^"\'&\s]+', r'\1****', text)

    return text

def mask_api_key(api_key: str) -> str:
    """Mask API key for safe logging."""
    if not api_key or len(api_key) < 8:
        return "****"
    return f"{api_key[:4]}****{api_key[-4:]}"

# =============================
# RESOURCE MANAGEMENT
# =============================
import gc
from contextlib import contextmanager
from typing import Generator

def get_memory_usage_mb() -> float:
    """Get current memory usage in MB."""
    try:
        import psutil
        process = psutil.Process()
        return process.memory_info().rss / 1024 / 1024
    except ImportError:
        logger.debug("psutil not available for memory monitoring")
        return 0.0
    except Exception as e:
        logger.debug(f"Failed to get memory usage: {e}")
        return 0.0

@contextmanager
def resource_monitor(operation_name: str) -> Generator[None, None, None]:
    """Context manager to monitor resource usage during operations."""
    start_memory = get_memory_usage_mb()
    start_time = datetime.now()

    try:
        yield
    finally:
        end_memory = get_memory_usage_mb()
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        memory_delta = end_memory - start_memory

        if memory_delta > 10:  # Log if memory increased by more than 10MB
            logger.warning(f"{operation_name}: Memory increased by {memory_delta:.1f}MB in {duration:.2f}s")
        else:
            logger.debug(f"{operation_name}: Completed in {duration:.2f}s, memory delta: {memory_delta:.1f}MB")

def cleanup_resources() -> None:
    """Cleanup resources and force garbage collection."""
    try:
        # Force garbage collection
        collected = gc.collect()
        logger.debug(f"Garbage collection freed {collected} objects")

        # Get memory usage after cleanup
        memory_mb = get_memory_usage_mb()
        logger.info(f"Memory usage after cleanup: {memory_mb:.1f}MB")

    except Exception as e:
        logger.error(f"Error during resource cleanup: {e}")

# =============================
# CIRCUIT BREAKER PATTERN
# =============================
class CircuitBreaker:
    """Circuit breaker for external service calls."""

    def __init__(self, failure_threshold: int = 5, recovery_timeout: int = 60):
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.failure_count = 0
        self.last_failure_time: Optional[datetime] = None
        self.state = "CLOSED"  # CLOSED, OPEN, HALF_OPEN

    def call(self, func: Callable[..., Any], *args: Any, **kwargs: Any) -> Any:
        """Execute function with circuit breaker protection."""
        if self.state == "OPEN":
            if self._should_attempt_reset():
                self.state = "HALF_OPEN"
            else:
                raise SearchError("Service temporarily unavailable (circuit breaker open)")

        try:
            result = func(*args, **kwargs)
            self._on_success()
            return result
        except Exception as e:
            self._on_failure()
            raise e

    def _should_attempt_reset(self) -> bool:
        """Check if enough time has passed to attempt reset."""
        if self.last_failure_time is None:
            return True
        return (datetime.now() - self.last_failure_time).total_seconds() > self.recovery_timeout

    def _on_success(self) -> None:
        """Handle successful call."""
        self.failure_count = 0
        self.state = "CLOSED"

    def _on_failure(self) -> None:
        """Handle failed call."""
        self.failure_count += 1
        self.last_failure_time = datetime.now()

        if self.failure_count >= self.failure_threshold:
            self.state = "OPEN"
            logger.warning(f"Circuit breaker opened after {self.failure_count} failures")

# Global circuit breakers for external services
ollama_circuit_breaker = CircuitBreaker(failure_threshold=3, recovery_timeout=30)
search_circuit_breaker = CircuitBreaker(failure_threshold=5, recovery_timeout=60)

# =============================
# RETRY MECHANISM
# =============================
import time
from functools import wraps

def retry_on_failure(max_retries: int = 3, delay: float = 1.0, backoff: float = 2.0):
    """Decorator to retry function calls on failure with exponential backoff."""
    def decorator(func: Callable[..., Any]) -> Callable[..., Any]:
        @wraps(func)
        def wrapper(*args: Any, **kwargs: Any) -> Any:
            last_exception = None
            current_delay = delay

            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except (ConnectionError, TimeoutError, OSError) as e:
                    last_exception = e
                    if attempt < max_retries:
                        logger.warning(f"Attempt {attempt + 1} failed for {func.__name__}: {e}. Retrying in {current_delay:.1f}s...")
                        time.sleep(current_delay)
                        current_delay *= backoff
                    else:
                        logger.error(f"All {max_retries + 1} attempts failed for {func.__name__}")
                except Exception as e:
                    # Don't retry on non-transient errors
                    logger.error(f"Non-retryable error in {func.__name__}: {e}")
                    raise e

            # If we get here, all retries failed
            raise last_exception or Exception(f"All retries failed for {func.__name__}")

        return wrapper
    return decorator

# =============================
# ADVANCED CACHE MANAGEMENT
# =============================
from collections import OrderedDict

class LRUCache:
    """LRU Cache with TTL support."""

    def __init__(self, max_size: int = 1000, ttl_seconds: int = 3600):
        self.max_size = max_size
        self.ttl_seconds = ttl_seconds
        self.cache: OrderedDict[str, Dict[str, Any]] = OrderedDict()

    def get(self, key: str) -> Optional[Any]:
        """Get item from cache, return None if expired or not found."""
        if key not in self.cache:
            return None

        entry = self.cache[key]

        # Check if expired
        if self._is_expired(entry):
            del self.cache[key]
            return None

        # Move to end (most recently used)
        self.cache.move_to_end(key)
        return entry['value']

    def put(self, key: str, value: Any) -> None:
        """Put item in cache with TTL."""
        # Remove if already exists
        if key in self.cache:
            del self.cache[key]

        # Add new entry
        self.cache[key] = {
            'value': value,
            'timestamp': datetime.now(),
            'access_count': 1
        }

        # Move to end
        self.cache.move_to_end(key)

        # Evict if over size limit
        while len(self.cache) > self.max_size:
            oldest_key = next(iter(self.cache))
            del self.cache[oldest_key]

    def cleanup_expired(self) -> int:
        """Remove expired entries and return count removed."""
        expired_keys: List[str] = []
        for key, entry in self.cache.items():
            if self._is_expired(entry):
                expired_keys.append(key)

        for key in expired_keys:
            del self.cache[key]

        return len(expired_keys)

    def _is_expired(self, entry: Dict[str, Any]) -> bool:
        """Check if cache entry is expired."""
        age = (datetime.now() - entry['timestamp']).total_seconds()
        return age > self.ttl_seconds

    def stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        return {
            'size': len(self.cache),
            'max_size': self.max_size,
            'ttl_seconds': self.ttl_seconds,
            'hit_rate': self._calculate_hit_rate()
        }

    def _calculate_hit_rate(self) -> float:
        """Calculate cache hit rate (placeholder)."""
        # This would need hit/miss tracking for accurate calculation
        return 0.0

# Replace simple cache with LRU cache
advanced_search_cache = LRUCache(max_size=MAX_SEARCH_CACHE_SIZE, ttl_seconds=SEARCH_CACHE_DURATION)

# =============================
# INPUT VALIDATION & SANITIZATION
# =============================
def validate_file_path(file_path: str, allowed_dirs: Optional[List[str]] = None) -> bool:
    """Validate file path to prevent directory traversal attacks."""
    if not file_path or not isinstance(file_path, str):  # type: ignore
        return False

    # Normalize path
    normalized_path = os.path.normpath(file_path)

    # Check for directory traversal attempts
    if '..' in normalized_path or normalized_path.startswith('/'):
        logger.warning(f"Directory traversal attempt detected: {file_path}")
        return False

    # Check against allowed directories if specified
    if allowed_dirs:
        abs_path = os.path.abspath(normalized_path)
        for allowed_dir in allowed_dirs:
            allowed_abs = os.path.abspath(allowed_dir)
            if abs_path.startswith(allowed_abs):
                return True
        logger.warning(f"File path outside allowed directories: {file_path}")
        return False

    return True

def sanitize_user_input(user_input: str, max_length: int = 10000) -> str:
    """Sanitize user input for safety."""
    if not isinstance(user_input, str):  # type: ignore
        return ""

    # Limit length
    if len(user_input) > max_length:
        logger.warning(f"Input truncated from {len(user_input)} to {max_length} characters")
        user_input = user_input[:max_length]

    # Remove null bytes and control characters (except newlines and tabs)
    sanitized = ''.join(char for char in user_input
                       if ord(char) >= 32 or char in '\n\t\r')

    # Log if sanitization occurred
    if sanitized != user_input:
        logger.info("User input was sanitized")

    return sanitized

def validate_search_query(query: str) -> bool:
    """Validate search query for safety."""
    if not query or not isinstance(query, str):  # type: ignore
        return False

    # Check length
    if len(query) > 1000:
        logger.warning(f"Search query too long: {len(query)} characters")
        return False

    # Check for suspicious patterns
    suspicious_patterns = [
        r'<script',
        r'javascript:',
        r'data:',
        r'vbscript:',
        r'onload=',
        r'onerror='
    ]

    query_lower = query.lower()
    for pattern in suspicious_patterns:
        if re.search(pattern, query_lower):
            logger.warning(f"Suspicious pattern detected in search query: {pattern}")
            return False

    return True

# =============================
# CUSTOM EXCEPTIONS
# =============================
class SetupAgentError(Exception):
    """Base exception for Setup Agent errors."""
    pass

class ConfigurationError(SetupAgentError):
    """Raised when there's a configuration issue."""
    pass

class EmbeddingsError(SetupAgentError):
    """Raised when there's an embeddings-related error."""
    pass

class SearchError(SetupAgentError):
    """Raised when there's a search-related error."""
    pass

class AgentMemoryError(SetupAgentError):
    """Raised when there's a memory management error."""
    pass

class OllamaError(SetupAgentError):
    """Raised when there's an Ollama API error."""
    pass

# Safe Rich CLI wrapper functions
def safe_console_print(text: str, style: str = "") -> None:
    """Safely print with Rich console or fallback to standard print."""
    if use_rich and console is not None:
        if style:
            console.print(f"[{style}]{text}[/{style}]")
        else:
            console.print(text)
    else:
        print(text)

def safe_prompt_ask(prompt_text: str) -> str:
    """Safely ask for input with Rich prompt or fallback to standard input."""
    if use_rich and Prompt is not None:
        return Prompt.ask(prompt_text)
    else:
        return input(prompt_text + " ").strip()

def safe_table_display(title: str, data: Dict[str, Any]) -> None:
    """Safely display table with Rich or fallback to standard print."""
    if use_rich and console is not None and Table is not None:
        table = Table(title=title)
        table.add_column("Key")
        table.add_column("Value")
        for k, v in data.items():
            table.add_row(str(k), str(v))
        console.print(table)
    else:
        print(f"\n=== {title} ===")
        for k, v in data.items():
            print(f"{k}: {v}")
        print("=" * (len(title) + 8))

# Override hardcoded values if present in config.json
OLLAMA_BASE_URL: str = CONFIG.get('ollama', {}).get('url', "http://localhost:11434")
OLLAMA_URL: str = f"{OLLAMA_BASE_URL}/api/generate"
DEFAULT_MODEL: str = CONFIG.get('ollama', {}).get('default_model', "mistral")
OLLAMA_GPU_LAYERS: int = CONFIG.get('gpu', {}).get('gpu_layers', -1)
GPU_MEMORY_RESERVE_MB: int = CONFIG.get('gpu', {}).get('memory_reserve_mb', 512)
QUADRO_P1000_TARGET_GPU: int = CONFIG.get('gpu', {}).get('target_gpu', 0)

def get_gpu_info() -> Dict[str, Any]:
    """Get GPU information if monitoring is available."""
    if not has_gpu_monitoring or not pynvml:
        return {"error": "GPU monitoring not available"}

    try:
        pynvml.nvmlInit()  # type: ignore
        device_count = pynvml.nvmlDeviceGetCount()  # type: ignore

        gpu_info: Dict[str, Any] = {
            "device_count": device_count,
            "devices": []
        }

        for i in range(device_count):
            handle = pynvml.nvmlDeviceGetHandleByIndex(i)  # type: ignore
            name = pynvml.nvmlDeviceGetName(handle).decode('utf-8')  # type: ignore

            # Get memory info
            mem_info = pynvml.nvmlDeviceGetMemoryInfo(handle)  # type: ignore

            # Get utilization
            util = pynvml.nvmlDeviceGetUtilizationRates(handle)  # type: ignore

            device_info: Dict[str, Any] = {
                "index": i,
                "name": name,
                "memory_total": mem_info.total,  # type: ignore
                "memory_used": mem_info.used,  # type: ignore
                "memory_free": mem_info.free,  # type: ignore
                "utilization_gpu": util.gpu,  # type: ignore
                "utilization_memory": util.memory  # type: ignore
            }

            gpu_info["devices"].append(device_info)  # type: ignore

        return gpu_info

    except Exception as e:
        logger.error(f"Failed to get GPU info: {e}")
        return {"error": f"GPU monitoring failed: {e}"}

def monitor_gpu_usage() -> str:
    """Monitor and display GPU usage information."""
    gpu_info = get_gpu_info()

    if "error" in gpu_info:
        return f"❌ {gpu_info['error']}"

    result = f"🖥️ GPU Information ({gpu_info['device_count']} device(s)):\n"

    for device in gpu_info["devices"]:
        memory_used_gb = device["memory_used"] / (1024**3)
        memory_total_gb = device["memory_total"] / (1024**3)
        memory_percent = (device["memory_used"] / device["memory_total"]) * 100

        result += f"\n📊 GPU {device['index']}: {device['name']}\n"
        result += f"   Memory: {memory_used_gb:.1f}GB / {memory_total_gb:.1f}GB ({memory_percent:.1f}%)\n"
        result += f"   GPU Utilization: {device['utilization_gpu']}%\n"
        result += f"   Memory Utilization: {device['utilization_memory']}%\n"

    return result

# =============================
# COMMAND WHITELIST/BLACKLIST CONFIGURATION
# =============================
COMMAND_WHITELIST: List[str] = CONFIG.get('command_whitelist', [
    'git', 'cd', 'ls', 'dir', 'pwd', 'whoami', 'echo', 'cat', 'head', 'tail',
    'grep', 'find', 'which', 'where', 'python', 'pip', 'node', 'npm', 'code'
])
COMMAND_BLACKLIST: List[str] = CONFIG.get('command_blacklist', [
    'rm ', 'del ', 'format', 'shutdown', 'reboot', 'kill', 'taskkill',
    'sudo rm', 'sudo del', '> /dev/', 'dd if=', 'mkfs'
])

def validate_command(command: str) -> bool:
    """Validate command against whitelist and blacklist for security."""
    if not command or not command.strip():
        return False

    command_lower = command.lower().strip()

    # Check blacklist first (security priority)
    for blocked_cmd in COMMAND_BLACKLIST:
        if blocked_cmd.lower() in command_lower:
            logger.warning(f"Command blocked by blacklist: {command}")
            return False

    # Check whitelist (if configured)
    if COMMAND_WHITELIST:
        command_start = command_lower.split()[0] if command_lower.split() else ""
        for allowed_cmd in COMMAND_WHITELIST:
            if command_start == allowed_cmd.lower():
                return True
        logger.warning(f"Command not in whitelist: {command}")
        return False

    # If no whitelist configured, allow (but still check blacklist)
    return True

def safe_command_execution(command: str) -> str:
    """Safely execute command with validation and error handling."""
    if not validate_command(command):
        return "❌ Command blocked for security reasons"

    try:
        import subprocess
        result = subprocess.run(
            command,
            shell=True,
            capture_output=True,
            text=True,
            timeout=30  # 30 second timeout
        )

        if result.returncode == 0:
            return result.stdout if result.stdout else "Command executed successfully"
        else:
            return f"Command failed with exit code {result.returncode}: {result.stderr}"

    except ImportError:
        return "❌ Subprocess module not available"
    except Exception as e:
        # Handle both subprocess.TimeoutExpired and other exceptions
        if "TimeoutExpired" in str(type(e)):
            return "❌ Command timed out after 30 seconds"
        logger.error(f"Command execution error: {e}")
        return f"❌ Command execution failed: {e}"

# =============================
# PROMPT TEMPLATES
# =============================
PROMPT_TEMPLATES: Dict[str, str] = CONFIG.get('prompt_templates', {
    'default': '{prompt}',
    'code': 'You are a helpful coding assistant. {prompt}',
    'explain': 'Explain the following in detail: {prompt}'
})

def get_prompt_template(name: str, prompt: str) -> str:
    template = PROMPT_TEMPLATES.get(name, PROMPT_TEMPLATES['default'])
    return template.format(prompt=prompt)

# =============================
# SESSION MANAGEMENT STUBS
# =============================
SESSIONS_DIR = os.path.join(os.path.dirname(__file__), 'sessions')
os.makedirs(SESSIONS_DIR, exist_ok=True)

def list_sessions() -> List[str]:
    return [f for f in os.listdir(SESSIONS_DIR) if f.endswith('.json')]

def save_session(name: str, data: dict[str, Any]) -> None:
    """Save session data to file with error handling."""
    try:
        with open(os.path.join(SESSIONS_DIR, f'{name}.json'), 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2)
        logger.info(f"Session '{name}' saved successfully")
    except Exception as e:
        logger.error(f"Failed to save session '{name}': {e}")
        raise ConfigurationError(f"Could not save session '{name}': {e}")

def load_session(name: str) -> dict[str, Any]:
    """Load session data from file with error handling."""
    try:
        with open(os.path.join(SESSIONS_DIR, f'{name}.json'), 'r', encoding='utf-8') as f:
            data = json.load(f)
        logger.info(f"Session '{name}' loaded successfully")
        return data
    except FileNotFoundError:
        logger.error(f"Session '{name}' not found")
        raise ConfigurationError(f"Session '{name}' does not exist")
    except json.JSONDecodeError as e:
        logger.error(f"Invalid JSON in session '{name}': {e}")
        raise ConfigurationError(f"Session '{name}' contains invalid JSON: {e}")
    except Exception as e:
        logger.error(f"Failed to load session '{name}': {e}")
        raise ConfigurationError(f"Could not load session '{name}': {e}")

# =============================
# USAGE ANALYTICS STUB
# =============================
USAGE_FILE = 'usage_stats.json'
def log_usage(event: str, details: dict[str, Any] | None = None) -> None:
    try:
        if os.path.exists(USAGE_FILE):
            with open(USAGE_FILE, 'r', encoding='utf-8') as f:
                stats: dict[str, Any] = json.load(f)
        else:
            stats: dict[str, Any] = {}
        stats.setdefault(event, 0)
        stats[event] += 1

        # Store details if provided
        if details:
            detail_key = f"{event}_details"
            if detail_key not in stats:
                stats[detail_key] = []
            stats[detail_key].append({
                "timestamp": datetime.now().isoformat(),
                "details": details
            })
            # Keep only last 10 detail entries
            stats[detail_key] = stats[detail_key][-10:]

        with open(USAGE_FILE, 'w', encoding='utf-8') as f:
            json.dump(stats, f, indent=2)
    except Exception as e:
        logger.error(f"Failed to log usage: {e}")

# =============================
# PLUGIN MANIFEST & DYNAMIC LOADING
# =============================
def validate_plugin_name(name: str) -> bool:
    """Validate plugin name for security (prevent path traversal)."""
    if not name or not name.strip():
        return False

    # Check for path traversal attempts
    if '..' in name or '/' in name or '\\' in name:
        logger.warning(f"Invalid plugin name (path traversal attempt): {name}")
        return False

    # Only allow alphanumeric, underscore, and hyphen
    if not re.match(r'^[a-zA-Z0-9_-]+$', name):
        logger.warning(f"Invalid plugin name (invalid characters): {name}")
        return False

    return True

def load_plugin_manifest(plugin_dir: str) -> Dict[str, Any]:
    """Load plugin manifest with error handling."""
    manifest_path = os.path.join(plugin_dir, 'plugin.json')
    if os.path.exists(manifest_path):
        try:
            with open(manifest_path, 'r', encoding='utf-8') as f:
                manifest_data = json.load(f)

            # Validate required fields
            if not isinstance(manifest_data, dict):
                logger.warning(f"Invalid manifest format in {plugin_dir}")
                return {}

            # Type cast to ensure proper typing
            manifest: Dict[str, Any] = manifest_data  # type: ignore
            return manifest
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in plugin manifest {manifest_path}: {e}")
            return {}
        except Exception as e:
            logger.error(f"Failed to load plugin manifest {manifest_path}: {e}")
            return {}
    return {}

def load_plugin(name: str) -> None:
    """Load plugin with security validation and error handling."""
    if not validate_plugin_name(name):
        print(f"❌ Invalid plugin name: {name}")
        return

    plugin_path = os.path.join(PLUGINS_DIR, name)

    # Ensure the plugin path is within the plugins directory (security check)
    try:
        real_plugin_path = os.path.realpath(plugin_path)
        real_plugins_dir = os.path.realpath(PLUGINS_DIR)
        if not real_plugin_path.startswith(real_plugins_dir):
            logger.warning(f"Plugin path outside plugins directory: {name}")
            print(f"❌ Security error: Plugin path outside allowed directory")
            return
    except Exception as e:
        logger.error(f"Path validation error for plugin {name}: {e}")
        print(f"❌ Path validation failed for plugin: {name}")
        return

    if not os.path.isdir(plugin_path):
        print(f"❌ Plugin directory not found: {name}")
        return

    manifest = load_plugin_manifest(plugin_path)
    if manifest:
        description = manifest.get('description', 'No description')
        version = manifest.get('version', 'Unknown version')
        print(f"📦 Loading plugin {name} v{version}: {description}")

        # Check for required permissions if specified
        permissions = manifest.get('permissions', [])
        if permissions:
            print(f"⚠️  Plugin requests permissions: {', '.join(permissions)}")

    try:
        sys.path.insert(0, plugin_path)
        __import__(name)
        print(f"✅ Plugin {name} loaded successfully")
        logger.info(f"Plugin loaded: {name}")
    except Exception as e:
        print(f"❌ Failed to load plugin {name}: {e}")
        logger.error(f"Plugin loading failed for {name}: {e}")
    finally:
        if sys.path and sys.path[0] == plugin_path:
            sys.path.pop(0)

def print_help() -> None:
    print("\n=== Setup Agent Help ===")
    print("Available commands:")
    print("  help                Show this help message")
    print("  plugins             List available plugins")
    print("  load_plugin <name>  Dynamically load a plugin")
    print("  generate_plugin <name> Generate a new plugin")
    print("  sessions            List saved sessions")
    print("  use_session <name>  Load a session")
    print("  save_session <name> Save current session")
    print("  models              List available LLM models")
    print("  set_model <name>    Switch LLM model at runtime")
    print("  prompt_templates    List available prompt templates")
    print("  set_prompt <name>   Use a prompt template")
    print("  analytics           Show usage analytics")
    print("  command_freq        Show command frequency analytics")
    print("  llm_stream <prompt> Stream LLM response")
    if has_embeddings:
        print("🧠 Embeddings & Memory:")
        print("  vector_query <query>      Query vector memory for similar interactions")
        print("  similar_commands <cmd>    Find similar past command executions")
        print("  embeddings_status         Show embeddings system status")
        print("  memory_stats              Show memory and embedding statistics")
    else:
        print("  vector_query <query>      Query vector memory (disabled - no embeddings)")
    print("  run_plugin_isolated <name> Run plugin in isolation (stub)")
    print("  run_sandboxed <cmd> Run command in sandbox (stub)")
    print("  run_api             Start HTTP API (if enabled)")
    print("  search <query>      Perform a web search")
    print("  search_ai <query>   Perform AI-enhanced web search")
    print("  search_history      Show recent search history")
    print("  search_favorites    Show saved search favorites")
    print("  add_favorite        Add a search query to favorites")
    print("  remove_favorite     Remove a search favorite by index")
    print("  clear_cache         Clear the search results cache")
    print("  search_engines      Show available search engines")
    print("")
    print("🗨️ Conversation Commands:")
    print("  chat                Enter dedicated chat mode")
    print("  clear_history       Clear conversation history")
    print("  show_history        Show recent conversation history")
    print("")
    print("🧹 Memory Management:")
    print("  cleanup_memory      Clean up expired cache and trim history")
    print("  memory_usage        Show current memory usage statistics")
    print("  mark_important      Mark last conversation as important")
    print("  show_important      Show all important conversations")
    print("")
    if has_gpu_monitoring:
        print("🖥️ GPU Monitoring:")
        print("  gpu_info            Show GPU information and status")
        print("  monitor_gpu         Monitor GPU usage and memory")
        print("")
    print("🔒 Security & Commands:")
    print("  safe_exec <cmd>     Execute command with security validation")
    print("")
    print("💡 Pro tip: You can chat naturally without using commands!")
    print("  exit                Exit the agent")
    print("=======================\n")

# =============================
# COMMAND AUTOCOMPLETE (CLI)
# =============================
try:
    import readline
    _COMMANDS = [
        'help', 'plugins', 'load_plugin', 'generate_plugin', 'sessions', 'use_session', 'save_session',
        'models', 'set_model', 'prompt_templates', 'set_prompt', 'analytics', 'command_freq', 'llm_stream',
        'vector_query', 'run_plugin_isolated', 'run_sandboxed', 'run_api', 'chat', 'clear_history',
        'show_history', 'cleanup_memory', 'memory_usage', 'mark_important', 'show_important', 'gpu_info',
        'monitor_gpu', 'safe_exec', 'exit', 'quit', 'search', 'search_ai', 'search_history', 'search_favorites',
        'add_favorite', 'remove_favorite', 'clear_cache', 'search_engines'
    ]
    def completer(text: str, state: int) -> str | None:
        options = [cmd for cmd in _COMMANDS if cmd.startswith(text)]
        if state < len(options):
            return options[state]
        return None
    # type: ignore for dynamic attribute warnings
    readline.set_completer(completer)  # type: ignore[attr-defined]
    readline.parse_and_bind('tab: complete')  # type: ignore[attr-defined]
except ImportError:
    pass

# Additional configuration constants
COMMAND_HISTORY_FILE = "command_history.json"
MAX_COMMAND_HISTORY = 200

# Plugins registry
PLUGINS: PluginsRegistry = {}

def load_search_data() -> None:
    """Load search cache, history, and favorites from files."""
    global search_cache, search_history, search_favorites

    # Load cache
    try:
        if os.path.exists(SEARCH_CACHE_FILE):
            with open(SEARCH_CACHE_FILE, 'r', encoding='utf-8') as f:
                search_cache = json.load(f)
    except (json.JSONDecodeError, FileNotFoundError) as e:
        logger.warning(f"Search cache file issue: {e}")
        search_cache = {}
    except PermissionError as e:
        logger.error(f"Permission denied accessing search cache: {e}")
        search_cache = {}
    except Exception as e:
        logger.error(f"Unexpected error loading search cache: {e}")
        search_cache = {}

    # Load history
    try:
        if os.path.exists(SEARCH_HISTORY_FILE):
            with open(SEARCH_HISTORY_FILE, 'r', encoding='utf-8') as f:
                search_history = json.load(f)
    except (json.JSONDecodeError, FileNotFoundError) as e:
        logger.warning(f"Search history file issue: {e}")
        search_history = []
    except PermissionError as e:
        logger.error(f"Permission denied accessing search history: {e}")
        search_history = []
    except Exception as e:
        logger.error(f"Unexpected error loading search history: {e}")
        search_history = []

    # Load favorites
    try:
        if os.path.exists(SEARCH_FAVORITES_FILE):
            with open(SEARCH_FAVORITES_FILE, 'r', encoding='utf-8') as f:
                search_favorites = json.load(f)
    except (json.JSONDecodeError, FileNotFoundError) as e:
        logger.warning(f"Search favorites file issue: {e}")
        search_favorites = []
    except PermissionError as e:
        logger.error(f"Permission denied accessing search favorites: {e}")
        search_favorites = []
    except Exception as e:
        logger.error(f"Unexpected error loading search favorites: {e}")
        search_favorites = []

def save_search_data() -> None:
    """Save search cache, history, and favorites to files."""
    # Save cache
    try:
        with open(SEARCH_CACHE_FILE, 'w', encoding='utf-8') as f:
            json.dump(search_cache, f, indent=2)
    except PermissionError as e:
        logger.error(f"Permission denied saving search cache: {e}")
        raise SearchError(f"Cannot save search cache: permission denied")
    except OSError as e:
        logger.error(f"OS error saving search cache: {e}")
        raise SearchError(f"Cannot save search cache: {e}")
    except Exception as e:
        logger.error(f"Unexpected error saving search cache: {e}")
        raise SearchError(f"Failed to save search cache: {e}")

    # Save history
    try:
        with open(SEARCH_HISTORY_FILE, 'w', encoding='utf-8') as f:
            json.dump(search_history[-MAX_SEARCH_HISTORY:], f, indent=2)  # Keep recent searches
    except PermissionError as e:
        logger.error(f"Permission denied saving search history: {e}")
    except OSError as e:
        logger.error(f"OS error saving search history: {e}")
    except Exception as e:
        logger.error(f"Unexpected error saving search history: {e}")

    # Save favorites
    try:
        with open(SEARCH_FAVORITES_FILE, 'w', encoding='utf-8') as f:
            json.dump(search_favorites, f, indent=2)
    except PermissionError as e:
        logger.error(f"Permission denied saving search favorites: {e}")
    except OSError as e:
        logger.error(f"OS error saving search favorites: {e}")
    except Exception as e:
        logger.error(f"Unexpected error saving search favorites: {e}")

def add_to_search_history(query: str, results_count: int, engine: str) -> None:
    """Add a search query to history."""
    global search_history
    search_entry: SearchEntry = {
        "timestamp": datetime.now().isoformat(),
        "query": query,
        "results_count": results_count,
        "engine": engine
    }
    search_history.append(search_entry)
    # Keep only recent searches
    if len(search_history) > MAX_SEARCH_HISTORY:
        search_history = search_history[-MAX_SEARCH_HISTORY:]

def is_cache_valid(cache_entry: Dict[str, Any]) -> bool:
    """Check if a cache entry is still valid."""
    try:
        cache_time = datetime.fromisoformat(cache_entry['timestamp'])
        current_time = datetime.now()
        return (current_time - cache_time).total_seconds() < SEARCH_CACHE_DURATION
    except Exception:
        return False

def get_cached_results(query: str, engine: str) -> List[Dict[str, str]] | None:
    """Get cached search results if available and valid."""
    cache_key = f"{engine}:{query.lower()}"
    if cache_key in search_cache:
        cache_entry = search_cache[cache_key]
        if is_cache_valid(cache_entry):
            print(f"📋 Using cached results for: {query}")
            return cache_entry['results']
        else:
            # Remove expired cache entry
            del search_cache[cache_key]
    return None

def cache_search_results(query: str, engine: str, results: List[SearchResult]) -> None:
    """Cache search results."""
    cache_key = f"{engine}:{query.lower()}"
    cache_entry: SearchEntry = {
        "timestamp": datetime.now().isoformat(),
        "results": results
    }
    search_cache[cache_key] = cache_entry

def filter_search_results(results: List[SearchResult], domain_filter: Optional[str] = None,
                         exclude_domains: Optional[List[str]] = None) -> List[SearchResult]:
    """Filter search results by domain or other criteria."""
    if not results:
        return results

    filtered_results: List[SearchResult] = []
    
    for result in results:
        if "error" in result:
            filtered_results.append(result)
            continue
            
        url = result.get('url', '')
        
        # Domain filtering
        if domain_filter and domain_filter not in url:
            continue
            
        # Exclude domains
        if exclude_domains and any(domain in url for domain in exclude_domains):
            continue
            
        filtered_results.append(result)
    
    return filtered_results

@retry_on_failure(max_retries=2, delay=1.0)
def search_duckduckgo(query: str, max_results: int = 5) -> List[SearchResult]:
    """Search DuckDuckGo and return results."""
    # Validate input
    if not validate_search_query(query):
        return [{"error": "Invalid search query"}]

    query = sanitize_user_input(query, max_length=500)

    try:
        if not (should_use_requests_library and requests):
            return [{"error": "Requests library not available for web search"}]

        # DuckDuckGo instant answer API
        encoded_query = urllib.parse.quote(query)
        url = f"https://api.duckduckgo.com/?q={encoded_query}&format=json&no_html=1&skip_disambig=1"

        response = requests.get(url, timeout=10)
        response.raise_for_status()
        data = response.json()

        results: List[SearchResult] = []
        
        # Add instant answer if available
        if data.get('Abstract'):
            results.append({
                "title": data.get('AbstractText', 'DuckDuckGo Summary'),
                "snippet": data.get('Abstract', ''),
                "url": data.get('AbstractURL', ''),
                "source": "DuckDuckGo Instant Answer"
            })
        
        # Add related topics
        for topic in data.get('RelatedTopics', [])[:max_results-1]:
            if isinstance(topic, dict) and 'Text' in topic:
                text = str(topic.get('Text', ''))  # type: ignore
                first_url = str(topic.get('FirstURL', ''))  # type: ignore
                results.append({
                    "title": text[:100] + "..." if len(text) > 100 else text,
                    "snippet": text,
                    "url": first_url,
                    "source": "DuckDuckGo Related"
                })
        
        return results[:max_results] if results else [{"error": "No results found"}]
        
    except Exception as e:
        return [{"error": f"Search error: {e}"}]

def search_web_scraping(query: str, max_results: int = 3) -> List[SearchResult]:
    """Backup web search using simple scraping."""
    try:
        if not (should_use_requests_library and requests):
            return [{"error": "Requests library not available"}]

        # Use DuckDuckGo HTML search as backup
        encoded_query = urllib.parse.quote(query)
        url = f"https://html.duckduckgo.com/html/?q={encoded_query}"

        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }

        response = requests.get(url, headers=headers, timeout=10)
        response.raise_for_status()

        # Simple regex to extract search results
        html = response.text

        # Extract titles and links (basic regex approach)
        title_pattern = r'<a[^>]*class="result__a"[^>]*href="([^"]*)"[^>]*>([^<]*)</a>'
        snippet_pattern = r'<a[^>]*class="result__snippet"[^>]*>([^<]*)</a>'

        titles = re.findall(title_pattern, html)
        snippets = re.findall(snippet_pattern, html)

        results: List[SearchResult] = []
        for i, (url, title) in enumerate(titles[:max_results]):
            snippet = snippets[i] if i < len(snippets) else ""
            results.append({
                "title": title.strip(),
                "snippet": snippet.strip(),
                "url": url,
                "source": "DuckDuckGo Search"
            })
        
        return results if results else [{"error": "No search results found"}]
        
    except Exception as e:
        return [{"error": f"Web scraping error: {e}"}]

def search_bing(query: str, max_results: int = 5) -> List[SearchResult]:
    """Search using Bing Search API (requires API key in config)."""
    try:
        if not (should_use_requests_library and requests):
            return [{"error": "Requests library not available for Bing search"}]

        # Check for Bing API key in config
        bing_api_key = CONFIG.get('search_apis', {}).get('bing_api_key')
        if not bing_api_key:
            return [{"error": "Bing API key not configured"}]

        # Bing Web Search API
        encoded_query = urllib.parse.quote(query)
        url = f"https://api.bing.microsoft.com/v7.0/search?q={encoded_query}&count={max_results}"

        headers: Dict[str, str] = {
            'Ocp-Apim-Subscription-Key': bing_api_key,
            'User-Agent': 'SetupAgent/1.0'
        }
        
        response = requests.get(url, headers=headers, timeout=10)
        response.raise_for_status()
        data = response.json()

        results: List[SearchResult] = []
        
        # Process web pages results
        for item in data.get('webPages', {}).get('value', [])[:max_results]:
            results.append({
                "title": item.get('name', 'No title'),
                "snippet": item.get('snippet', ''),
                "url": item.get('url', ''),
                "source": "Bing Search"
            })
        
        return results if results else [{"error": "No Bing results found"}]
        
    except Exception as e:
        return [{"error": f"Bing search error: {e}"}]

def search_google_custom(query: str, max_results: int = 5) -> List[SearchResult]:
    """Search using Google Custom Search API (requires API key and search engine ID)."""
    try:
        if not (should_use_requests_library and requests):
            return [{"error": "Requests library not available for Google search"}]

        # Check for Google API credentials in config
        google_api_key = CONFIG.get('search_apis', {}).get('google_api_key')
        google_cx = CONFIG.get('search_apis', {}).get('google_search_engine_id')

        if not google_api_key or not google_cx:
            return [{"error": "Google Custom Search API credentials not configured"}]

        # Google Custom Search API
        encoded_query = urllib.parse.quote(query)
        url = f"https://www.googleapis.com/customsearch/v1?key={google_api_key}&cx={google_cx}&q={encoded_query}&num={max_results}"

        # Log sanitized URL for debugging
        safe_url = sanitize_for_logging(url)
        logger.debug(f"Google search URL: {safe_url}")

        response = requests.get(url, timeout=10)
        response.raise_for_status()
        data = response.json()

        results: List[SearchResult] = []
        
        # Process search results
        for item in data.get('items', [])[:max_results]:
            results.append({
                "title": item.get('title', 'No title'),
                "snippet": item.get('snippet', ''),
                "url": item.get('link', ''),
                "source": "Google Custom Search"
            })
        
        return results if results else [{"error": "No Google results found"}]
        
    except Exception as e:
        return [{"error": f"Google search error: {e}"}]

def get_available_search_engines() -> List[str]:
    """Get list of available search engines based on configuration."""
    engines = ['duckduckgo']  # Always available
    
    # Check for API keys
    if CONFIG.get('search_apis', {}).get('bing_api_key'):
        engines.append('bing')
    
    if (CONFIG.get('search_apis', {}).get('google_api_key') and 
        CONFIG.get('search_apis', {}).get('google_search_engine_id')):
        engines.append('google')
    
    return engines

def search_multiple_engines(query: str, engines: Optional[List[str]] = None, max_results: int = 5) -> Dict[str, List[SearchResult]]:
    """Search using multiple engines and return combined results."""
    if engines is None:
        engines = get_available_search_engines()

    all_results: Dict[str, List[SearchResult]] = {}
    
    for engine in engines:
        print(f"🔍 Searching {engine.title()}...")
        
        # Check cache first
        cached_results = get_cached_results(query, engine)
        if cached_results:
            all_results[engine] = cached_results
            continue
        
        # Perform search based on engine
        if engine == 'duckduckgo':
            results = search_duckduckgo(query, max_results)
        elif engine == 'bing':
            results = search_bing(query, max_results)
        elif engine == 'google':
            results = search_google_custom(query, max_results)
        else:
            results = [{"error": f"Unknown search engine: {engine}"}]
        
        # Cache results if successful
        if results and not (len(results) == 1 and "error" in results[0]):
            cache_search_results(query, engine, results)
        
        all_results[engine] = results
    
    return all_results

def combine_search_results(engine_results: Dict[str, List[SearchResult]], max_results: int = 10) -> List[SearchResult]:
    """Combine and deduplicate results from multiple search engines."""
    combined: List[SearchResult] = []
    seen_urls: set[str] = set()
    
    # Priority order for engines (can be configured)
    engine_priority = CONFIG.get('search', {}).get('engine_priority', ['google', 'bing', 'duckduckgo'])
    
    # Process results in priority order
    for engine in engine_priority:
        if engine not in engine_results:
            continue
            
        for result in engine_results[engine]:
            if "error" in result:
                continue
                
            url = result.get('url', '')
            if url and url not in seen_urls:
                seen_urls.add(url)
                combined.append(result)
                
                if len(combined) >= max_results:
                    break
        
        if len(combined) >= max_results:
            break
    
    # Fill remaining slots with results from any engine
    if len(combined) < max_results:
        for engine, results in engine_results.items():
            for result in results:
                if "error" in result:
                    continue
                    
                url = result.get('url', '')
                if url and url not in seen_urls:
                    seen_urls.add(url)
                    combined.append(result)
                    
                    if len(combined) >= max_results:
                        break
            
            if len(combined) >= max_results:
                break
    
    return combined

def perform_web_search(query: str, max_results: int = 5, engines: Optional[List[str]] = None,
                      domain_filter: Optional[str] = None, exclude_domains: Optional[List[str]] = None) -> List[SearchResult]:
    """Perform enhanced web search using available methods with caching and filtering."""
    print(f"🔍 Searching the web for: {query}")
    
    # Use default engines if none specified
    if engines is None:
        engines = ['duckduckgo']  # Default to DuckDuckGo
    
    # Check for multi-engine search
    if len(engines) > 1:
        engine_results = search_multiple_engines(query, engines, max_results)
        results = combine_search_results(engine_results, max_results)
    else:
        engine = engines[0]
        
        # Check cache first
        cached_results = get_cached_results(query, engine)
        if cached_results:
            results = cached_results
        else:
            # Perform search based on engine
            if engine == 'duckduckgo':
                results = search_duckduckgo(query, max_results)
                # Fallback to web scraping if needed
                if not results or (len(results) == 1 and "error" in results[0]):
                    print("📝 Trying alternative search method...")
                    backup_results = search_web_scraping(query, max_results)
                    if backup_results and not ("error" in backup_results[0]):
                        results = backup_results
            elif engine == 'bing':
                results = search_bing(query, max_results)
            elif engine == 'google':
                results = search_google_custom(query, max_results)
            else:
                results = [{"error": f"Unknown search engine: {engine}"}]
            
            # Cache results if successful
            if results and not (len(results) == 1 and "error" in results[0]):
                cache_search_results(query, engine, results)
    
    # Apply filters
    if domain_filter or exclude_domains:
        results = filter_search_results(results, domain_filter, exclude_domains)
    
    # Add to search history
    if results and not (len(results) == 1 and "error" in results[0]):
        add_to_search_history(query, len(results), ', '.join(engines))
    
    return results

def format_search_results(results: List[Dict[str, str]]) -> str:
    """Format search results for display."""
    if not results:
        return "❌ No search results found."
    
    if len(results) == 1 and "error" in results[0]:
        return f"❌ Search error: {results[0]['error']}"
    
    formatted = "🔍 **Search Results:**\n\n"
    
    for i, result in enumerate(results, 1):
        if "error" in result:
            continue
            
        title = result.get('title', 'No title')
        snippet = result.get('snippet', 'No description')
        url = result.get('url', '')
        source = result.get('source', 'Web')
        
        formatted += f"**{i}. {title}**\n"
        if snippet:
            formatted += f"   {snippet[:200]}{'...' if len(snippet) > 200 else ''}\n"
        if url:
            formatted += f"   🔗 {url}\n"
        formatted += f"   📄 Source: {source}\n\n"
    
    return formatted

def search_enhanced_conversation(user_input: str, model: str = DEFAULT_MODEL) -> str:
    """Handle conversation with web search enhancement."""
    # Check if the user is asking for current/recent information
    search_triggers = [
        "latest", "current", "recent", "new", "updated", "2024", "2025", 
        "what's new", "recent news", "current version", "latest version",
        "today", "this year", "recently", "now", "currently"
    ]
    
    should_search = any(trigger in user_input.lower() for trigger in search_triggers)
    
    # Also check for specific searchable queries
    question_patterns = [
        r"what is the latest .+",
        r"how to .+ in 2024|2025",
        r"current .+ version",
        r"latest .+ news",
        r"recent .+ updates"
    ]
    
    for pattern in question_patterns:
        if re.search(pattern, user_input.lower()):
            should_search = True
            break
    
    response_parts: List[str] = []
    
    # Perform web search if needed
    if should_search:
        search_results = perform_web_search(user_input)
        search_context = format_search_results(search_results)
        response_parts.append("🌐 I found some current information for you:\n")
        response_parts.append(search_context)
        
        # Build enhanced prompt with search results
        context = get_conversation_context()
        enhanced_prompt = f"""You are a helpful AI assistant called Setup Agent. You have access to current web search results.

Previous conversation:
{context}

Current user input: {user_input}

Recent web search results:
{search_context}

Based on the search results and your knowledge, provide a comprehensive and helpful response. If the search results are relevant, incorporate them into your answer. If they're not very relevant, focus on your general knowledge but mention that you tried to find current information.

Please respond helpfully:"""
        
        # Get AI response with search context
        ai_response = call_ollama(enhanced_prompt, model)
        response_parts.append("\n🤖 **My Analysis:**")
        response_parts.append(ai_response)
        
    else:
        # Regular conversation without search
        response_parts.append(handle_conversation(user_input, model))
    
    full_response = "\n".join(response_parts)
    
    # Add to conversation history
    add_to_conversation(user_input, full_response)
    
    return full_response

# =============================
# SEARCH MANAGEMENT COMMANDS
# =============================

def show_search_history(limit: int = 10) -> None:
    """Show recent search history."""
    if not search_history:
        print("📜 No search history found.")
        return
    
    print(f"📜 Recent Search History (last {limit}):")
    print("=" * 50)
    
    for entry in search_history[-limit:]:
        timestamp = datetime.fromisoformat(entry['timestamp']).strftime("%Y-%m-%d %H:%M")
        query = entry['query']
        results_count = entry['results_count']
        engine = entry['engine']
        
        print(f"🕒 {timestamp}")
        print(f"🔍 Query: {query}")
        print(f"📊 Results: {results_count} (via {engine})")
        print("-" * 30)

def show_search_favorites() -> None:
    """Show saved search favorites."""
    if not search_favorites:
        print("⭐ No search favorites found.")
        return
    
    print("⭐ Search Favorites:")
    print("=" * 40)
    
    for i, favorite in enumerate(search_favorites, 1):
        print(f"{i}. {favorite['name']}")
        print(f"   Query: {favorite['query']}")
        print(f"   Added: {datetime.fromisoformat(favorite['timestamp']).strftime('%Y-%m-%d %H:%M')}")
        if favorite.get('description'):
            print(f"   Description: {favorite['description']}")
        print("-" * 30)

def add_search_favorite(query: str, name: Optional[str] = None, description: Optional[str] = None) -> None:
    """Add a search query to favorites."""
    global search_favorites

    if name is None:
        name = query[:50] + "..." if len(query) > 50 else query

    favorite: SearchEntry = {
        "name": name,
        "query": query,
        "timestamp": datetime.now().isoformat(),
        "description": description
    }
    
    search_favorites.append(favorite)
    save_search_data()
    print(f"⭐ Added '{name}' to search favorites.")

def remove_search_favorite(index: int) -> None:
    """Remove a search favorite by index."""
    global search_favorites
    
    if 1 <= index <= len(search_favorites):
        removed = search_favorites.pop(index - 1)
        save_search_data()
        print(f"⭐ Removed '{removed['name']}' from favorites.")
    else:
        print(f"❌ Invalid favorite index. Use 1-{len(search_favorites)}.")

def clear_search_cache() -> None:
    """Clear the search results cache."""
    global search_cache
    search_cache = {}
    save_search_data()
    print("🗑️ Search cache cleared.")

def show_search_engines() -> None:
    """Show available search engines and their configuration status."""
    engines = get_available_search_engines()
    
    print("🔍 Available Search Engines:")
    print("=" * 40)
    
    for engine in ['duckduckgo', 'bing', 'google']:
        status = "✅ Available" if engine in engines else "❌ Not configured"
        print(f"{engine.title()}: {status}")
        
        if engine == 'bing' and engine not in engines:
            print("   → Requires: Bing API key in config.json")
        elif engine == 'google' and engine not in engines:
            print("   → Requires: Google API key and Custom Search Engine ID in config.json")
    
    print("\n📝 Example config.json section:")
    print("""{
  "search_apis": {
    "bing_api_key": "your_bing_api_key_here",
    "google_api_key": "your_google_api_key_here",
    "google_search_engine_id": "your_custom_search_engine_id_here"
  }
}""")

def search_with_options(query: str, engines: Optional[List[str]] = None, max_results: int = 5,
                       domain_filter: Optional[str] = None, exclude_domains: Optional[List[str]] = None) -> str:
    """Enhanced search command with options."""
    results = perform_web_search(query, max_results, engines, domain_filter, exclude_domains)
    return format_search_results(results)

# Search data will be loaded in main() function

# =============================
# CORE LLM INTEGRATION
# =============================
def call_ollama(prompt: str, model: str = DEFAULT_MODEL, use_gpu_optimization: bool = True) -> str:
    """Call Ollama API with enhanced error handling and GPU optimization."""
    def _make_ollama_request() -> str:
        # Validate and sanitize inputs
        if not prompt or not prompt.strip():
            raise OllamaError("Empty prompt provided")

        if not model or not model.strip():
            raise OllamaError("Empty model name provided")

        # Sanitize prompt for safety
        sanitized_prompt = sanitize_user_input(prompt, max_length=50000)
        if len(sanitized_prompt) != len(prompt):
            logger.info("Prompt was sanitized for safety")

        ollama_url: str = OLLAMA_URL

        # Get model-specific configuration from config
        ollama_config = CONFIG.get('ollama', {})
        base_options = ollama_config.get('options', {
            "temperature": 0.7,
            "top_p": 0.9,
            "num_ctx": 4096,
            "num_batch": 512,
            "num_thread": 4,
            "repeat_penalty": 1.1,
            "top_k": 40
        })

        # Add GPU optimization
        base_options["num_gpu"] = OLLAMA_GPU_LAYERS if use_gpu_optimization else 0

        # Optimize for Mistral specifically
        formatted_prompt = sanitized_prompt
        if 'mistral' in model.lower():
            mistral_config = ollama_config.get('mistral_specific', {})

            # Use Mistral's instruction format if enabled
            if mistral_config.get('use_chat_template', True):
                # Format prompt for Mistral's instruction format
                formatted_prompt = f"<s>[INST] {sanitized_prompt} [/INST]"

        payload: Dict[str, Any] = {
            "model": model,
            "prompt": formatted_prompt,
            "stream": False,
            "options": base_options
        }

        # Try requests library first
        if should_use_requests_library and requests:
            try:
                response = requests.post(ollama_url, json=payload, headers={'Content-Type': 'application/json'}, timeout=60)
                response.raise_for_status()
                result = response.json()

                if 'error' in result:
                    raise OllamaError(f"Ollama API error: {result['error']}")

                response_text = result.get('response', '')
                if not response_text:
                    raise OllamaError("Empty response from Ollama API")

                return response_text

            except requests.exceptions.ConnectionError:
                raise OllamaError("Cannot connect to Ollama API. Is Ollama running?")
            except requests.exceptions.Timeout:
                raise OllamaError("Ollama API request timed out")
            except requests.exceptions.HTTPError as e:
                raise OllamaError(f"HTTP error from Ollama API: {e}")
            except json.JSONDecodeError:
                raise OllamaError("Invalid JSON response from Ollama API")

        # Fallback to urllib
        elif urllib_request:
            try:
                data = json.dumps(payload).encode('utf-8')
                req = urllib_request.Request(ollama_url, data=data, headers={'Content-Type': 'application/json'})
                with urllib_request.urlopen(req, timeout=60) as response:
                    result = json.loads(response.read().decode('utf-8'))

                    if 'error' in result:
                        raise OllamaError(f"Ollama API error: {result['error']}")

                    response_text = result.get('response', '')
                    if not response_text:
                        raise OllamaError("Empty response from Ollama API")

                    return response_text

            except Exception as e:
                raise OllamaError(f"urllib error: {e}")
        else:
            raise OllamaError("No HTTP library available for Ollama API calls")

    try:
        # Use circuit breaker for Ollama calls
        return ollama_circuit_breaker.call(_make_ollama_request)
    except OllamaError:
        # Re-raise our custom errors with user-friendly messages
        raise
    except Exception as e:
        # Wrap unexpected errors
        raise OllamaError(f"Unexpected error calling Ollama API: {e}") from e

# =============================
# CONVERSATIONAL MEMORY & CONTEXT
# =============================
conversation_history: List[ConversationEntry] = []

def add_to_conversation(user_input: str, ai_response: str) -> None:
    """Add a conversation turn to memory and store embeddings."""
    global conversation_history

    conversation_entry: ConversationEntry = {
        "timestamp": datetime.now().isoformat(),
        "user": user_input,
        "assistant": ai_response
    }
    conversation_history.append(conversation_entry)
    
    # Store in embeddings if available
    if has_embeddings and embedding_manager:
        try:
            # Store the interaction with metadata
            metadata = {
                'project': os.getcwd(),
                'session_type': 'conversation'
            }
            embedding_manager.store_interaction(user_input, ai_response, metadata)
        except Exception as e:
            logger.debug(f"Failed to store embeddings: {e}")
    
    # Keep only recent conversation (enforce memory limit)
    if len(conversation_history) > MAX_CONVERSATION_HISTORY:
        conversation_history = conversation_history[-MAX_CONVERSATION_HISTORY:]
        logger.info(f"Trimmed conversation history to {MAX_CONVERSATION_HISTORY} entries")

    # Save to file
    save_conversation_history()

def cleanup_memory() -> None:
    """Clean up memory by removing old entries and expired cache."""
    global search_cache, search_history, conversation_history

    try:
        # Clean up search cache (remove expired entries)
        current_time = datetime.now()
        expired_keys: List[str] = []

        for key, entry in search_cache.items():
            if isinstance(entry, dict) and 'timestamp' in entry:
                try:
                    timestamp_value = entry['timestamp']  # type: ignore
                    if isinstance(timestamp_value, str):
                        entry_time = datetime.fromisoformat(timestamp_value)
                    else:
                        # Convert to string if not already
                        entry_time = datetime.fromisoformat(str(timestamp_value))  # type: ignore

                    if (current_time - entry_time).total_seconds() > SEARCH_CACHE_DURATION:
                        expired_keys.append(key)
                except (ValueError, TypeError):
                    # Invalid timestamp format, mark for removal
                    expired_keys.append(key)

        for key in expired_keys:
            del search_cache[key]

        if expired_keys:
            logger.info(f"Cleaned up {len(expired_keys)} expired search cache entries")

        # Limit search history size
        if len(search_history) > MAX_SEARCH_HISTORY:
            search_history = search_history[-MAX_SEARCH_HISTORY:]
            logger.info(f"Trimmed search history to {MAX_SEARCH_HISTORY} entries")

        # Limit conversation history size (but preserve important conversations)
        if len(conversation_history) > MAX_CONVERSATION_HISTORY:
            # Separate important and regular conversations
            important_convs = [conv for conv in conversation_history if conv.get('important') == 'true']
            regular_convs = [conv for conv in conversation_history if conv.get('important') != 'true']

            # Keep all important conversations + recent regular ones
            max_regular = MAX_CONVERSATION_HISTORY - len(important_convs)
            if max_regular > 0:
                recent_regular = regular_convs[-max_regular:]
                conversation_history = important_convs + recent_regular
            else:
                # If too many important conversations, keep most recent important ones
                conversation_history = important_convs[-MAX_CONVERSATION_HISTORY:]

            logger.info(f"Trimmed conversation history to {len(conversation_history)} entries ({len(important_convs)} important, {len(conversation_history) - len(important_convs)} recent)")

        # Save cleaned data
        save_search_data()
        save_conversation_history()

    except Exception as e:
        logger.error(f"Error during memory cleanup: {e}")

def get_memory_usage() -> Dict[str, Any]:
    """Get current memory usage statistics."""
    embeddings_count = 0
    if has_embeddings and embedding_manager:
        try:
            # Get embeddings count if available
            embeddings_count = len(embedding_manager.interactions) if hasattr(embedding_manager, 'interactions') else 0
        except:
            embeddings_count = 0

    return {
        "conversation_history_count": len(conversation_history),
        "search_cache_count": len(search_cache),
        "search_history_count": len(search_history),
        "search_favorites_count": len(search_favorites),
        "embeddings_count": embeddings_count,
        "max_conversation_history": MAX_CONVERSATION_HISTORY,
        "max_search_cache_size": MAX_SEARCH_CACHE_SIZE,
        "max_search_history": MAX_SEARCH_HISTORY,
        "long_term_memory_enabled": has_embeddings
    }

def mark_conversation_important(conversation_index: int = -1) -> None:
    """Mark a conversation as important to prevent it from being cleaned up."""
    global conversation_history

    if not conversation_history:
        print("No conversations to mark as important.")
        return

    # Default to last conversation
    if conversation_index == -1:
        conversation_index = len(conversation_history) - 1

    if 0 <= conversation_index < len(conversation_history):
        conversation_history[conversation_index]['important'] = 'true'  # Store as string for JSON compatibility
        conversation_history[conversation_index]['marked_important_at'] = datetime.now().isoformat()
        save_conversation_history()
        print(f"✨ Marked conversation {conversation_index + 1} as important")

        # Also store in embeddings with high importance
        if has_embeddings and embedding_manager:
            try:
                conv = conversation_history[conversation_index]
                metadata = {
                    'type': 'important_conversation',
                    'importance': 'high',
                    'marked_at': datetime.now().isoformat(),
                    'project': os.getcwd()
                }
                embedding_manager.store_interaction(conv['user'], conv['assistant'], metadata)
                print("📚 Also stored in long-term memory with high importance")
            except Exception as e:
                logger.warning(f"Failed to store important conversation in embeddings: {e}")
    else:
        print(f"Invalid conversation index: {conversation_index}")

def get_important_conversations() -> List[Dict[str, Any]]:
    """Get all conversations marked as important."""
    return [conv for conv in conversation_history if conv.get('important') == 'true']

def get_conversation_context() -> str:
    """Get formatted conversation context for the LLM."""
    if not conversation_history:
        return ""
    
    context = "\n=== Recent Conversation ===\n"
    for turn in conversation_history[-5:]:  # Last 5 turns
        context += f"User: {turn['user']}\n"
        context += f"Assistant: {turn['assistant']}\n\n"
    context += "=== Current Conversation ===\n"
    return context

def save_conversation_history() -> None:
    """Save conversation history to file."""
    try:
        with open(CHAT_HISTORY_FILE, 'w', encoding='utf-8') as f:
            json.dump(conversation_history, f, indent=2)
    except Exception as e:
        logger.error(f"Failed to save conversation history: {e}")

def load_conversation_history() -> None:
    """Load conversation history from file."""
    global conversation_history
    try:
        if os.path.exists(CHAT_HISTORY_FILE):
            with open(CHAT_HISTORY_FILE, 'r', encoding='utf-8') as f:
                conversation_history = json.load(f)
                # Keep only recent conversation
                if len(conversation_history) > MAX_CONVERSATION_HISTORY:
                    conversation_history = conversation_history[-MAX_CONVERSATION_HISTORY:]
    except Exception as e:
        logger.error(f"Failed to load conversation history: {e}")
        conversation_history = []

# =============================
# ENHANCED CHAT MODE
# =============================
def is_command(text: str) -> bool:
    """Check if the input is a command rather than a conversation."""
    commands = [
        'help', 'plugins', 'load_plugin', 'generate_plugin', 'sessions',
        'use_session', 'save_session', 'models', 'set_model', 'prompt_templates',
        'set_prompt', 'analytics', 'command_freq', 'llm_stream', 'vector_query',
        'run_plugin_isolated', 'run_sandboxed', 'run_api', 'chat', 'clear_history',
        'show_history', 'cleanup_memory', 'memory_usage', 'mark_important', 'show_important',
        'gpu_info', 'monitor_gpu', 'safe_exec', 'exit', 'quit', 'search', 'search_ai',
        'search_history', 'search_favorites', 'add_favorite', 'remove_favorite',
        'clear_cache', 'search_engines'
    ]
    first_word = text.strip().split()[0].lower() if text.strip() else ""
    return first_word in commands

def handle_conversation(user_input: str, model: str = DEFAULT_MODEL) -> str:
    """Handle natural conversation with context and embeddings-enhanced memory."""
    try:
        # Build context-aware prompt
        context = get_conversation_context()

        # Get embeddings-based context if available
        embeddings_context = ""
        project_context = ""
        if has_embeddings and embedding_manager:
            try:
                # Get similar interactions from memory
                similar_interactions = embedding_manager.find_similar_interactions(user_input, max_results=3)

                # Get similar command executions if relevant
                similar_commands = embedding_manager.find_similar_commands(user_input, max_results=2)

                # Format embeddings context
                context_parts: List[str] = []

                if similar_interactions and len(similar_interactions) > 0:
                    context_parts.append("📚 **Similar Past Conversations:**")
                    for interaction in similar_interactions:
                        timestamp = interaction.get('timestamp', 'Unknown time')
                        similarity = interaction.get('similarity', 0.0)
                        prompt = interaction.get('prompt', '')[:100] + "..." if len(interaction.get('prompt', '')) > 100 else interaction.get('prompt', '')
                        response = interaction.get('response', '')[:150] + "..." if len(interaction.get('response', '')) > 150 else interaction.get('response', '')

                        context_parts.append(f"• [{timestamp}] (similarity: {similarity:.2f})")
                        context_parts.append(f"  Q: {prompt}")
                        context_parts.append(f"  A: {response}")
                        context_parts.append("")

                if similar_commands:
                    context_parts.append("🔧 **Similar Past Commands:**")
                    for cmd in similar_commands:
                        timestamp = cmd.get('timestamp', 'Unknown time')
                        similarity = cmd.get('similarity', 0.0)
                        command = cmd.get('command', '')
                        success = "✅" if cmd.get('success', False) else "❌"

                        context_parts.append(f"• [{timestamp}] {success} (similarity: {similarity:.2f})")
                        context_parts.append(f"  Command: {command}")
                        context_parts.append("")

                embeddings_context = "\n".join(context_parts)

                # Add current project context
                current_project = os.getcwd()
                project_context = f"Current working directory: {current_project}"

            except EmbeddingsError as e:
                logger.warning(f"Embeddings error: {e}")
            except Exception as e:
                logger.debug(f"Failed to get embeddings context: {e}")

        # System prompt for the assistant
        system_prompt = """You are a helpful AI assistant called Setup Agent. You are designed to help users with:
- General questions and conversations
- Programming and development tasks
- System administration and setup
- Git operations and version control
- CUDA/GPU configuration
- Python development
- File operations and automation

You have access to various tools and plugins. You can execute commands, manage sessions, and help with technical tasks.
Be helpful, conversational, and provide practical solutions. If a user asks you to do something that requires system commands or file operations, explain what you would do and ask for confirmation.

{project_context}

Previous conversation context:
{context}

{embeddings_context}

Current user input: {user_input}

Please respond helpfully and conversationally. If you see relevant past interactions or commands above, you can reference them to provide better assistance:"""

        prompt = system_prompt.format(
            context=context,
            embeddings_context=embeddings_context,
            project_context=project_context,
            user_input=user_input
        )

        # Get AI response with error handling
        try:
            response = call_ollama(prompt, model)
        except OllamaError as e:
            logger.error(f"Ollama error: {e}")
            return f"❌ I'm having trouble connecting to the AI model. Error: {e}"

        # Add to conversation history
        add_to_conversation(user_input, response)

        # Store interaction in embeddings if enabled
        if has_embeddings and embedding_manager:
            try:
                metadata = {
                    'type': 'conversation',
                    'project': os.getcwd(),
                    'model': model
                }
                embedding_manager.store_interaction(user_input, response, metadata)
            except EmbeddingsError as e:
                logger.warning(f"Failed to store interaction in embeddings: {e}")
            except Exception as e:
                logger.debug(f"Failed to store interaction in embeddings: {e}")

        return response

    except Exception as e:
        logger.error(f"Error in handle_conversation: {e}")
        return f"❌ I encountered an error while processing your request: {e}"

# =============================
# STREAMING LLM RESPONSE SUPPORT (Ollama streaming demo)
# =============================
def call_ollama_stream(prompt: str, model: str = DEFAULT_MODEL, use_gpu_optimization: bool = True):
    """Stream responses from Ollama API if supported."""
    if not (should_use_requests_library and requests):
        print("Streaming requires 'requests' library.")
        return
    ollama_url: str = OLLAMA_URL

    # Get model-specific configuration from config
    ollama_config = CONFIG.get('ollama', {})
    base_options = ollama_config.get('options', {
        "temperature": 0.7,
        "top_p": 0.9,
        "num_ctx": 4096,
        "num_batch": 512,
        "num_thread": 4,
        "repeat_penalty": 1.1,
        "top_k": 40
    })

    # Add GPU optimization
    base_options["num_gpu"] = OLLAMA_GPU_LAYERS if use_gpu_optimization else 0

    # Optimize for Mistral specifically
    formatted_prompt = prompt
    if 'mistral' in model.lower():
        mistral_config = ollama_config.get('mistral_specific', {})

        # Use Mistral's instruction format if enabled
        if mistral_config.get('use_chat_template', True):
            # Format prompt for Mistral's instruction format
            formatted_prompt = f"<s>[INST] {prompt} [/INST]"

    payload: Dict[str, Any] = {
        "model": model,
        "prompt": formatted_prompt,
        "stream": True,
        "options": base_options
    }
    try:
        with requests.post(ollama_url, json=payload, stream=True, headers={'Content-Type': 'application/json'}) as resp:
            resp.raise_for_status()
            for line in resp.iter_lines():
                if line:
                    try:
                        data = json.loads(line.decode('utf-8'))
                        print(data.get('response', ''), end='', flush=True)
                    except Exception:
                        pass
            print()
    except Exception as e:
        print(f"Streaming error: {e}")

# =============================
# LONG-TERM MEMORY (VECTOR DB STUB)
# =============================
# Placeholder for future vector DB integration (e.g., Chroma, FAISS)
def store_vector_memory(text: str) -> None:
    # TODO: Integrate with a vector DB for semantic memory
    logger.debug(f"Would store vector memory for text: {text[:50]}...")

def query_vector_memory(query: str) -> str:
    # TODO: Integrate with a vector DB for semantic memory
    logger.debug(f"Would query vector memory for: {query}")
    return "(Vector memory search not implemented)"

# Session management functions are defined earlier in the file

# =============================
# COMMAND FREQUENCY VISUALIZATION (analytics improvement)
# =============================
def print_command_frequency() -> None:
    if os.path.exists('memory.json'):
        with open('memory.json', 'r', encoding='utf-8') as f:
            memory = json.load(f)
        freq = memory.get('command_frequency', {})
        if use_rich and console:
            table = Table(title="Command Frequency")  # type: ignore[name-defined]
            table.add_column("Command")  # type: ignore[attr-defined]
            table.add_column("Count")  # type: ignore[attr-defined]
            for k, v in freq.items():
                table.add_row(str(k), str(v))  # type: ignore[attr-defined]
            console.print(table)  # type: ignore[attr-defined]
        else:
            print("Command frequency:", freq)
    else:
        print("No command frequency data yet.")

# =============================
# ADVANCED PLUGIN ISOLATION (subprocess stub)
# =============================
def run_plugin_isolated(plugin_name: str, *args: Any) -> None:
    # TODO: Implement plugin isolation using subprocess for crash safety
    args_str = ', '.join(str(arg) for arg in args) if args else 'no args'
    print(f"(Plugin isolation not implemented: would run {plugin_name} with {args_str} in a subprocess)")

# =============================
# ADVANCED SANDBOX MODE (stub)
# =============================
def run_command_sandboxed(command: str) -> None:
    # TODO: Implement sandboxing (e.g., Docker, Windows Sandbox)
    print(f"(Sandbox mode not implemented: would run '{command}' in a sandbox)")

# =============================
# PLUGIN GENERATOR COMMAND
# =============================
def generate_plugin(name: str) -> None:
    """Generate a new plugin template."""
    plugin_dir = os.path.join(PLUGINS_DIR, name)
    os.makedirs(plugin_dir, exist_ok=True)
    
    # Create plugin.json manifest
    manifest = {
        "name": name,
        "version": "1.0.0",
        "description": f"Generated plugin: {name}",
        "author": "Setup Agent",
        "main": f"{name}.py"
    }
    
    with open(os.path.join(plugin_dir, 'plugin.json'), 'w', encoding='utf-8') as f:
        json.dump(manifest, f, indent=2)
    
    # Create plugin code template
    plugin_code = f'''"""
{name} Plugin for Setup Agent
Generated automatically by Setup Agent
"""

def initialize():
    """Initialize the plugin."""
    print(f"Plugin {name} initialized")

def execute(command: str, *args):
    """Execute plugin functionality."""
    print(f"Plugin {name} executing: {{command}} with args: {{args}}")
    return f"Plugin {name} result"

# Plugin registration
PLUGIN_INFO = {{
    "name": "{name}",
    "commands": ["example_command"],
    "description": "Example plugin generated by Setup Agent"
}}
'''
    
    with open(os.path.join(plugin_dir, f'{name}.py'), 'w', encoding='utf-8') as f:
        f.write(plugin_code)
    
    print(f"✅ Plugin '{name}' generated in {plugin_dir}")

# =============================
# MAIN COMMAND PROCESSOR AND CLI
# =============================
def process_command(cmd: str, args: List[str], default_model: str = DEFAULT_MODEL) -> str:
    """Process a single command and return the result."""
    if cmd == 'help':
        print_help()
        return "Help displayed"
    elif cmd == 'plugins':
        if os.path.exists(PLUGINS_DIR):
            plugins = [d for d in os.listdir(PLUGINS_DIR) if os.path.isdir(os.path.join(PLUGINS_DIR, d))]
            print(f"Available plugins: {plugins}")
            return f"Found {len(plugins)} plugins"
        else:
            print("No plugins directory found")
            return "No plugins"
    elif cmd == 'load_plugin' and args:
        load_plugin(args[0])
        return f"Loaded plugin: {args[0]}"
    elif cmd == 'generate_plugin' and args:
        generate_plugin(args[0])
        return f"Generated plugin: {args[0]}"
    elif cmd == 'sessions':
        sessions = list_sessions()
        print(f"Available sessions: {sessions}")
        return f"Found {len(sessions)} sessions"
    elif cmd == 'use_session' and args:
        load_session(args[0])
        print(f"Loaded session: {args[0]}")
        return f"Session {args[0]} loaded"
    elif cmd == 'save_session' and args:
        save_session(args[0], {})
        print(f"Session saved: {args[0]}")
        return f"Session {args[0]} saved"
    elif cmd == 'models':
        print(f"Available models: {default_model}")
        return f"Current model: {default_model}"
    elif cmd == 'set_model' and args:
        print(f"Model set to: {args[0]}")
        return f"Model changed to: {args[0]}"
    elif cmd == 'prompt_templates':
        safe_table_display("Prompt Templates", PROMPT_TEMPLATES)
        return "Prompt templates displayed"
    elif cmd == 'set_prompt' and args:
        print(f"Selected prompt template: {args[0]}")
        return f"Prompt template set to: {args[0]}"
    elif cmd == 'analytics':
        if os.path.exists(USAGE_FILE):
            with open(USAGE_FILE, 'r', encoding='utf-8') as f:
                stats = json.load(f)
            safe_table_display("Usage Analytics", stats)
            return "Analytics displayed"
        else:
            print("No usage stats yet.")
            return "No analytics data"
    elif cmd == 'command_freq':
        print_command_frequency()
        return "Command frequency displayed"
    elif cmd == 'llm_stream' and args:
        call_ollama_stream(' '.join(args), default_model)
        return "LLM stream completed"
    elif cmd == 'vector_query' and args:
        result = query_vector_memory(' '.join(args))
        print(result)
        return result
    elif cmd == 'run_plugin_isolated' and args:
        run_plugin_isolated(args[0], *args[1:])
        return f"Plugin {args[0]} run in isolation"
    elif cmd == 'run_sandboxed' and args:
        run_command_sandboxed(' '.join(args))
        return f"Command run in sandbox: {' '.join(args)}"
    elif cmd == 'search' and args:
        query = ' '.join(args)
        results = perform_web_search(query)
        formatted_results = format_search_results(results)
        safe_console_print(formatted_results)
        return f"Search completed for: {query}"
    elif cmd == 'search_ai' and args:
        query = ' '.join(args)
        response = search_enhanced_conversation(query, default_model)
        safe_console_print(response, "bold green")
        return f"AI-enhanced search completed for: {query}"
    elif cmd == 'search_history':
        show_search_history()
        return "Search history displayed"
    elif cmd == 'search_favorites':
        show_search_favorites()
        return "Search favorites displayed"
    elif cmd == 'add_favorite' and args:
        query = ' '.join(args)
        add_search_favorite(query)
        return f"Added favorite: {query}"
    elif cmd == 'remove_favorite' and args:
        try:
            index = int(args[0])
            remove_search_favorite(index)
            return f"Removed favorite at index: {index}"
        except ValueError:
            print("Invalid index for remove_favorite")
            return "Error: Invalid index"
    elif cmd == 'clear_cache':
        clear_search_cache()
        return "Search cache cleared"
    elif cmd == 'search_engines':
        show_search_engines()
        return "Search engines displayed"
    elif cmd == 'clear_history':
        global conversation_history
        conversation_history = []
        print("🗑️ Conversation history cleared.")
        return "Conversation history cleared"
    elif cmd == 'show_history':
        if conversation_history:
            print("\n📜 Recent Conversation History:")
            for i, turn in enumerate(conversation_history[-10:], 1):
                print(f"\n{i}. User: {turn['user']}")
                print(f"   AI: {turn['assistant'][:100]}{'...' if len(turn['assistant']) > 100 else ''}")
            return f"Displayed {len(conversation_history)} conversation turns"
        else:
            print("No conversation history yet.")
            return "No conversation history"
    elif cmd == 'cleanup_memory':
        cleanup_memory()
        print("🧹 Memory cleanup completed.")
        return "Memory cleanup completed"
    elif cmd == 'memory_usage':
        usage = get_memory_usage()
        safe_table_display("Memory Usage", usage)
        return "Memory usage displayed"
    elif cmd == 'mark_important':
        mark_conversation_important()
        return "Conversation marked as important"
    elif cmd == 'show_important':
        important_convs = get_important_conversations()
        if important_convs:
            print("\n⭐ Important Conversations:")
            for i, conv in enumerate(important_convs, 1):
                timestamp = conv.get('timestamp', 'Unknown time')
                marked_at = conv.get('marked_important_at', 'Unknown time')
                print(f"\n{i}. [{timestamp}] (marked: {marked_at})")
                print(f"   User: {conv['user'][:100]}{'...' if len(conv['user']) > 100 else ''}")
                print(f"   AI: {conv['assistant'][:100]}{'...' if len(conv['assistant']) > 100 else ''}")
            return f"Displayed {len(important_convs)} important conversations"
        else:
            print("No important conversations marked yet.")
            return "No important conversations"
    elif cmd == 'gpu_info':
        gpu_info = get_gpu_info()
        if "error" in gpu_info:
            print(f"❌ {gpu_info['error']}")
            return gpu_info['error']
        else:
            safe_table_display("GPU Information", gpu_info)
            return "GPU information displayed"
    elif cmd == 'monitor_gpu':
        result = monitor_gpu_usage()
        print(result)
        return "GPU monitoring completed"
    elif cmd.startswith('safe_exec '):
        command_to_exec = cmd[10:]  # Remove 'safe_exec ' prefix
        result = safe_command_execution(command_to_exec)
        print(result)
        return f"Command execution: {result[:50]}..."
    else:
        return f"Unknown command: {cmd}"

# =============================
# MAIN FUNCTION
# =============================
def main() -> None:
    """Main function to run the Setup Agent."""
    print("🤖 Setup Agent Started")
    print("Type 'help' for commands or just chat naturally!")

    # Load conversation history
    load_conversation_history()

    # Load search data
    load_search_data()

    # Perform initial memory cleanup with resource monitoring
    try:
        with resource_monitor("Initial memory cleanup"):
            cleanup_memory()
            cleanup_resources()

            # Cleanup expired cache entries
            expired_count = advanced_search_cache.cleanup_expired()
            if expired_count > 0:
                logger.info(f"Cleaned up {expired_count} expired cache entries")

        print("🧹 Initial memory cleanup completed")
    except Exception as e:
        logger.warning(f"Initial memory cleanup failed: {e}")

    default_model = DEFAULT_MODEL
    loop_count = 0

    try:
        while True:
            # Periodic cleanup every 50 iterations
            loop_count += 1
            if loop_count % 50 == 0:
                try:
                    cleanup_resources()
                    expired_count = advanced_search_cache.cleanup_expired()
                    if expired_count > 0:
                        logger.debug(f"Periodic cleanup: removed {expired_count} expired cache entries")
                except Exception as e:
                    logger.debug(f"Periodic cleanup failed: {e}")
            try:
                user_input = safe_prompt_ask("[bold blue]agent>[/bold blue]")

                if not user_input:
                    continue

                # Validate and sanitize user input
                if len(user_input) > 10000:
                    print("❌ Input too long. Please limit to 10,000 characters.")
                    continue

                user_input = sanitize_user_input(user_input)
                
                if user_input.lower() in ['exit', 'quit']:
                    print("👋 Goodbye!")
                    break
                
                # Check if it's a command or natural conversation
                if is_command(user_input):
                    parts = user_input.split()
                    cmd = parts[0].lower()
                    args = parts[1:]
                    
                    # Handle special commands
                    if cmd == 'chat':
                        # Enter pure conversation mode
                        print("💬 Entering conversation mode. Type 'exit' to return to commands.")
                        while True:
                            try:
                                chat_input = safe_prompt_ask("[bold blue]chat>[/bold blue]")

                                if not chat_input:
                                    continue

                                # Validate and sanitize chat input
                                if len(chat_input) > 10000:
                                    print("❌ Input too long. Please limit to 10,000 characters.")
                                    continue

                                chat_input = sanitize_user_input(chat_input)

                                if chat_input.lower() in ['exit', 'quit', 'back']:
                                    print("Returning to command mode...")
                                    break
                                
                                if chat_input:
                                    response = search_enhanced_conversation(chat_input, default_model)
                                    safe_console_print(response, "bold green")
                            except (EOFError, KeyboardInterrupt):
                                print("\nReturning to command mode...")
                                break
                    elif cmd == 'run_api':
                        try:
                            if api_enabled:
                                print("Starting HTTP API on http://localhost:5000 ...")
                                app.run(port=5000)  # type: ignore[attr-defined]
                            else:
                                print("❌ Flask not available. Install with: pip install flask")
                        except NameError:
                            print("❌ Flask not available. Install with: pip install flask")
                    else:
                        process_command(cmd, args, default_model)
                        log_usage(cmd)
                else:
                    # Natural conversation
                    response = search_enhanced_conversation(user_input, default_model)
                    safe_console_print(response, "bold green")
                    log_usage("conversation")
                    
            except (EOFError, KeyboardInterrupt):
                print("\n👋 Goodbye!")
                break
            except Exception as e:
                print(f"❌ Error: {e}")
                logger.error(f"Main loop error: {e}")
                
    finally:        # Save conversation history and search data on exit
        save_conversation_history()
        save_search_data()

# =============================
# HTTP API (Flask, optional)
# =============================
# Optional dependency: install with 'pip install flask' for HTTP API
try:
    from flask import Flask, request, jsonify  # type: ignore[import-untyped]
    app = Flask(__name__)  # type: ignore[misc]
    
    @app.route('/api/command', methods=['POST'])  # type: ignore[misc]
    def api_command() -> Any:
        data = request.json or {}  # type: ignore[misc]
        cmd = str(data.get('command', '')) if data else ''  # type: ignore[misc]
        # Note: This would need a proper command execution function
        result = f"Command executed: {cmd}"
        return jsonify({"result": result})  # type: ignore[misc]
    
    @app.route('/api/llm', methods=['POST'])  # type: ignore[misc]
    def api_llm() -> Any:
        data = request.json or {}  # type: ignore[misc]
        prompt = str(data.get('prompt', '')) if data else ''  # type: ignore[misc]
        model = str(data.get('model', DEFAULT_MODEL)) if data else DEFAULT_MODEL  # type: ignore[misc]
        response = call_ollama(prompt, model)  # type: ignore[misc]
        return jsonify({'response': response})  # type: ignore[misc]
    
    api_enabled = True
except ImportError:
    api_enabled = False

# Search data loaded in main() function to avoid duplicate loading

if __name__ == "__main__":
    main()
