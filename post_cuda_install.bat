@echo off
echo 🧪 CUDA Installation Verification Script
echo ==========================================

echo.
echo 1. Testing CUDA Compiler...
nvcc --version
if %errorlevel% neq 0 (
    echo ❌ NVCC not found. Please restart your terminal and try again.
    echo 💡 You may need to add CUDA to your PATH manually.
    pause
    exit /b 1
)

echo.
echo 2. Testing NVIDIA SMI...
nvidia-smi
if %errorlevel% neq 0 (
    echo ❌ nvidia-smi failed
    pause
    exit /b 1
)

echo.
echo 3. Testing Python CUDA Helper...
python cuda_install_helper.py verify

echo.
echo 4. Testing Ollama Performance (Post-CUDA)...
python cuda_install_helper.py test-ollama

echo.
echo ✅ CUDA Installation Verification Complete!
echo 💡 Compare the Ollama response time with the pre-installation time.
pause
