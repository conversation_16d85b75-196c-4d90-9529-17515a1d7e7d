# Setup Agent PowerShell Launcher
# This script launches the Setup Agent with enhanced error handling

param(
    [switch]$Verbose
)

# Set window title
$Host.UI.RawUI.WindowTitle = "🤖 Setup Agent Launcher"

Write-Host "🤖 Setup Agent Launcher" -ForegroundColor Cyan
Write-Host "=========================" -ForegroundColor Cyan
Write-Host ""

# Change to Setup Agent directory
$SetupAgentPath = "f:\SetupAgent"
if (Test-Path $SetupAgentPath) {
    Set-Location $SetupAgentPath
    Write-Host "✅ Changed to Setup Agent directory: $SetupAgentPath" -ForegroundColor Green
} else {
    Write-Host "❌ ERROR: Setup Agent directory not found: $SetupAgentPath" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Check if Python is available
try {
    $pythonVersion = python --version 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Python found: $pythonVersion" -ForegroundColor Green
    } else {
        throw "Python not found"
    }
} catch {
    Write-Host "❌ ERROR: Python is not installed or not in PATH" -ForegroundColor Red
    Write-Host "Please install Python 3.8+ and add it to your PATH" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

# Check if setup_agent.py exists
if (Test-Path "setup_agent.py") {
    Write-Host "✅ setup_agent.py found" -ForegroundColor Green
} else {
    Write-Host "❌ ERROR: setup_agent.py not found in current directory" -ForegroundColor Red
    Write-Host "Current directory: $(Get-Location)" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

# Check if requirements are met (optional)
if (Test-Path "requirements.txt") {
    Write-Host "📋 Requirements file found. Checking dependencies..." -ForegroundColor Yellow
    if ($Verbose) {
        pip show -q requests rich flask 2>$null
        if ($LASTEXITCODE -ne 0) {
            Write-Host "⚠️  Some optional dependencies may be missing. Run 'pip install -r requirements.txt' if needed." -ForegroundColor Yellow
        }
    }
}

Write-Host ""
Write-Host "🚀 Launching Setup Agent..." -ForegroundColor Green
Write-Host ""

# Launch the Setup Agent
try {
    python setup_agent.py
    $exitCode = $LASTEXITCODE
    
    if ($exitCode -ne 0) {
        Write-Host ""
        Write-Host "❌ Setup Agent exited with error code: $exitCode" -ForegroundColor Red
        Read-Host "Press Enter to exit"
    } else {
        Write-Host ""
        Write-Host "✅ Setup Agent exited normally" -ForegroundColor Green
    }
} catch {
    Write-Host ""
    Write-Host "❌ ERROR launching Setup Agent: $($_.Exception.Message)" -ForegroundColor Red
    Read-Host "Press Enter to exit"
}
