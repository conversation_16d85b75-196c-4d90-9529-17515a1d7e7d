@echo off
REM Setup Agent Launcher
REM This batch file launches the Setup Agent with proper environment

echo Starting Setup Agent...
echo.

REM Change to the Setup Agent directory
cd /d "f:\SetupAgent"

REM Check if Python is available
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.8+ and try again
    pause
    exit /b 1
)

REM Check if setup_agent.py exists
if not exist "setup_agent.py" (
    echo ERROR: setup_agent.py not found in current directory
    echo Current directory: %CD%
    pause
    exit /b 1
)

REM Launch the Setup Agent
echo Launching Setup Agent...
echo.
python setup_agent.py

REM Keep window open if there's an error
if %errorlevel% neq 0 (
    echo.
    echo Setup Agent exited with error code: %errorlevel%
    pause
)
