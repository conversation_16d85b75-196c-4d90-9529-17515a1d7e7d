#!/usr/bin/env python3
"""
🧪 Test Suite for Advanced Memory and Learning System
Comprehensive tests for the advanced memory capabilities of SetupAgent.
"""

import os
import sys
import json
import time
from datetime import datetime

# Add current directory to path
sys.path.insert(0, os.path.dirname(__file__))

def test_memory_initialization():
    """Test memory system initialization."""
    print("🔧 Testing memory system initialization...")
    
    try:
        from advanced_memory import AdvancedMemorySystem, MemoryType, SourceType
        
        # Load config
        with open('config.json', 'r') as f:
            config = json.load(f)
        
        memory_system = AdvancedMemorySystem(config)
        print("✅ Advanced memory system initialized successfully")
        
        # Test database connection
        stats = memory_system.get_memory_statistics()
        print(f"✅ Database connection working - {stats.get('overall', {}).get('total_memories', 0)} memories found")
        
        return memory_system
        
    except Exception as e:
        print(f"❌ Memory initialization failed: {e}")
        return None

def test_memory_storage(memory_system):
    """Test memory storage functionality."""
    print("\n💾 Testing memory storage...")
    
    try:
        from advanced_memory import MemoryType, SourceType
        
        # Test storing different types of memories
        test_memories = [
            {
                'type': MemoryType.FACTUAL_KNOWLEDGE,
                'content': 'Python is a high-level programming language',
                'source': SourceType.USER_INPUT,
                'tags': ['python', 'programming', 'language']
            },
            {
                'type': MemoryType.PROCEDURAL_KNOWLEDGE,
                'content': 'To install Python packages, use pip install package_name',
                'source': SourceType.USER_INPUT,
                'tags': ['python', 'pip', 'installation']
            },
            {
                'type': MemoryType.USER_PREFERENCE,
                'content': 'User prefers detailed explanations with examples',
                'source': SourceType.AI_GENERATED,
                'tags': ['preference', 'explanation_style']
            }
        ]
        
        stored_ids = []
        for memory in test_memories:
            memory_id = memory_system.store_memory(
                memory['type'],
                memory['content'],
                {'test': True},
                memory['source'],
                confidence=0.8,
                tags=memory['tags']
            )
            
            if memory_id:
                stored_ids.append(memory_id)
                print(f"✅ Stored {memory['type'].value}: {memory_id}")
            else:
                print(f"❌ Failed to store {memory['type'].value}")
        
        print(f"✅ Successfully stored {len(stored_ids)} memories")
        return stored_ids
        
    except Exception as e:
        print(f"❌ Memory storage test failed: {e}")
        return []

def test_memory_retrieval(memory_system):
    """Test memory retrieval functionality."""
    print("\n🔍 Testing memory retrieval...")
    
    try:
        from advanced_memory import MemoryType
        
        # Test semantic search
        memories = memory_system.retrieve_memories("Python programming", max_results=5)
        print(f"✅ Semantic search found {len(memories)} memories")
        
        for memory in memories[:2]:
            print(f"   • [{memory.memory_type.value}] {memory.content[:50]}... (confidence: {memory.confidence:.2f})")
        
        # Test filtered search
        python_memories = memory_system.retrieve_memories(
            "installation", 
            memory_type=MemoryType.PROCEDURAL_KNOWLEDGE,
            min_confidence=0.5
        )
        print(f"✅ Filtered search found {len(python_memories)} procedural memories")
        
        return len(memories) > 0
        
    except Exception as e:
        print(f"❌ Memory retrieval test failed: {e}")
        return False

def test_web_content_extraction(memory_system):
    """Test web content extraction."""
    print("\n🌐 Testing web content extraction...")
    
    try:
        # Test with a reliable documentation URL
        test_url = "https://docs.python.org/3/tutorial/introduction.html"
        
        print(f"   Extracting content from: {test_url}")
        web_content = memory_system.extract_web_content(test_url, timeout=15)
        
        if web_content:
            print(f"✅ Content extracted successfully")
            print(f"   Title: {web_content.title}")
            print(f"   Content length: {len(web_content.content)} characters")
            print(f"   Key facts: {len(web_content.key_facts)} found")
            print(f"   Code examples: {len(web_content.code_examples)} found")
            print(f"   Tags: {', '.join(web_content.tags)}")
            print(f"   Reliability: {web_content.source_reliability:.2f}")
            
            # Store the web content
            memory_id = memory_system.store_web_content(web_content)
            if memory_id:
                print(f"✅ Web content stored with ID: {memory_id}")
            
            return True
        else:
            print("⚠️  No content extracted (may be due to network or site restrictions)")
            return False
            
    except Exception as e:
        print(f"❌ Web content extraction test failed: {e}")
        return False

def test_conversation_storage(memory_system):
    """Test conversation storage and context retrieval."""
    print("\n💬 Testing conversation storage...")
    
    try:
        # Store test conversations
        conversations = [
            {
                'user': 'How do I install Python on Windows?',
                'ai': 'You can install Python on Windows by downloading the installer from python.org and running it. Make sure to check "Add Python to PATH" during installation.'
            },
            {
                'user': 'What is the difference between pip and conda?',
                'ai': 'pip is the standard package manager for Python, while conda is a more comprehensive package and environment manager that can handle non-Python dependencies as well.'
            },
            {
                'user': 'How do I create a virtual environment?',
                'ai': 'You can create a virtual environment using: python -m venv myenv. Then activate it with myenv\\Scripts\\activate on Windows or source myenv/bin/activate on Linux/Mac.'
            }
        ]
        
        stored_conversations = []
        for conv in conversations:
            conv_id = memory_system.store_conversation(
                conv['user'], 
                conv['ai'],
                context_used="test context",
                session_id="test_session"
            )
            
            if conv_id:
                stored_conversations.append(conv_id)
                print(f"✅ Stored conversation: {conv_id}")
        
        # Test intelligent context retrieval
        context = memory_system.get_intelligent_context("Python environment setup")
        if context:
            print(f"✅ Generated intelligent context ({len(context)} characters)")
            print("   Context preview:")
            print("   " + context[:200] + "...")
        else:
            print("⚠️  No context generated")
        
        return len(stored_conversations) > 0
        
    except Exception as e:
        print(f"❌ Conversation storage test failed: {e}")
        return False

def test_learning_patterns(memory_system):
    """Test learning pattern storage and retrieval."""
    print("\n🧠 Testing learning patterns...")
    
    try:
        # Store test learning patterns
        patterns = [
            {
                'type': 'question_pattern',
                'data': {
                    'question_type': 'how_to',
                    'topic': 'python_installation',
                    'frequency': 5
                }
            },
            {
                'type': 'command_pattern',
                'data': {
                    'command': 'pip install',
                    'success_rate': 0.9,
                    'common_errors': ['network timeout', 'permission denied']
                }
            }
        ]
        
        stored_patterns = []
        for pattern in patterns:
            pattern_id = memory_system.store_learning_pattern(
                pattern['type'], 
                pattern['data']
            )
            
            if pattern_id:
                stored_patterns.append(pattern_id)
                print(f"✅ Stored learning pattern: {pattern['type']}")
        
        # Test pattern identification
        frequent_patterns = memory_system.identify_frequent_patterns(min_frequency=1)
        print(f"✅ Found {len(frequent_patterns)} frequent patterns")
        
        for pattern in frequent_patterns[:2]:
            print(f"   • {pattern['pattern_type']}: frequency {pattern['frequency']}")
        
        return len(stored_patterns) > 0
        
    except Exception as e:
        print(f"❌ Learning patterns test failed: {e}")
        return False

def test_feedback_system(memory_system, stored_memory_ids):
    """Test feedback and learning system."""
    print("\n👍 Testing feedback system...")
    
    try:
        if not stored_memory_ids:
            print("⚠️  No stored memories to test feedback on")
            return False
        
        # Test positive feedback
        memory_id = stored_memory_ids[0]
        success = memory_system.record_feedback(memory_id, 5, "general")
        if success:
            print("✅ Positive feedback recorded")
        
        # Test learning from correction
        correction_id = memory_system.learn_from_correction(
            "Original incorrect response",
            "Corrected response with accurate information",
            "User provided correction"
        )
        
        if correction_id:
            print(f"✅ Learned from correction: {correction_id}")
        
        return success and bool(correction_id)
        
    except Exception as e:
        print(f"❌ Feedback system test failed: {e}")
        return False

def test_memory_maintenance(memory_system):
    """Test memory cleanup and maintenance."""
    print("\n🧹 Testing memory maintenance...")
    
    try:
        # Get initial statistics
        initial_stats = memory_system.get_memory_statistics()
        print(f"✅ Initial statistics retrieved")
        print(f"   Total memories: {initial_stats.get('overall', {}).get('total_memories', 0)}")
        
        # Test memory cleanup (won't actually clean much in test data)
        cleanup_stats = memory_system.cleanup_old_memories()
        print(f"✅ Memory cleanup completed: {cleanup_stats}")
        
        # Test memory consolidation
        consolidated = memory_system.consolidate_similar_memories(similarity_threshold=0.95)
        print(f"✅ Memory consolidation completed: {consolidated} memories consolidated")
        
        return True
        
    except Exception as e:
        print(f"❌ Memory maintenance test failed: {e}")
        return False

def test_integration():
    """Test integration with SetupAgent."""
    print("\n🔗 Testing integration...")
    
    try:
        from memory_integration import initialize_advanced_memory, patch_setup_agent
        
        # Load config
        with open('config.json', 'r') as f:
            config = json.load(f)
        
        # Initialize integration
        success = initialize_advanced_memory(config)
        if success:
            print("✅ Memory integration initialized")
        
        # Test patching (may fail if setup_agent not available)
        try:
            patch_success = patch_setup_agent()
            if patch_success:
                print("✅ SetupAgent patched successfully")
            else:
                print("⚠️  SetupAgent patching failed (may not be available)")
        except ImportError:
            print("⚠️  SetupAgent not available for patching test")
        
        return success
        
    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        return False

def main():
    """Run all advanced memory tests."""
    print("🧪 Advanced Memory System Test Suite")
    print("=" * 50)
    
    # Initialize memory system
    memory_system = test_memory_initialization()
    if not memory_system:
        print("❌ Cannot continue without memory system")
        return False
    
    # Run tests
    test_results = []
    
    stored_ids = test_memory_storage(memory_system)
    test_results.append(("Memory Storage", len(stored_ids) > 0))
    
    test_results.append(("Memory Retrieval", test_memory_retrieval(memory_system)))
    test_results.append(("Web Content Extraction", test_web_content_extraction(memory_system)))
    test_results.append(("Conversation Storage", test_conversation_storage(memory_system)))
    test_results.append(("Learning Patterns", test_learning_patterns(memory_system)))
    test_results.append(("Feedback System", test_feedback_system(memory_system, stored_ids)))
    test_results.append(("Memory Maintenance", test_memory_maintenance(memory_system)))
    test_results.append(("Integration", test_integration()))
    
    # Summary
    print("\n" + "=" * 50)
    print("🎉 Test Results Summary:")
    
    passed = 0
    for test_name, result in test_results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(test_results)} tests passed")
    
    if passed == len(test_results):
        print("🎉 All tests passed! Advanced memory system is ready.")
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
    
    return passed == len(test_results)

if __name__ == "__main__":
    main()
