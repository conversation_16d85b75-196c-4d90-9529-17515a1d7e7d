#!/usr/bin/env python3
"""
🧠 Embeddings and Vector Search Module for Setup Agent
Provides embedding generation and similarity search capabilities using multiple backends.
"""

import os
import json
import sqlite3
import logging
from typing import Dict, Any, List, Optional, TYPE_CHECKING
from datetime import datetime

logger = logging.getLogger(__name__)

# Type checking imports
if TYPE_CHECKING:
    import numpy as np
    import faiss
    from sentence_transformers import SentenceTransformer
    import chromadb

# Import optional dependencies with proper typing
numpy: Optional[Any] = None
faiss: Optional[Any] = None  # type: ignore[reportMissingTypeStubs]
sentence_transformers: Optional[Any] = None
chromadb: Optional[Any] = None

try:
    import numpy as np
    numpy = np
except ImportError:
    logger.warning("NumPy not available - some embedding features will be disabled")

try:
    import faiss  # type: ignore[reportMissingTypeStubs]
    # Check if GPU is available for FAISS
    if hasattr(faiss, 'get_num_gpus') and faiss.get_num_gpus() > 0:
        logger.info(f"🚀 FAISS GPU acceleration available with {faiss.get_num_gpus()} GPU(s)")
    else:
        logger.info("💻 FAISS using CPU (no GPU acceleration available)")
except ImportError:
    logger.warning("FAISS not available - FAISS vector search will be disabled")

try:
    from sentence_transformers import SentenceTransformer
    sentence_transformers = SentenceTransformer
except ImportError:
    logger.warning("SentenceTransformers not available - local embedding models will be disabled")

try:
    import chromadb
except ImportError:
    logger.warning("ChromaDB not available - ChromaDB vector search will be disabled")

# Configuration
EMBEDDINGS_DIR = os.path.join(os.path.dirname(__file__), 'embeddings_data')
VECTOR_DB_FILE = os.path.join(EMBEDDINGS_DIR, 'vector_store.db')
FAISS_INDEX_FILE = os.path.join(EMBEDDINGS_DIR, 'faiss_index.bin')
CHROMA_DB_DIR = os.path.join(EMBEDDINGS_DIR, 'chroma_db')

# Ensure embeddings directory exists
os.makedirs(EMBEDDINGS_DIR, exist_ok=True)

class EmbeddingManager:
    """Manages embeddings with multiple backend support."""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.embedding_model = None
        self.vector_store = None
        self.embedding_backend = config.get('embeddings', {}).get('backend', 'sentence_transformers')
        self.vector_backend = config.get('embeddings', {}).get('vector_store', 'sqlite')
        self.max_context_results = config.get('embeddings', {}).get('max_context_results', 5)
        
        self._init_embedding_model()
        self._init_vector_store()
    
    def _init_embedding_model(self):
        """Initialize the embedding model based on configuration."""
        try:
            if self.embedding_backend == 'nomic-ollama':
                # Use Ollama with nomic-embed-text model
                self.embedding_model = OllamaEmbeddings(
                    model_name=self.config.get('embeddings', {}).get('model', 'nomic-embed-text'),
                    ollama_url=self.config.get('ollama', {}).get('url', 'http://localhost:11434'),
                    timeout=self.config.get('ollama', {}).get('timeout', 90)
                )
                logger.info("Initialized Ollama embeddings with nomic-embed-text")
                
            elif self.embedding_backend == 'sentence_transformers' and sentence_transformers:
                # Use SentenceTransformers for local embeddings
                model_name = self.config.get('embeddings', {}).get('model', 'all-MiniLM-L6-v2')
                self.embedding_model = sentence_transformers(model_name)
                logger.info(f"Initialized SentenceTransformers with {model_name}")
                
            elif self.embedding_backend == 'openai':
                # OpenAI embeddings (requires API key)
                self.embedding_model = OpenAIEmbeddings(
                    api_key=self.config.get('openai', {}).get('api_key'),
                    model=self.config.get('embeddings', {}).get('model', 'text-embedding-3-small')
                )
                logger.info("Initialized OpenAI embeddings")
                
            else:
                logger.warning(f"Unknown embedding backend: {self.embedding_backend}")
                
        except Exception as e:
            logger.error(f"Failed to initialize embedding model: {e}")
    
    def _init_vector_store(self):
        """Initialize the vector store based on configuration."""
        try:
            if self.vector_backend == 'faiss' and faiss and numpy:
                self.vector_store = FAISSVectorStore(FAISS_INDEX_FILE)
                logger.info("Initialized FAISS vector store")
                
            elif self.vector_backend == 'chromadb' and chromadb:
                self.vector_store = ChromaVectorStore(CHROMA_DB_DIR)
                logger.info("Initialized ChromaDB vector store")
                
            elif self.vector_backend == 'sqlite':
                self.vector_store = SQLiteVectorStore(VECTOR_DB_FILE)
                logger.info("Initialized SQLite vector store")
                
            else:
                logger.warning(f"Unknown vector backend: {self.vector_backend}")
                
        except Exception as e:
            logger.error(f"Failed to initialize vector store: {e}")
    
    def get_embedding(self, text: str) -> List[float]:
        """Generate embedding for given text."""
        if not self.embedding_model:
            return []

        try:
            if sentence_transformers and hasattr(self.embedding_model, 'encode'):
                # SentenceTransformers
                embedding: Any = self.embedding_model.encode(text)
                if numpy and hasattr(embedding, 'tolist'):
                    return embedding.tolist()
                elif isinstance(embedding, list):
                    return embedding
                else:
                    return []

            elif hasattr(self.embedding_model, 'get_embedding'):
                # Custom embedding classes
                result = self.embedding_model.get_embedding(text)
                return result if isinstance(result, list) else []

            else:
                logger.warning("No valid embedding method available")
                return []

        except Exception as e:
            logger.error(f"Failed to generate embedding: {e}")
            return []
    
    def store_interaction(self, prompt: str, response: str, metadata: Optional[Dict[str, Any]] = None):
        """Store an interaction with embeddings for future retrieval."""
        if not self.vector_store or not self.embedding_model:
            return
        
        try:
            # Generate embeddings for both prompt and response
            prompt_embedding = self.get_embedding(prompt)
            response_embedding = self.get_embedding(response)
            
            if not prompt_embedding or not response_embedding:
                return
            
            # Prepare metadata
            interaction_metadata: Dict[str, Any] = {
                'timestamp': datetime.now().isoformat(),
                'prompt': prompt,
                'response': response,
                'type': 'interaction',
                **(metadata or {})
            }
            
            # Store both prompt and response embeddings
            prompt_id = self._generate_id(prompt)
            response_id = self._generate_id(response)
            
            self.vector_store.add_vector(
                prompt_id, 
                prompt_embedding, 
                {**interaction_metadata, 'content_type': 'prompt', 'content': prompt}
            )
            
            self.vector_store.add_vector(
                response_id, 
                response_embedding, 
                {**interaction_metadata, 'content_type': 'response', 'content': response}
            )
            
            logger.debug(f"Stored interaction embeddings: {prompt_id}, {response_id}")
            
        except Exception as e:
            logger.error(f"Failed to store interaction: {e}")
    
    def find_similar_interactions(self, query: str, max_results: Optional[int] = None) -> List[Dict[str, Any]]:
        """Find similar past interactions based on the query."""
        if not self.vector_store or not self.embedding_model:
            return []

        try:
            max_results = max_results or self.max_context_results
            query_embedding = self.get_embedding(query)

            if not query_embedding:
                return []

            # Search for similar vectors
            search_count = max_results * 2 if max_results is not None else 10
            results = self.vector_store.search_similar(query_embedding, search_count)  # Get more to filter

            # Filter and format results
            formatted_results: List[Dict[str, Any]] = []
            seen_interactions: set[str] = set()

            for result in results:
                metadata = result.get('metadata', {})
                interaction_key = f"{metadata.get('timestamp', '')}_{metadata.get('type', '')}"

                if interaction_key not in seen_interactions:
                    formatted_results.append({
                        'prompt': metadata.get('prompt', ''),
                        'response': metadata.get('response', ''),
                        'timestamp': metadata.get('timestamp', ''),
                        'similarity': result.get('similarity', 0.0),
                        'metadata': metadata
                    })
                    seen_interactions.add(interaction_key)

                if max_results is not None and len(formatted_results) >= max_results:
                    break

            return formatted_results
            
        except Exception as e:
            logger.error(f"Failed to find similar interactions: {e}")
            return []
    
    def get_context_for_query(self, query: str, max_results: Optional[int] = None) -> str:
        """Get formatted context from similar past interactions."""
        similar = self.find_similar_interactions(query, max_results)
        
        if not similar:
            return ""
        
        context_parts = ["📚 **Relevant Past Context:**\n"]
        
        for i, item in enumerate(similar, 1):
            similarity_pct = item.get('similarity', 0) * 100
            timestamp = item.get('timestamp', '')
            
            context_parts.append(f"**Context {i}** (Similarity: {similarity_pct:.1f}%)")
            if timestamp:
                context_parts.append(f"*Time: {timestamp[:19]}*")
            
            context_parts.append(f"**Previous Question:** {item.get('prompt', '')}")
            context_parts.append(f"**Previous Answer:** {item.get('response', '')}")
            context_parts.append("")
        
        return "\n".join(context_parts)
    
    def store_command_execution(self, command: str, output: str, success: bool, metadata: Optional[Dict[str, Any]] = None):
        """Store command execution for future reference."""
        if not self.vector_store or not self.embedding_model:
            return
        
        try:
            # Create searchable text combining command and output
            searchable_text = f"Command: {command}\nOutput: {output[:500]}"  # Limit output length
            embedding = self.get_embedding(searchable_text)
            
            if not embedding:
                return
            
            command_metadata: Dict[str, Any] = {
                'timestamp': datetime.now().isoformat(),
                'command': command,
                'output': output,
                'success': success,
                'type': 'command_execution',
                'content': searchable_text,
                **(metadata or {})
            }
            
            command_id = self._generate_id(f"{command}_{datetime.now().isoformat()}")
            self.vector_store.add_vector(command_id, embedding, command_metadata)
            
            logger.debug(f"Stored command execution: {command_id}")
            
        except Exception as e:
            logger.error(f"Failed to store command execution: {e}")
    
    def find_similar_commands(self, command: str, max_results: int = 3) -> List[Dict[str, Any]]:
        """Find similar past command executions."""
        if not self.vector_store or not self.embedding_model:
            return []
        
        try:
            query_embedding = self.get_embedding(f"Command: {command}")
            
            if not query_embedding:
                return []
            
            results = self.vector_store.search_similar(query_embedding, max_results)
            
            formatted_results: List[Dict[str, Any]] = []
            for result in results:
                metadata = result.get('metadata', {})
                if metadata.get('type') == 'command_execution':
                    formatted_results.append({
                        'command': metadata.get('command', ''),
                        'output': metadata.get('output', ''),
                        'success': metadata.get('success', False),
                        'timestamp': metadata.get('timestamp', ''),
                        'similarity': result.get('similarity', 0.0)
                    })
            
            return formatted_results
            
        except Exception as e:
            logger.error(f"Failed to find similar commands: {e}")
            return []
    
    def _generate_id(self, text: str) -> str:
        """Generate unique ID for text."""
        import hashlib
        return hashlib.md5(text.encode()).hexdigest()

# Vector Store Implementations

class SQLiteVectorStore:
    """SQLite-based vector store with cosine similarity search."""
    
    def __init__(self, db_path: str):
        self.db_path = db_path
        self._init_db()
    
    def _init_db(self):
        """Initialize SQLite database with vector storage."""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS vectors (
                id TEXT PRIMARY KEY,
                embedding BLOB,
                metadata TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def add_vector(self, vector_id: str, embedding: List[float], metadata: Dict[str, Any]):
        """Add a vector with metadata to the store."""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Convert embedding to bytes
        if numpy:
            embedding_bytes = numpy.array(embedding, dtype=numpy.float32).tobytes()
        else:
            embedding_bytes = json.dumps(embedding).encode()
        
        cursor.execute('''
            INSERT OR REPLACE INTO vectors (id, embedding, metadata)
            VALUES (?, ?, ?)
        ''', (vector_id, embedding_bytes, json.dumps(metadata)))
        
        conn.commit()
        conn.close()
    
    def search_similar(self, query_embedding: List[float], max_results: int = 5) -> List[Dict[str, Any]]:
        """Search for similar vectors using cosine similarity."""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('SELECT id, embedding, metadata FROM vectors')
        results: List[Dict[str, Any]] = []
        
        if not numpy:
            # Fallback without numpy
            logger.warning("NumPy not available - similarity search may be slow")
            all_vectors = cursor.fetchall()[:max_results]  # Just return first N results
            for vector_id, embedding_bytes, metadata_str in all_vectors:
                results.append({
                    'id': vector_id,
                    'similarity': 0.5,  # Placeholder similarity
                    'metadata': json.loads(metadata_str)
                })
        else:
            # Use numpy for proper cosine similarity
            query_vec = numpy.array(query_embedding, dtype=numpy.float32)
            query_norm = numpy.linalg.norm(query_vec)
            
            for vector_id, embedding_bytes, metadata_str in cursor.fetchall():
                try:
                    stored_vec = numpy.frombuffer(embedding_bytes, dtype=numpy.float32)
                    stored_norm = numpy.linalg.norm(stored_vec)
                    
                    if query_norm > 0 and stored_norm > 0:
                        similarity = numpy.dot(query_vec, stored_vec) / (query_norm * stored_norm)
                        results.append({
                            'id': vector_id,
                            'similarity': float(similarity),
                            'metadata': json.loads(metadata_str)
                        })
                except Exception as e:
                    logger.debug(f"Error processing vector {vector_id}: {e}")
                    continue
            
            # Sort by similarity and return top results
            results.sort(key=lambda x: x['similarity'], reverse=True)  # type: ignore[reportUnknownLambdaType]
            results = results[:max_results]
        
        conn.close()
        return results

class FAISSVectorStore:
    """FAISS-based vector store for fast similarity search."""

    def __init__(self, index_path: str):
        self.index_path = index_path
        self.index: Optional[Any] = None
        self.metadata_store: Dict[str, Dict[str, Any]] = {}
        self.vector_ids: List[str] = []
        self._init_index()
    
    def _init_index(self):
        """Initialize FAISS index."""
        if not faiss or not numpy:
            raise RuntimeError("FAISS and NumPy are required for FAISSVectorStore")
        
        # Try to load existing index
        if os.path.exists(self.index_path):
            try:
                self.index = faiss.read_index(self.index_path)
                self._load_metadata()
                vector_count = self.index.ntotal if self.index else 0
                logger.info(f"Loaded existing FAISS index with {vector_count} vectors")
            except Exception as e:
                logger.warning(f"Failed to load existing index: {e}")
                self._create_new_index()
        else:
            self._create_new_index()
    
    def _create_new_index(self):
        """Create a new FAISS index with GPU acceleration if available."""
        if not faiss:
            raise RuntimeError("FAISS is required but not available")

        # Use 768 dimensions for nomic-embed-text model (default)
        dimension = 768

        # Create base index
        cpu_index = faiss.IndexFlatIP(dimension)  # Inner product for cosine similarity

        # Try to move to GPU if available
        if hasattr(faiss, 'get_num_gpus') and faiss.get_num_gpus() > 0:
            try:
                # Use GPU 0 (can be configured)
                gpu_resource = faiss.StandardGpuResources()
                self.index = faiss.index_cpu_to_gpu(gpu_resource, 0, cpu_index)
                logger.info("🚀 FAISS index created with GPU acceleration")
            except Exception as e:
                logger.warning(f"Failed to create GPU index, falling back to CPU: {e}")
                self.index = cpu_index
        else:
            self.index = cpu_index
            logger.info("💻 FAISS index created with CPU")

        self.metadata_store = {}
        self.vector_ids = []
    
    def _save_index(self):
        """Save FAISS index and metadata."""
        if self.index and faiss:
            faiss.write_index(self.index, self.index_path)

            metadata_path = self.index_path + '.metadata'
            with open(metadata_path, 'w') as f:
                json.dump({
                    'metadata_store': self.metadata_store,
                    'vector_ids': self.vector_ids
                }, f)
    
    def _load_metadata(self):
        """Load metadata for existing index."""
        metadata_path = self.index_path + '.metadata'
        if os.path.exists(metadata_path):
            try:
                with open(metadata_path, 'r') as f:
                    data = json.load(f)
                    self.metadata_store = data.get('metadata_store', {})
                    self.vector_ids = data.get('vector_ids', [])
            except Exception as e:
                logger.warning(f"Failed to load metadata: {e}")
                self.metadata_store = {}
                self.vector_ids = []
    
    def add_vector(self, vector_id: str, embedding: List[float], metadata: Dict[str, Any]):
        """Add a vector to the FAISS index."""
        if not self.index or not numpy:
            return

        try:
            # Normalize embedding for cosine similarity
            vec = numpy.array(embedding, dtype=numpy.float32).reshape(1, -1)
            vec = vec / numpy.linalg.norm(vec)

            self.index.add(vec)
            self.vector_ids.append(vector_id)
            self.metadata_store[vector_id] = metadata

            # Save periodically
            if len(self.vector_ids) % 10 == 0:
                self._save_index()

        except Exception as e:
            logger.error(f"Failed to add vector to FAISS: {e}")
    
    def search_similar(self, query_embedding: List[float], max_results: int = 5) -> List[Dict[str, Any]]:
        """Search for similar vectors in FAISS index."""
        if not self.index or self.index.ntotal == 0 or not numpy:
            return []

        try:
            # Normalize query embedding
            query_vec = numpy.array(query_embedding, dtype=numpy.float32).reshape(1, -1)
            query_vec = query_vec / numpy.linalg.norm(query_vec)

            # Search
            similarities, indices = self.index.search(query_vec, min(max_results, self.index.ntotal))
            
            results: List[Dict[str, Any]] = []
            for similarity, idx in zip(similarities[0], indices[0]):
                if idx >= 0 and idx < len(self.vector_ids):
                    vector_id: str = self.vector_ids[idx]
                    metadata = self.metadata_store.get(vector_id, {})
                    results.append({
                        'id': vector_id,
                        'similarity': float(similarity),
                        'metadata': metadata
                    })
            
            return results
            
        except Exception as e:
            logger.error(f"Failed to search FAISS index: {e}")
            return []

class ChromaVectorStore:
    """ChromaDB-based vector store."""
    
    def __init__(self, db_path: str):
        if not chromadb:
            raise RuntimeError("ChromaDB is required for ChromaVectorStore")
        
        self.client = chromadb.PersistentClient(path=db_path)
        self.collection = self.client.get_or_create_collection(
            name="setup_agent_embeddings",
            metadata={"description": "Setup Agent conversation and command embeddings"}
        )
    
    def add_vector(self, vector_id: str, embedding: List[float], metadata: Dict[str, Any]):
        """Add a vector to ChromaDB."""
        try:
            self.collection.add(
                ids=[vector_id],
                embeddings=[embedding],
                metadatas=[metadata],
                documents=[metadata.get('content', '')]
            )
        except Exception as e:
            logger.error(f"Failed to add vector to ChromaDB: {e}")
    
    def search_similar(self, query_embedding: List[float], max_results: int = 5) -> List[Dict[str, Any]]:
        """Search for similar vectors in ChromaDB."""
        try:
            results = self.collection.query(
                query_embeddings=[query_embedding],
                n_results=max_results,
                include=['metadatas', 'distances']
            )
            
            formatted_results: List[Dict[str, Any]] = []
            if results['ids']:
                for i, vector_id in enumerate(results['ids'][0]):
                    metadata: Dict[str, Any] = results['metadatas'][0][i] if results.get('metadatas') else {}
                    distance = results['distances'][0][i] if results.get('distances') else 1.0

                    # Convert distance to similarity (ChromaDB uses distance, we want similarity)
                    similarity = 1.0 - distance

                    formatted_results.append({
                        'id': vector_id,
                        'similarity': similarity,
                        'metadata': metadata
                    })
            
            return formatted_results
            
        except Exception as e:
            logger.error(f"Failed to search ChromaDB: {e}")
            return []

# Embedding Model Implementations

class OllamaEmbeddings:
    """Ollama-based embeddings using nomic-embed-text or other models."""

    def __init__(self, model_name: str = 'nomic-embed-text', ollama_url: str = 'http://localhost:11434', timeout: int = 90):
        self.model_name = model_name
        self.ollama_url = ollama_url.rstrip('/')
        self.embed_endpoint = f"{self.ollama_url}/api/embeddings"
        self.timeout = timeout
        self._test_connection()

    def _test_connection(self):
        """Test connection to Ollama and ensure model is available."""
        try:
            import requests

            # Check if Ollama is running
            health_response = requests.get(f"{self.ollama_url}/api/tags", timeout=5)
            health_response.raise_for_status()

            # Check if the embedding model is available
            models = health_response.json().get('models', [])
            model_names = [model.get('name', '').split(':')[0] for model in models]

            if self.model_name not in model_names:
                logger.warning(f"Model {self.model_name} not found in Ollama. Available models: {model_names}")
                logger.info(f"To install {self.model_name}, run: ollama pull {self.model_name}")
            else:
                logger.info(f"✅ Ollama model {self.model_name} is available")

        except Exception as e:
            logger.warning(f"Could not verify Ollama connection: {e}")

    def get_embedding(self, text: str) -> List[float]:
        """Get embedding from Ollama."""
        try:
            import requests

            # Clean and prepare text
            text = text.strip()
            if not text:
                return []

            payload = {
                "model": self.model_name,
                "prompt": text
            }

            response = requests.post(
                self.embed_endpoint,
                json=payload,
                timeout=self.timeout
            )
            response.raise_for_status()

            result = response.json()
            embedding = result.get('embedding', [])

            if not embedding:
                logger.warning(f"Empty embedding returned for text: {text[:50]}...")
                return []

            logger.debug(f"Generated embedding of dimension {len(embedding)} for text: {text[:50]}...")
            return embedding

        except Exception as e:
            logger.error(f"Failed to get Ollama embedding: {e}")
            return []

class OpenAIEmbeddings:
    """OpenAI-based embeddings."""
    
    def __init__(self, api_key: str, model: str = 'text-embedding-3-small'):
        self.api_key = api_key
        self.model = model
        self.endpoint = "https://api.openai.com/v1/embeddings"
    
    def get_embedding(self, text: str) -> List[float]:
        """Get embedding from OpenAI."""
        try:
            import requests
            
            headers = {
                'Authorization': f'Bearer {self.api_key}',
                'Content-Type': 'application/json'
            }
            
            payload = {
                "model": self.model,
                "input": text
            }
            
            response = requests.post(
                self.endpoint,
                headers=headers,
                json=payload,
                timeout=30
            )
            response.raise_for_status()
            
            result = response.json()
            return result['data'][0]['embedding']
            
        except Exception as e:
            logger.error(f"Failed to get OpenAI embedding: {e}")
            return []
