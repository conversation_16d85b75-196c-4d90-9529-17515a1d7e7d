#!/usr/bin/env python3
"""
Test Qwen models for optimal SetupAgent performance on Quadro P1000.
"""

import time
import json
import requests
from pathlib import Path

# Qwen models to test (in order of preference)
QWEN_MODELS = [
    "qwen2.5:7b-instruct",  # Primary - Instruction following
    "qwen3:8b",             # Advanced reasoning
    "qwen2.5:3b",           # Fast inference
]

OLLAMA_URL = "http://localhost:11434/api/generate"

def test_qwen_model_performance(model_name: str) -> dict:
    """Test a Qwen model's performance with SetupAgent-specific prompts."""
    print(f"\n🧪 Testing {model_name} for SetupAgent tasks...")
    
    # SetupAgent-specific test prompt
    test_prompt = """You are an AI assistant helping with software development. 
    Create a Python function that reads a JSON config file and validates it has required fields: 'name', 'version', 'dependencies'. 
    Return the function with error handling."""
    
    payload = {
        "model": model_name,
        "prompt": test_prompt,
        "stream": False,
        "options": {
            "temperature": 0.6,
            "num_ctx": 8192 if "7b" in model_name or "8b" in model_name else 4096,
            "num_batch": 1024 if "7b" in model_name or "8b" in model_name else 512,
            "num_gpu": -1,
            "main_gpu": 0,
            "low_vram": False
        }
    }
    
    try:
        start_time = time.time()
        response = requests.post(OLLAMA_URL, json=payload, timeout=90)
        end_time = time.time()
        
        if response.status_code == 200:
            result = response.json()
            response_time = end_time - start_time
            response_text = result.get("response", "")
            tokens = len(response_text.split())
            
            # Check if response contains code (good for SetupAgent)
            has_code = "def " in response_text or "import " in response_text
            has_error_handling = "try:" in response_text or "except" in response_text
            
            return {
                "model": model_name,
                "success": True,
                "response_time": round(response_time, 2),
                "tokens_generated": tokens,
                "tokens_per_second": round(tokens / response_time, 1),
                "has_code": has_code,
                "has_error_handling": has_error_handling,
                "quality_score": (tokens / response_time) * (2 if has_code else 1) * (1.5 if has_error_handling else 1),
                "response_preview": response_text[:200] + "..." if len(response_text) > 200 else response_text
            }
        else:
            return {
                "model": model_name,
                "success": False,
                "error": f"HTTP {response.status_code}: {response.text[:100]}"
            }
            
    except Exception as e:
        return {
            "model": model_name,
            "success": False,
            "error": str(e)[:100]
        }

def check_qwen_models_availability() -> list:
    """Check which Qwen models are available."""
    print("🔍 Checking available Qwen models...")
    
    try:
        response = requests.get("http://localhost:11434/api/tags", timeout=10)
        if response.status_code == 200:
            models_data = response.json()
            available_models = [model["name"] for model in models_data.get("models", [])]
            
            qwen_available = []
            for model in QWEN_MODELS:
                if model in available_models:
                    qwen_available.append(model)
                    print(f"✅ {model} - Available")
                else:
                    print(f"❌ {model} - Not found")
            
            return qwen_available
        else:
            print(f"❌ Failed to get model list: HTTP {response.status_code}")
            return []
            
    except Exception as e:
        print(f"❌ Error checking models: {e}")
        return []

def recommend_optimal_qwen_model(test_results: list) -> str:
    """Recommend the optimal Qwen model based on test results."""
    if not test_results:
        return "qwen2.5:7b-instruct"  # Default fallback
    
    # Filter successful tests
    successful_tests = [r for r in test_results if r["success"]]
    
    if not successful_tests:
        return "qwen2.5:7b-instruct"  # Default fallback
    
    # Sort by quality score (highest first)
    successful_tests.sort(key=lambda x: x.get("quality_score", 0), reverse=True)
    
    return successful_tests[0]["model"]

def update_config_with_qwen_model(optimal_model: str):
    """Update config.json with the optimal Qwen model."""
    config_path = Path("config.json")
    
    if not config_path.exists():
        print("❌ config.json not found")
        return
    
    try:
        with open(config_path, 'r') as f:
            config = json.load(f)
        
        # Update default model
        config["ollama"]["default_model"] = optimal_model
        
        # Add Qwen optimization note
        config["ollama"]["qwen_optimized"] = True
        config["ollama"]["last_qwen_test"] = time.strftime("%Y-%m-%d %H:%M:%S")
        
        with open(config_path, 'w') as f:
            json.dump(config, f, indent=2)
        
        print(f"✅ Updated config.json with optimal Qwen model: {optimal_model}")
        
    except Exception as e:
        print(f"❌ Failed to update config: {e}")

def main():
    """Main Qwen testing function."""
    print("🎯 SetupAgent Qwen Model Performance Test")
    print("🚀 Optimized for Quadro P1000 4GB VRAM")
    print("=" * 60)
    
    # Check Ollama connection
    try:
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code != 200:
            print("❌ Ollama server not responding")
            return
    except Exception as e:
        print(f"❌ Cannot connect to Ollama: {e}")
        return
    
    print("✅ Ollama server is running")
    
    # Check available Qwen models
    available_models = check_qwen_models_availability()
    
    if not available_models:
        print("\n❌ No Qwen models found!")
        print("💡 Install recommended Qwen models:")
        for model in QWEN_MODELS:
            print(f"   ollama pull {model}")
        return
    
    # Test available Qwen models
    print(f"\n🧪 Testing {len(available_models)} Qwen models for SetupAgent...")
    test_results = []
    
    for model in available_models:
        result = test_qwen_model_performance(model)
        test_results.append(result)
        
        if result["success"]:
            print(f"✅ {model}:")
            print(f"   • Response time: {result['response_time']}s")
            print(f"   • Speed: {result['tokens_per_second']} tokens/sec")
            print(f"   • Code generation: {'Yes' if result['has_code'] else 'No'}")
            print(f"   • Error handling: {'Yes' if result['has_error_handling'] else 'No'}")
            print(f"   • Quality score: {result['quality_score']:.1f}")
        else:
            print(f"❌ {model}: {result['error']}")
    
    # Recommend optimal Qwen model
    optimal_model = recommend_optimal_qwen_model(test_results)
    
    print(f"\n🎯 QWEN MODEL RECOMMENDATION")
    print("=" * 40)
    print(f"🥇 Optimal Qwen model for SetupAgent: {optimal_model}")
    
    # Show performance comparison
    successful_tests = [r for r in test_results if r["success"]]
    if len(successful_tests) > 1:
        print(f"\n📊 Qwen Performance Comparison:")
        for result in sorted(successful_tests, key=lambda x: x.get("quality_score", 0), reverse=True):
            print(f"   {result['model']}: {result['tokens_per_second']} tok/s, Quality: {result['quality_score']:.1f}")
    
    # Show model capabilities
    print(f"\n🔧 SetupAgent Capabilities:")
    for result in successful_tests:
        if result["model"] == optimal_model:
            print(f"   • Code Generation: {'✅' if result['has_code'] else '❌'}")
            print(f"   • Error Handling: {'✅' if result['has_error_handling'] else '❌'}")
            print(f"   • Response Speed: {result['tokens_per_second']} tokens/sec")
            break
    
    # Ask to update config
    update_choice = input(f"\n🔧 Update config.json to use {optimal_model}? (y/n): ").lower().strip()
    if update_choice == 'y':
        update_config_with_qwen_model(optimal_model)
    
    print(f"\n✅ Qwen testing complete! Use '{optimal_model}' for best SetupAgent performance.")
    print(f"💡 Your Quadro P1000 is optimized for Qwen models with 4GB VRAM usage.")

if __name__ == "__main__":
    main()