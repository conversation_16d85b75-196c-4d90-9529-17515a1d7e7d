@echo off
REM SetupAgent Issue Tracker - Windows Batch Script
REM Usage: track_issues.bat [command]

if "%1"=="" goto dashboard
if "%1"=="help" goto help
if "%1"=="dashboard" goto dashboard
if "%1"=="report" goto report
if "%1"=="verify-all" goto verify_all
if "%1"=="list" goto list
if "%1"=="pending" goto pending
if "%1"=="resolved" goto resolved

:dashboard
echo 🔧 SetupAgent Issue Dashboard
echo =============================
python issue_tracker.py --dashboard
goto end

:report
echo 📊 Generating Progress Report...
python issue_tracker.py --report > issue_progress_report.txt
echo ✅ Report saved to issue_progress_report.txt
type issue_progress_report.txt
goto end

:verify_all
echo 🧪 Running All Verification Checks...
python issue_tracker.py --verify-all
goto end

:list
echo 📋 All Issues:
python issue_tracker.py --list all
goto end

:pending
echo ⏳ Pending Issues:
python issue_tracker.py --list pending
goto end

:resolved
echo ✅ Resolved Issues:
python issue_tracker.py --list resolved
goto end

:help
echo 🔧 SetupAgent Issue Tracker Commands:
echo.
echo   track_issues.bat                 - Show dashboard
echo   track_issues.bat dashboard       - Show dashboard
echo   track_issues.bat report          - Generate progress report
echo   track_issues.bat verify-all      - Run all verification checks
echo   track_issues.bat list            - List all issues
echo   track_issues.bat pending         - List pending issues
echo   track_issues.bat resolved        - List resolved issues
echo   track_issues.bat help            - Show this help
echo.
echo Advanced usage:
echo   python issue_tracker.py --verify ISSUE-001
echo   python issue_tracker.py --update ISSUE-001 resolved "Fixed the bug"
echo.
goto end

:end