"""
Tests for LLM provider factory.
"""

import pytest
from unittest.mock import Mock, patch

from setup_agent.llm.factory import LL<PERSON>roviderFactory
from setup_agent.llm.base import BaseLLMProvider
from setup_agent.core.exceptions import LLMProviderError


class MockProvider(BaseLLMProvider):
    """Mock LLM provider for testing."""
    
    def __init__(self, config):
        super().__init__(config)
        self._available = config.get('available', True)
    
    def is_available(self):
        return self._available
    
    def generate(self, prompt, model=None, **kwargs):
        from setup_agent.llm.base import LLMResponse
        return LLMResponse(
            content=f"Mock response to: {prompt}",
            model=model or "mock-model",
            provider="mock",
            metadata={}
        )
    
    def generate_stream(self, prompt, model=None, **kwargs):
        from setup_agent.llm.base import LLMStreamResponse
        yield LLMStreamResponse(
            content=f"Mock response to: {prompt}",
            is_complete=True,
            metadata={}
        )
    
    def list_models(self):
        return ["mock-model-1", "mock-model-2"]


class TestLLMProviderFactory:
    """Test LLM provider factory."""
    
    def setup_method(self):
        """Setup for each test."""
        # Clear factory state
        LLMProviderFactory._instances.clear()
        # Remove any test providers
        test_providers = ['mock', 'mock_available', 'mock_unavailable']
        for provider in test_providers:
            if provider in LLMProviderFactory._providers:
                del LLMProviderFactory._providers[provider]
    
    def test_register_provider(self):
        """Test provider registration."""
        LLMProviderFactory.register_provider('mock', MockProvider)
        assert 'mock' in LLMProviderFactory._providers
    
    def test_get_provider(self):
        """Test getting a provider instance."""
        LLMProviderFactory.register_provider('mock', MockProvider)
        
        with patch('setup_agent.core.config.config') as mock_config:
            mock_config.get.return_value = {'available': True}
            
            provider = LLMProviderFactory.get_provider('mock')
            assert isinstance(provider, MockProvider)
            assert provider.is_available()
    
    def test_get_provider_caching(self):
        """Test that providers are cached."""
        LLMProviderFactory.register_provider('mock', MockProvider)
        
        with patch('setup_agent.core.config.config') as mock_config:
            mock_config.get.return_value = {'available': True}
            
            provider1 = LLMProviderFactory.get_provider('mock')
            provider2 = LLMProviderFactory.get_provider('mock')
            
            assert provider1 is provider2
    
    def test_unknown_provider(self):
        """Test error handling for unknown provider."""
        with pytest.raises(LLMProviderError):
            LLMProviderFactory.get_provider('unknown')
    
    def test_get_available_providers(self):
        """Test getting available providers."""
        LLMProviderFactory.register_provider('mock_available', MockProvider)
        LLMProviderFactory.register_provider('mock_unavailable', MockProvider)
        
        with patch('setup_agent.core.config.config') as mock_config:
            def mock_get(path, default=None):
                if path == ['mock_available']:
                    return {'available': True}
                elif path == ['mock_unavailable']:
                    return {'available': False}
                return default
            
            mock_config.get.side_effect = mock_get
            
            available = LLMProviderFactory.get_available_providers()
            assert 'mock_available' in available
            assert 'mock_unavailable' not in available
    
    def test_get_provider_info(self):
        """Test getting provider information."""
        LLMProviderFactory.register_provider('mock', MockProvider)
        
        with patch('setup_agent.core.config.config') as mock_config:
            mock_config.get.return_value = {'available': True}
            
            info = LLMProviderFactory.get_provider_info()
            assert 'mock' in info
            assert info['mock']['available'] is True
            assert 'models' in info['mock']
    
    def test_list_all_models(self):
        """Test listing models from all providers."""
        LLMProviderFactory.register_provider('mock', MockProvider)
        
        with patch('setup_agent.core.config.config') as mock_config:
            mock_config.get.return_value = {'available': True}
            
            models = LLMProviderFactory.list_all_models()
            assert 'mock' in models
            assert 'mock-model-1' in models['mock']
            assert 'mock-model-2' in models['mock']
    
    def test_auto_select_model(self):
        """Test automatic model selection."""
        LLMProviderFactory.register_provider('mock', MockProvider)
        
        with patch('setup_agent.core.config.config') as mock_config:
            mock_config.get.return_value = {'available': True, 'default_model': 'mock-model-1'}
            
            provider_name, model = LLMProviderFactory.auto_select_model('general')
            assert provider_name == 'mock'
            assert model == 'mock-model-1'
    
    def test_clear_cache(self):
        """Test cache clearing."""
        LLMProviderFactory.register_provider('mock', MockProvider)
        
        with patch('setup_agent.core.config.config') as mock_config:
            mock_config.get.return_value = {'available': True}
            
            # Create instance
            provider = LLMProviderFactory.get_provider('mock')
            assert len(LLMProviderFactory._instances) == 1
            
            # Clear cache
            LLMProviderFactory.clear_cache()
            assert len(LLMProviderFactory._instances) == 0
