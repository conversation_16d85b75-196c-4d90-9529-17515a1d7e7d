{"ollama": {"url": "http://localhost:11434", "default_model": "qwen2.5:7b-instruct", "gpu_optimization": true, "timeout": 90, "simple_timeout": 20, "options": {"temperature": 0.7, "top_p": 0.9, "num_ctx": 8192, "num_batch": 1024, "num_thread": 8, "repeat_penalty": 1.1, "top_k": 40, "stop": ["Human:", "Assistant:", "User:"], "num_gpu": -1, "main_gpu": 0, "low_vram": false}, "mistral_specific": {"system_prompt_style": "instruct", "use_chat_template": true, "max_tokens": 2048, "stream": false}, "qwen_optimized_models": {"qwen2.5:7b-instruct": {"max_tokens": 2048, "num_ctx": 8192, "num_batch": 1024, "temperature": 0.6, "vram_usage": 3800, "use_case": "primary_instruction_following"}, "qwen3:8b": {"max_tokens": 2048, "num_ctx": 8192, "num_batch": 1024, "temperature": 0.7, "vram_usage": 3500, "use_case": "advanced_reasoning"}, "qwen2.5:3b": {"max_tokens": 1024, "num_ctx": 4096, "num_batch": 512, "temperature": 0.6, "vram_usage": 2100, "use_case": "fast_inference"}, "deepseek-coder:6.7b-instruct": {"max_tokens": 1536, "num_ctx": 6144, "num_batch": 768, "temperature": 0.5, "vram_usage": 3200, "use_case": "coding_specialized"}}}, "gpu": {"target_gpu": 0, "memory_reserve_mb": 100, "gpu_layers": -1, "monitor_interval": 5, "max_vram_usage": 4096, "aggressive_gpu_usage": true, "quadro_p1000_optimized": true, "preferred_models": ["qwen2.5:7b-instruct", "qwen3:8b", "qwen2.5:3b", "deepseek-coder:6.7b-instruct"]}, "safety": {"safe_commands": ["git", "cd", "ls", "dir", "pwd", "whoami", "echo", "cat", "head", "tail", "grep", "find", "which", "where", "python", "pip", "node", "npm", "code"], "dangerous_patterns": ["rm ", "del ", "format", "shutdown", "reboot", "kill", "taskkill", "sudo rm", "sudo del", "> /dev/", "dd if=", "mkfs"], "auto_execute_safe": true, "require_confirmation_for_unsafe": true}, "memory": {"max_chat_history": 100, "max_conversation_history": 1000, "max_command_history": 200, "max_search_history": 500, "max_search_cache_size": 2000, "cleanup_interval_hours": 168, "persist_frequency_data": true, "long_term_retention": true}, "ui": {"cli_colors": true, "gui_enabled": true, "default_interface": "cli"}, "embeddings": {"enabled": true, "backend": "nomic-ollama", "model": "nomic-embed-text", "vector_store": "faiss", "max_context_results": 5, "auto_store_interactions": true, "auto_store_commands": true, "similarity_threshold": 0.7, "context_injection": {"enabled": true, "max_tokens": 1000, "include_timestamp": true, "include_project": true}, "fallback_models": [{"backend": "sentence_transformers", "model": "all-MiniLM-L6-v2"}, {"backend": "openai", "model": "text-embedding-3-small"}]}, "advanced_memory": {"enabled": true, "database_path": "memory_data/advanced_memory.db", "auto_cleanup": true, "cleanup_interval_hours": 24, "retention_policies": {"conversation": 365, "factual_knowledge": 730, "procedural_knowledge": 365, "user_preference": 730, "web_content": 180, "command_execution": 180, "learning_pattern": 365, "knowledge_snippet": 365}, "web_learning": {"enabled": true, "auto_search_threshold": 0.7, "max_pages_per_search": 3, "content_extraction_timeout": 15, "rate_limit_seconds": 2, "trusted_domains": ["docs.python.org", "developer.mozilla.org", "docs.microsoft.com", "kubernetes.io", "docs.docker.com"]}, "learning_system": {"enabled": true, "pattern_detection": true, "auto_feedback_learning": true, "confidence_adjustment_rate": 0.1, "min_pattern_frequency": 3, "consolidation_similarity_threshold": 0.9}, "context_enhancement": {"max_context_items": 5, "include_web_content": true, "include_learning_patterns": true, "include_user_preferences": true, "context_relevance_threshold": 0.5}}}