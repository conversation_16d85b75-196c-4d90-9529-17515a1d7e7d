"""
Safe file operations and utilities.
"""

import os
import shutil
import logging
from pathlib import Path
from typing import Dict, Any, List, Optional, Union
from contextlib import contextmanager

from ..core.config import config
from ..core.exceptions import SecurityError

logger = logging.getLogger(__name__)


class FileManager:
    """Safe file operations manager."""
    
    def __init__(self):
        self.allowed_directories = set(config.get(['security', 'allowed_directories'], [
            '.', './data', './temp', './logs', './memory_data'
        ]))
        self.max_file_size = config.get(['security', 'max_file_size_mb'], 100) * 1024 * 1024  # MB to bytes
        
    def is_path_allowed(self, path: Union[str, Path]) -> bool:
        """Check if path is within allowed directories."""
        try:
            path_obj = Path(path).resolve()
            
            for allowed_dir in self.allowed_directories:
                allowed_path = Path(allowed_dir).resolve()
                if path_obj.is_relative_to(allowed_path):
                    return True
            
            return False
        except (ValueError, OSError):
            return False
    
    def safe_read_file(self, path: Union[str, Path], encoding: str = 'utf-8') -> str:
        """Safely read a file with validation."""
        if not self.is_path_allowed(path):
            raise SecurityError(f"Path not allowed: {path}")
        
        path_obj = Path(path)
        if not path_obj.exists():
            raise FileNotFoundError(f"File not found: {path}")
        
        if path_obj.stat().st_size > self.max_file_size:
            raise SecurityError(f"File too large: {path}")
        
        try:
            with open(path_obj, 'r', encoding=encoding) as f:
                return f.read()
        except Exception as e:
            logger.error(f"Failed to read file {path}: {e}")
            raise
    
    def safe_write_file(self, path: Union[str, Path], content: str, encoding: str = 'utf-8') -> None:
        """Safely write a file with validation."""
        if not self.is_path_allowed(path):
            raise SecurityError(f"Path not allowed: {path}")
        
        if len(content.encode(encoding)) > self.max_file_size:
            raise SecurityError("Content too large")
        
        path_obj = Path(path)
        
        # Create directory if it doesn't exist
        path_obj.parent.mkdir(parents=True, exist_ok=True)
        
        try:
            with open(path_obj, 'w', encoding=encoding) as f:
                f.write(content)
            logger.debug(f"File written: {path}")
        except Exception as e:
            logger.error(f"Failed to write file {path}: {e}")
            raise
    
    def safe_delete_file(self, path: Union[str, Path]) -> bool:
        """Safely delete a file."""
        if not self.is_path_allowed(path):
            raise SecurityError(f"Path not allowed: {path}")
        
        path_obj = Path(path)
        if not path_obj.exists():
            return False
        
        try:
            path_obj.unlink()
            logger.debug(f"File deleted: {path}")
            return True
        except Exception as e:
            logger.error(f"Failed to delete file {path}: {e}")
            raise
    
    def safe_copy_file(self, src: Union[str, Path], dst: Union[str, Path]) -> None:
        """Safely copy a file."""
        if not self.is_path_allowed(src) or not self.is_path_allowed(dst):
            raise SecurityError("Source or destination path not allowed")
        
        src_path = Path(src)
        dst_path = Path(dst)
        
        if not src_path.exists():
            raise FileNotFoundError(f"Source file not found: {src}")
        
        if src_path.stat().st_size > self.max_file_size:
            raise SecurityError(f"Source file too large: {src}")
        
        # Create destination directory if needed
        dst_path.parent.mkdir(parents=True, exist_ok=True)
        
        try:
            shutil.copy2(src_path, dst_path)
            logger.debug(f"File copied: {src} -> {dst}")
        except Exception as e:
            logger.error(f"Failed to copy file {src} to {dst}: {e}")
            raise
    
    def list_directory(self, path: Union[str, Path]) -> List[Dict[str, Any]]:
        """Safely list directory contents."""
        if not self.is_path_allowed(path):
            raise SecurityError(f"Path not allowed: {path}")
        
        path_obj = Path(path)
        if not path_obj.exists():
            raise FileNotFoundError(f"Directory not found: {path}")
        
        if not path_obj.is_dir():
            raise ValueError(f"Path is not a directory: {path}")
        
        try:
            items = []
            for item in path_obj.iterdir():
                stat = item.stat()
                items.append({
                    'name': item.name,
                    'path': str(item),
                    'is_file': item.is_file(),
                    'is_dir': item.is_dir(),
                    'size': stat.st_size if item.is_file() else None,
                    'modified': stat.st_mtime
                })
            
            return sorted(items, key=lambda x: (not x['is_dir'], x['name'].lower()))
        except Exception as e:
            logger.error(f"Failed to list directory {path}: {e}")
            raise
    
    @contextmanager
    def temporary_file(self, suffix: str = '.tmp', content: Optional[str] = None):
        """Create a temporary file in allowed directory."""
        import tempfile
        
        # Use first allowed directory for temp files
        temp_dir = Path(list(self.allowed_directories)[0]) / 'temp'
        temp_dir.mkdir(parents=True, exist_ok=True)
        
        try:
            with tempfile.NamedTemporaryFile(
                mode='w+',
                suffix=suffix,
                dir=temp_dir,
                delete=False,
                encoding='utf-8'
            ) as f:
                if content:
                    f.write(content)
                    f.flush()
                
                temp_path = Path(f.name)
                yield temp_path
        finally:
            # Clean up
            if temp_path.exists():
                temp_path.unlink()


class safe_file_operations:
    """Context manager for safe file operations."""
    
    def __init__(self):
        self.file_manager = FileManager()
    
    def __enter__(self):
        return self.file_manager
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        # Cleanup if needed
        pass


# Global file manager instance
file_manager = FileManager()
