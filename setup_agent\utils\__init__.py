"""Utility modules for SetupAgent."""

from .logging_utils import setup_logging, get_logger
from .file_utils import safe_file_operations, FileManager

# Optional monitoring (requires psutil)
try:
    from .monitoring import PerformanceMonitor, MemoryMonitor
    has_monitoring = True
except ImportError:
    has_monitoring = False
    PerformanceMonitor = None
    MemoryMonitor = None

__all__ = [
    "setup_logging",
    "get_logger",
    "safe_file_operations",
    "FileManager"
]

if has_monitoring:
    __all__.extend(["PerformanceMonitor", "MemoryMonitor"])
