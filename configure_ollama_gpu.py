#!/usr/bin/env python3
"""
🎯 Ollama GPU Configuration Script for NVIDIA Quadro P1000
Comprehensive setup and verification for GPU-accelerated LLM inference.
"""

import os
import sys
import subprocess
import json
import time
from typing import Dict, Any, Optional, List


def run_command(cmd: str, timeout: int = 30) -> Dict[str, Any]:
    """Run a shell command and return results."""
    try:
        result = subprocess.run(
            cmd, shell=True, capture_output=True, text=True, timeout=timeout
        )
        return {
            "success": result.returncode == 0,
            "stdout": result.stdout.strip(),
            "stderr": result.stderr.strip(),
            "returncode": result.returncode
        }
    except subprocess.TimeoutExpired:
        return {"success": False, "stdout": "", "stderr": "Command timed out", "returncode": -1}
    except Exception as e:
        return {"success": False, "stdout": "", "stderr": str(e), "returncode": -1}


def check_nvidia_driver() -> bool:
    """Check if NVIDIA drivers are properly installed."""
    print("🔍 Checking NVIDIA Driver Installation...")
    
    result = run_command("nvidia-smi")
    if result["success"]:
        print("✅ NVIDIA drivers are installed and working")
        
        # Extract driver and CUDA versions
        lines = result["stdout"].split('\n')
        for line in lines:
            if "Driver Version:" in line and "CUDA Version:" in line:
                print(f"📋 {line.strip()}")
                break
        
        # Check for Quadro P1000 specifically
        if "Quadro P1000" in result["stdout"]:
            print("🎯 NVIDIA Quadro P1000 detected!")
            return True
        else:
            print("⚠️  Quadro P1000 not found, but other NVIDIA GPU detected")
            return True
    else:
        print("❌ NVIDIA drivers not found or not working")
        print(f"   Error: {result['stderr']}")
        return False


def check_ollama_installation() -> bool:
    """Check if Ollama is installed and accessible."""
    print("\n🔍 Checking Ollama Installation...")
    
    result = run_command("ollama --version")
    if result["success"]:
        print(f"✅ Ollama is installed: {result['stdout']}")
        return True
    else:
        print("❌ Ollama not found in PATH")
        print("💡 Install Ollama from: https://ollama.ai/download")
        return False


def check_ollama_service() -> bool:
    """Check if Ollama service is running."""
    print("\n🔍 Checking Ollama Service Status...")
    
    # Try to connect to Ollama API
    import requests
    try:
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            print("✅ Ollama service is running")
            
            # List available models
            models = response.json().get("models", [])
            if models:
                print("📦 Available models:")
                for model in models[:5]:  # Show first 5 models
                    name = model.get("name", "Unknown")
                    size = model.get("size", 0)
                    size_gb = size / (1024**3) if size > 0 else 0
                    print(f"   • {name} ({size_gb:.1f}GB)")
            else:
                print("⚠️  No models installed")
                print("💡 Install a model: ollama pull llama3.2:3b")
            return True
    except requests.exceptions.ConnectionError:
        print("❌ Ollama service is not running")
        print("💡 Start Ollama: ollama serve")
        return False
    except Exception as e:
        print(f"❌ Error checking Ollama service: {e}")
        return False


def configure_ollama_gpu() -> bool:
    """Configure Ollama for optimal GPU usage."""
    print("\n🔧 Configuring Ollama for GPU Acceleration...")
    
    # Set environment variables for Ollama GPU usage
    gpu_env_vars = {
        "OLLAMA_GPU_LAYERS": "-1",  # Use all GPU layers
        "OLLAMA_NUM_PARALLEL": "1",  # Single parallel request for P1000
        "OLLAMA_MAX_LOADED_MODELS": "1",  # One model at a time for 4GB VRAM
        "OLLAMA_FLASH_ATTENTION": "1",  # Enable flash attention if available
        "CUDA_VISIBLE_DEVICES": "0",  # Use first GPU (P1000)
    }
    
    print("🎯 Setting Ollama GPU environment variables:")
    for var, value in gpu_env_vars.items():
        os.environ[var] = value
        print(f"   • {var}={value}")
    
    # Create or update Ollama configuration
    config_dir = os.path.expanduser("~/.ollama")
    if not os.path.exists(config_dir):
        os.makedirs(config_dir)
        print(f"📁 Created Ollama config directory: {config_dir}")
    
    return True


def test_gpu_inference() -> bool:
    """Test GPU inference with a simple prompt."""
    print("\n🧪 Testing GPU Inference Performance...")
    
    # Test with a simple model if available
    test_models = ["llama3.2:3b", "phi4-mini-reasoning:3.8b", "gemma3:4b", "mistral:latest"]
    
    for model in test_models:
        print(f"\n🔄 Testing with model: {model}")
        
        # Check if model exists
        result = run_command(f"ollama list | grep {model.split(':')[0]}")
        if not result["success"]:
            print(f"⚠️  Model {model} not found, trying next...")
            continue
        
        # Test inference with GPU monitoring
        print("📊 Starting GPU monitoring...")
        gpu_before = get_gpu_memory()
        
        start_time = time.time()
        result = run_command(f'ollama run {model} "Hello, test GPU inference"', timeout=60)
        end_time = time.time()
        
        gpu_after = get_gpu_memory()
        
        if result["success"]:
            inference_time = end_time - start_time
            memory_used = gpu_after - gpu_before if gpu_after > gpu_before else 0
            
            print(f"✅ GPU Inference Test Successful!")
            print(f"   • Model: {model}")
            print(f"   • Response time: {inference_time:.2f} seconds")
            print(f"   • GPU memory used: {memory_used:.0f}MB")
            print(f"   • Response: {result['stdout'][:100]}...")
            return True
        else:
            print(f"❌ Inference failed: {result['stderr']}")
    
    print("❌ No suitable models found for testing")
    return False


def get_gpu_memory() -> float:
    """Get current GPU memory usage in MB."""
    try:
        result = run_command("nvidia-smi --query-gpu=memory.used --format=csv,noheader,nounits")
        if result["success"]:
            return float(result["stdout"])
    except Exception:
        pass
    return 0.0


def recommend_models() -> None:
    """Recommend optimal models for Quadro P1000."""
    print("\n💡 Recommended Models for NVIDIA Quadro P1000 (4GB VRAM):")
    print("=" * 60)
    
    recommendations = [
        ("llama3.2:3b", "2.0GB", "Fast inference, good for chat"),
        ("phi4-mini-reasoning:3.8b", "3.2GB", "Excellent reasoning capabilities"),
        ("gemma3:4b", "3.3GB", "Balanced performance and quality"),
        ("mistral:latest", "4.1GB", "High quality, uses most VRAM"),
    ]
    
    for model, size, description in recommendations:
        print(f"📦 {model:<25} {size:<8} - {description}")
    
    print("\n🚀 Installation commands:")
    for model, _, _ in recommendations:
        print(f"   ollama pull {model}")


def main():
    """Main configuration function."""
    print("🎯 Ollama GPU Configuration for NVIDIA Quadro P1000")
    print("=" * 60)
    
    # Step 1: Check NVIDIA drivers
    if not check_nvidia_driver():
        print("\n❌ NVIDIA drivers are required. Please install them first.")
        print("💡 Download from: https://www.nvidia.com/drivers")
        return False
    
    # Step 2: Check Ollama installation
    if not check_ollama_installation():
        return False
    
    # Step 3: Check Ollama service
    if not check_ollama_service():
        print("\n💡 Please start Ollama service and try again:")
        print("   ollama serve")
        return False
    
    # Step 4: Configure GPU settings
    if not configure_ollama_gpu():
        return False
    
    # Step 5: Test GPU inference
    if test_gpu_inference():
        print("\n🎉 GPU Configuration Successful!")
        print("✅ Ollama is properly configured for GPU acceleration")
    else:
        print("\n⚠️  GPU inference test failed")
        print("💡 You may need to install a compatible model")
    
    # Step 6: Show recommendations
    recommend_models()
    
    print("\n🔧 Next Steps:")
    print("1. Install a recommended model: ollama pull llama3.2:3b")
    print("2. Test the setup agent: python setup_agent.py --gpu")
    print("3. Use LLM interactive mode for AI assistance")
    
    return True


if __name__ == "__main__":
    try:
        import requests
    except ImportError:
        print("❌ Missing required package: requests")
        print("💡 Install with: pip install requests")
        sys.exit(1)
    
    success = main()
    sys.exit(0 if success else 1)
