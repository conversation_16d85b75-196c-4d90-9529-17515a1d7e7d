"""
Base LLM provider interface.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, Iterator, List
from dataclasses import dataclass


@dataclass
class LLMResponse:
    """Response from an LLM provider."""
    content: str
    model: str
    provider: str
    metadata: Dict[str, Any]
    error: Optional[str] = None


@dataclass
class LLMStreamResponse:
    """Streaming response chunk from an LLM provider."""
    content: str
    is_complete: bool
    metadata: Dict[str, Any]
    error: Optional[str] = None


class BaseLLMProvider(ABC):
    """Abstract base class for LLM providers."""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize the provider with configuration."""
        self.config = config
        self.name = self.__class__.__name__.replace('Provider', '').lower()
    
    @abstractmethod
    def is_available(self) -> bool:
        """Check if the provider is available and properly configured."""
        pass
    
    @abstractmethod
    def generate(
        self, 
        prompt: str, 
        model: Optional[str] = None,
        **kwargs
    ) -> LLMResponse:
        """Generate a response from the LLM."""
        pass
    
    @abstractmethod
    def generate_stream(
        self, 
        prompt: str, 
        model: Optional[str] = None,
        **kwargs
    ) -> Iterator[LLMStreamResponse]:
        """Generate a streaming response from the LLM."""
        pass
    
    @abstractmethod
    def list_models(self) -> List[str]:
        """List available models for this provider."""
        pass
    
    def get_default_model(self) -> str:
        """Get the default model for this provider."""
        return self.config.get('default_model', 'unknown')
    
    def validate_model(self, model: str) -> bool:
        """Validate if a model is available."""
        try:
            available_models = self.list_models()
            return model in available_models
        except Exception:
            return False
    
    def get_provider_info(self) -> Dict[str, Any]:
        """Get information about this provider."""
        return {
            'name': self.name,
            'available': self.is_available(),
            'default_model': self.get_default_model(),
            'models': self.list_models() if self.is_available() else []
        }
