import os
import json
from typing import List, Dict, Any

# Path to the improvements JSON file
dir_path = os.path.dirname(__file__)
IMPROVEMENTS_FILE = os.path.join(dir_path, 'improvements.json')


def load_improvements() -> List[Dict[str, Any]]:
    """Load the list of improvements from the JSON file."""
    if not os.path.exists(IMPROVEMENTS_FILE):
        return []
    with open(IMPROVEMENTS_FILE, 'r', encoding='utf-8') as f:
        return json.load(f)


def save_improvements(improvements: List[Dict[str, Any]]) -> None:
    """Save the improvements list back to the JSON file."""
    with open(IMPROVEMENTS_FILE, 'w', encoding='utf-8') as f:
        json.dump(improvements, f, indent=2, ensure_ascii=False)


def list_improvements(show_all: bool = True) -> None:
    """Print all improvements, or only pending/completed ones."""
    improvements = load_improvements()
    if not improvements:
        print("No improvements found.")
        return

    for item in improvements:
        status = '✅' if item.get('implemented') else '❌'
        if show_all or not item.get('implemented'):
            print(f"[{status}] {item['id']}: {item['title']} - {item['description']}")


def mark_implemented(task_id: str) -> bool:
    """Mark a specific improvement as implemented."""
    improvements = load_improvements()
    found = False
    for item in improvements:
        if item.get('id') == task_id:
            item['implemented'] = True
            found = True
            break
    if found:
        save_improvements(improvements)
    return found


if __name__ == '__main__':
    import argparse

    parser = argparse.ArgumentParser(description="Track implementation status of project improvements.")
    parser.add_argument('--list', action='store_true', help='List all improvements')
    parser.add_argument('--pending', action='store_true', help='List only pending improvements')
    parser.add_argument('--done', action='store_true', help='List only completed improvements')
    parser.add_argument('--mark', metavar='TASK_ID', help='Mark a task as implemented')

    args = parser.parse_args()

    if args.mark:
        if mark_implemented(args.mark):
            print(f"Task '{args.mark}' marked as implemented.")
        else:
            print(f"Task '{args.mark}' not found.")
    elif args.pending:
        print("Pending improvements:")
        list_improvements(show_all=False)
    elif args.done:
        print("Completed improvements:")
        # show only implemented
        for item in load_improvements():
            if item.get('implemented'):
                print(f"[✅] {item['id']}: {item['title']}")
    else:
        print("All improvements:")
        list_improvements()
