"""
OpenAI LLM provider implementation.
"""

import logging
from typing import Dict, Any, Optional, Iterator, List

from .base import Base<PERSON><PERSON>rovider, LLMResponse, LLMStreamResponse
from ..core.exceptions import LLMProviderError

# Optional dependency
try:
    import openai
    HAS_OPENAI = True
except ImportError:
    HAS_OPENAI = False

logger = logging.getLogger(__name__)


class OpenAIProvider(BaseLLMProvider):
    """OpenAI LLM provider implementation."""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.api_key = config.get('api_key', '')
        self.organization = config.get('organization', '')
        self.timeout = config.get('timeout', 30)
        
        if HAS_OPENAI and self.api_key:
            self.client = openai.OpenAI(
                api_key=self.api_key,
                organization=self.organization if self.organization else None,
                timeout=self.timeout
            )
        else:
            self.client = None
    
    def is_available(self) -> bool:
        """Check if OpenAI is available and configured."""
        if not HAS_OPENAI:
            return False
        
        if not self.api_key:
            return False
        
        try:
            # Test with a simple API call
            self.client.models.list()
            return True
        except Exception as e:
            logger.debug(f"OpenAI not available: {e}")
            return False
    
    def list_models(self) -> List[str]:
        """List available OpenAI models."""
        if not self.is_available():
            return []
        
        try:
            models = self.client.models.list()
            return [model.id for model in models.data if 'gpt' in model.id]
        except Exception as e:
            logger.error(f"Failed to list OpenAI models: {e}")
            return []
    
    def generate(
        self, 
        prompt: str, 
        model: Optional[str] = None,
        **kwargs
    ) -> LLMResponse:
        """Generate a response from OpenAI."""
        if not self.is_available():
            raise LLMProviderError("OpenAI is not available or not configured")
        
        model = model or self.get_default_model()
        
        try:
            # Prepare messages
            messages = [{"role": "user", "content": prompt}]
            
            # Extract OpenAI-specific parameters
            temperature = kwargs.get('temperature', 0.7)
            max_tokens = kwargs.get('max_tokens', 2048)
            top_p = kwargs.get('top_p', 1.0)
            
            response = self.client.chat.completions.create(
                model=model,
                messages=messages,
                temperature=temperature,
                max_tokens=max_tokens,
                top_p=top_p,
                stream=False
            )
            
            content = response.choices[0].message.content or ''
            
            return LLMResponse(
                content=content,
                model=model,
                provider='openai',
                metadata={
                    'usage': response.usage.model_dump() if response.usage else {},
                    'finish_reason': response.choices[0].finish_reason,
                    'created': response.created
                }
            )
            
        except Exception as e:
            logger.error(f"OpenAI generation failed: {e}")
            return LLMResponse(
                content='',
                model=model,
                provider='openai',
                metadata={},
                error=str(e)
            )
    
    def generate_stream(
        self, 
        prompt: str, 
        model: Optional[str] = None,
        **kwargs
    ) -> Iterator[LLMStreamResponse]:
        """Generate a streaming response from OpenAI."""
        if not self.is_available():
            raise LLMProviderError("OpenAI is not available or not configured")
        
        model = model or self.get_default_model()
        
        try:
            # Prepare messages
            messages = [{"role": "user", "content": prompt}]
            
            # Extract OpenAI-specific parameters
            temperature = kwargs.get('temperature', 0.7)
            max_tokens = kwargs.get('max_tokens', 2048)
            top_p = kwargs.get('top_p', 1.0)
            
            stream = self.client.chat.completions.create(
                model=model,
                messages=messages,
                temperature=temperature,
                max_tokens=max_tokens,
                top_p=top_p,
                stream=True
            )
            
            for chunk in stream:
                if chunk.choices and chunk.choices[0].delta.content:
                    content = chunk.choices[0].delta.content
                    is_complete = chunk.choices[0].finish_reason is not None
                    
                    yield LLMStreamResponse(
                        content=content,
                        is_complete=is_complete,
                        metadata={
                            'created': chunk.created,
                            'model': chunk.model
                        }
                    )
                    
        except Exception as e:
            logger.error(f"OpenAI streaming failed: {e}")
            yield LLMStreamResponse(
                content='',
                is_complete=True,
                metadata={},
                error=str(e)
            )
