"""
Type stubs for FAISS library to suppress Pylance warnings.
"""

from typing import Any, Optional, <PERSON>ple
import numpy as np

class Index:
    """FAISS Index base class."""
    def add(self, vectors: np.ndarray) -> None: ...
    def search(self, query: np.ndarray, k: int) -> Tuple[np.ndarray, np.ndarray]: ...
    def ntotal(self) -> int: ...

class IndexFlatL2(Index):
    """FAISS L2 distance index."""
    def __init__(self, d: int) -> None: ...

class IndexFlatIP(Index):
    """FAISS inner product index."""
    def __init__(self, d: int) -> None: ...

class IndexIVFFlat(Index):
    """FAISS IVF index."""
    def __init__(self, quantizer: Index, d: int, nlist: int) -> None: ...
    def train(self, vectors: np.ndarray) -> None: ...

def get_num_gpus() -> int: ...
def index_factory(d: int, description: str) -> Index: ...
def normalize_L2(vectors: np.ndarray) -> None: ...
