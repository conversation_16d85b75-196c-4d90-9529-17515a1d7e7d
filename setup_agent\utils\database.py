"""
Database optimization utilities with connection pooling and indexing.
"""

import sqlite3
import threading
import logging
import time
from typing import Dict, Any, List, Optional, Tuple, Union
from contextlib import contextmanager
from pathlib import Path
import queue

from ..core.config import config

logger = logging.getLogger(__name__)


class DatabasePool:
    """SQLite connection pool for better performance."""
    
    def __init__(self, database_path: str, max_connections: int = 10):
        self.database_path = database_path
        self.max_connections = max_connections
        self.pool = queue.Queue(maxsize=max_connections)
        self._lock = threading.RLock()
        self._created_connections = 0
        
        # Initialize pool
        self._initialize_pool()
    
    def _initialize_pool(self) -> None:
        """Initialize connection pool."""
        # Ensure database directory exists
        db_path = Path(self.database_path)
        db_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Create initial connections
        for _ in range(min(3, self.max_connections)):  # Start with 3 connections
            conn = self._create_connection()
            if conn:
                self.pool.put(conn)
    
    def _create_connection(self) -> Optional[sqlite3.Connection]:
        """Create a new database connection."""
        try:
            conn = sqlite3.connect(
                self.database_path,
                check_same_thread=False,
                timeout=30.0
            )
            
            # Optimize SQLite settings
            conn.execute("PRAGMA journal_mode=WAL")  # Write-Ahead Logging
            conn.execute("PRAGMA synchronous=NORMAL")  # Balance safety/performance
            conn.execute("PRAGMA cache_size=10000")  # 10MB cache
            conn.execute("PRAGMA temp_store=MEMORY")  # Use memory for temp tables
            conn.execute("PRAGMA mmap_size=268435456")  # 256MB memory map
            
            # Enable foreign keys
            conn.execute("PRAGMA foreign_keys=ON")
            
            conn.row_factory = sqlite3.Row  # Enable dict-like access
            
            with self._lock:
                self._created_connections += 1
            
            logger.debug(f"Created database connection #{self._created_connections}")
            return conn
            
        except Exception as e:
            logger.error(f"Failed to create database connection: {e}")
            return None
    
    @contextmanager
    def get_connection(self):
        """Get a connection from the pool."""
        conn = None
        try:
            # Try to get existing connection
            try:
                conn = self.pool.get_nowait()
            except queue.Empty:
                # Create new connection if pool is empty and under limit
                if self._created_connections < self.max_connections:
                    conn = self._create_connection()
                else:
                    # Wait for available connection
                    conn = self.pool.get(timeout=10.0)
            
            if conn is None:
                raise Exception("Could not obtain database connection")
            
            yield conn
            
        except Exception as e:
            logger.error(f"Database connection error: {e}")
            if conn:
                try:
                    conn.rollback()
                except:
                    pass
            raise
        finally:
            # Return connection to pool
            if conn:
                try:
                    # Check if connection is still valid
                    conn.execute("SELECT 1")
                    self.pool.put_nowait(conn)
                except (queue.Full, sqlite3.Error):
                    # Pool full or connection invalid, close it
                    try:
                        conn.close()
                    except:
                        pass
    
    def close_all(self) -> None:
        """Close all connections in the pool."""
        while not self.pool.empty():
            try:
                conn = self.pool.get_nowait()
                conn.close()
            except (queue.Empty, sqlite3.Error):
                break
        
        logger.info("All database connections closed")


class DatabaseOptimizer:
    """Database optimization utilities."""
    
    def __init__(self, pool: DatabasePool):
        self.pool = pool
    
    def create_indexes(self, table_indexes: Dict[str, List[str]]) -> None:
        """Create indexes for better query performance."""
        with self.pool.get_connection() as conn:
            for table, columns in table_indexes.items():
                for column in columns:
                    index_name = f"idx_{table}_{column}"
                    try:
                        conn.execute(f"""
                            CREATE INDEX IF NOT EXISTS {index_name} 
                            ON {table} ({column})
                        """)
                        logger.debug(f"Created index: {index_name}")
                    except sqlite3.Error as e:
                        logger.warning(f"Failed to create index {index_name}: {e}")
            
            conn.commit()
    
    def analyze_database(self) -> None:
        """Update database statistics for query optimization."""
        with self.pool.get_connection() as conn:
            try:
                conn.execute("ANALYZE")
                conn.commit()
                logger.info("Database analysis completed")
            except sqlite3.Error as e:
                logger.error(f"Database analysis failed: {e}")
    
    def vacuum_database(self) -> None:
        """Vacuum database to reclaim space and optimize."""
        with self.pool.get_connection() as conn:
            try:
                conn.execute("VACUUM")
                logger.info("Database vacuum completed")
            except sqlite3.Error as e:
                logger.error(f"Database vacuum failed: {e}")
    
    def get_table_stats(self) -> Dict[str, Dict[str, Any]]:
        """Get statistics for all tables."""
        stats = {}
        
        with self.pool.get_connection() as conn:
            # Get all tables
            tables = conn.execute("""
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name NOT LIKE 'sqlite_%'
            """).fetchall()
            
            for table in tables:
                table_name = table['name']
                try:
                    # Get row count
                    count_result = conn.execute(f"SELECT COUNT(*) as count FROM {table_name}").fetchone()
                    row_count = count_result['count'] if count_result else 0
                    
                    # Get table info
                    table_info = conn.execute(f"PRAGMA table_info({table_name})").fetchall()
                    columns = [col['name'] for col in table_info]
                    
                    # Get indexes
                    indexes = conn.execute(f"PRAGMA index_list({table_name})").fetchall()
                    index_names = [idx['name'] for idx in indexes]
                    
                    stats[table_name] = {
                        'row_count': row_count,
                        'column_count': len(columns),
                        'columns': columns,
                        'indexes': index_names
                    }
                    
                except sqlite3.Error as e:
                    logger.warning(f"Failed to get stats for table {table_name}: {e}")
                    stats[table_name] = {'error': str(e)}
        
        return stats
    
    def optimize_queries(self, queries: List[str]) -> Dict[str, Any]:
        """Analyze query performance."""
        results = {}
        
        with self.pool.get_connection() as conn:
            for i, query in enumerate(queries):
                try:
                    # Get query plan
                    plan = conn.execute(f"EXPLAIN QUERY PLAN {query}").fetchall()
                    
                    # Time the query
                    start_time = time.time()
                    conn.execute(query).fetchall()
                    execution_time = time.time() - start_time
                    
                    results[f"query_{i}"] = {
                        'query': query,
                        'execution_time': execution_time,
                        'query_plan': [dict(row) for row in plan]
                    }
                    
                except sqlite3.Error as e:
                    results[f"query_{i}"] = {
                        'query': query,
                        'error': str(e)
                    }
        
        return results


class PreparedStatementCache:
    """Cache for prepared statements."""
    
    def __init__(self, max_size: int = 100):
        self.max_size = max_size
        self.cache: Dict[str, sqlite3.Cursor] = {}
        self._access_order: List[str] = []
        self._lock = threading.RLock()
    
    def get_statement(self, conn: sqlite3.Connection, sql: str) -> sqlite3.Cursor:
        """Get or create prepared statement."""
        with self._lock:
            if sql in self.cache:
                # Move to end (most recently used)
                self._access_order.remove(sql)
                self._access_order.append(sql)
                return self.cache[sql]
            
            # Create new prepared statement
            cursor = conn.cursor()
            
            # Evict least recently used if cache is full
            if len(self.cache) >= self.max_size:
                lru_sql = self._access_order.pop(0)
                if lru_sql in self.cache:
                    del self.cache[lru_sql]
            
            # Add to cache
            self.cache[sql] = cursor
            self._access_order.append(sql)
            
            return cursor
    
    def clear(self) -> None:
        """Clear the cache."""
        with self._lock:
            self.cache.clear()
            self._access_order.clear()


class DatabaseManager:
    """High-level database manager with optimization."""
    
    def __init__(self, database_path: str):
        self.database_path = database_path
        self.pool = DatabasePool(database_path)
        self.optimizer = DatabaseOptimizer(self.pool)
        self.statement_cache = PreparedStatementCache()
        
        # Initialize database
        self._initialize_database()
    
    def _initialize_database(self) -> None:
        """Initialize database with optimizations."""
        # Create standard indexes for common tables
        standard_indexes = {
            'conversations': ['timestamp', 'user_id'],
            'commands': ['timestamp', 'success'],
            'embeddings': ['timestamp', 'type'],
            'search_cache': ['query_hash', 'timestamp'],
            'memory_data': ['timestamp', 'type']
        }
        
        try:
            self.optimizer.create_indexes(standard_indexes)
            logger.info("Database initialized with optimizations")
        except Exception as e:
            logger.warning(f"Database optimization failed: {e}")
    
    def execute_query(self, sql: str, params: Optional[Tuple] = None) -> List[sqlite3.Row]:
        """Execute a query with connection pooling."""
        with self.pool.get_connection() as conn:
            cursor = conn.cursor()
            if params:
                cursor.execute(sql, params)
            else:
                cursor.execute(sql)
            return cursor.fetchall()
    
    def execute_many(self, sql: str, params_list: List[Tuple]) -> None:
        """Execute many statements efficiently."""
        with self.pool.get_connection() as conn:
            cursor = conn.cursor()
            cursor.executemany(sql, params_list)
            conn.commit()
    
    def get_stats(self) -> Dict[str, Any]:
        """Get database statistics."""
        return {
            'table_stats': self.optimizer.get_table_stats(),
            'connection_pool_size': self.pool._created_connections,
            'database_path': self.database_path
        }
    
    def maintenance(self) -> None:
        """Perform database maintenance."""
        logger.info("Starting database maintenance")
        
        try:
            self.optimizer.analyze_database()
            self.optimizer.vacuum_database()
            logger.info("Database maintenance completed")
        except Exception as e:
            logger.error(f"Database maintenance failed: {e}")
    
    def close(self) -> None:
        """Close database manager."""
        self.pool.close_all()
        self.statement_cache.clear()


# Global database manager for advanced memory
def get_database_manager() -> DatabaseManager:
    """Get or create global database manager."""
    db_path = config.get(['memory', 'database_path'], 'memory_data/advanced_memory.db')
    return DatabaseManager(db_path)
