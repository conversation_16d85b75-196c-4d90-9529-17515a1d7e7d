"""
Improvements package: each module here implements one enhancement with an init() stub.
"""
import pkgutil
import importlib

# Dynamically discover and initialize improvements
_discovered_mods: list[str] = []
for _, modname, _ in pkgutil.iter_modules(__path__):
    _discovered_mods.append(modname)
    module = importlib.import_module(f"improvements.{modname}")
    if hasattr(module, 'initialize'):
        module.initialize()
# pyright: ignore[reportUnsupportedDunderAll]
__all__ = _discovered_mods
