#!/usr/bin/env python3
"""
Test lightweight models for optimal SetupAgent performance.
"""

import time
import json
import requests
from pathlib import Path

# Lightweight models to test (in order of preference)
LIGHTWEIGHT_MODELS = [
    "llama3.2:3b",      # ~2.0GB VRAM - Fastest
    "qwen2.5:3b",       # ~2.1GB VRAM - Good reasoning
    "phi4-mini-reasoning:3.8b"  # ~3.2GB VRAM - Best reasoning
]

OLLAMA_URL = "http://localhost:11434/api/generate"

def test_model_performance(model_name: str) -> dict:
    """Test a model's performance with a simple prompt."""
    print(f"\n🧪 Testing {model_name}...")
    
    test_prompt = "Write a simple Python function to check if a number is prime. Be concise."
    
    payload = {
        "model": model_name,
        "prompt": test_prompt,
        "stream": False,
        "options": {
            "temperature": 0.7,
            "num_ctx": 4096,
            "num_batch": 256,
            "max_tokens": 512
        }
    }
    
    try:
        start_time = time.time()
        response = requests.post(OLLAMA_URL, json=payload, timeout=60)
        end_time = time.time()
        
        if response.status_code == 200:
            result = response.json()
            response_time = end_time - start_time
            
            return {
                "model": model_name,
                "success": True,
                "response_time": round(response_time, 2),
                "response_length": len(result.get("response", "")),
                "tokens_per_second": round(len(result.get("response", "").split()) / response_time, 1),
                "response_preview": result.get("response", "")[:100] + "..."
            }
        else:
            return {
                "model": model_name,
                "success": False,
                "error": f"HTTP {response.status_code}: {response.text}"
            }
            
    except Exception as e:
        return {
            "model": model_name,
            "success": False,
            "error": str(e)
        }

def check_model_availability() -> list:
    """Check which lightweight models are available."""
    print("🔍 Checking available lightweight models...")
    
    try:
        response = requests.get("http://localhost:11434/api/tags", timeout=10)
        if response.status_code == 200:
            models_data = response.json()
            available_models = [model["name"] for model in models_data.get("models", [])]
            
            lightweight_available = []
            for model in LIGHTWEIGHT_MODELS:
                if model in available_models:
                    lightweight_available.append(model)
                    print(f"✅ {model} - Available")
                else:
                    print(f"❌ {model} - Not found")
            
            return lightweight_available
        else:
            print(f"❌ Failed to get model list: HTTP {response.status_code}")
            return []
            
    except Exception as e:
        print(f"❌ Error checking models: {e}")
        return []

def recommend_optimal_model(test_results: list) -> str:
    """Recommend the optimal model based on test results."""
    if not test_results:
        return "llama3.2:3b"  # Default fallback
    
    # Filter successful tests
    successful_tests = [r for r in test_results if r["success"]]
    
    if not successful_tests:
        return "llama3.2:3b"  # Default fallback
    
    # Score models based on speed and efficiency
    for result in successful_tests:
        # Higher tokens/second is better, lower response time is better
        speed_score = result["tokens_per_second"] * 0.7
        efficiency_score = (10 / result["response_time"]) * 0.3
        result["total_score"] = speed_score + efficiency_score
    
    # Sort by total score (highest first)
    successful_tests.sort(key=lambda x: x["total_score"], reverse=True)
    
    return successful_tests[0]["model"]

def update_config_with_optimal_model(optimal_model: str):
    """Update config.json with the optimal model."""
    config_path = Path("config.json")
    
    if not config_path.exists():
        print("❌ config.json not found")
        return
    
    try:
        with open(config_path, 'r') as f:
            config = json.load(f)
        
        # Update default model
        config["ollama"]["default_model"] = optimal_model
        
        # Add performance note
        config["ollama"]["performance_optimized"] = True
        config["ollama"]["last_optimization"] = time.strftime("%Y-%m-%d %H:%M:%S")
        
        with open(config_path, 'w') as f:
            json.dump(config, f, indent=2)
        
        print(f"✅ Updated config.json with optimal model: {optimal_model}")
        
    except Exception as e:
        print(f"❌ Failed to update config: {e}")

def main():
    """Main testing function."""
    print("🎯 SetupAgent Lightweight Model Performance Test")
    print("=" * 60)
    
    # Check Ollama connection
    try:
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code != 200:
            print("❌ Ollama server not responding")
            return
    except Exception as e:
        print(f"❌ Cannot connect to Ollama: {e}")
        return
    
    print("✅ Ollama server is running")
    
    # Check available models
    available_models = check_model_availability()
    
    if not available_models:
        print("\n❌ No lightweight models found!")
        print("💡 Install recommended models:")
        for model in LIGHTWEIGHT_MODELS:
            print(f"   ollama pull {model}")
        return
    
    # Test available models
    print(f"\n🧪 Testing {len(available_models)} lightweight models...")
    test_results = []
    
    for model in available_models:
        result = test_model_performance(model)
        test_results.append(result)
        
        if result["success"]:
            print(f"✅ {model}:")
            print(f"   • Response time: {result['response_time']}s")
            print(f"   • Speed: {result['tokens_per_second']} tokens/sec")
            print(f"   • Preview: {result['response_preview']}")
        else:
            print(f"❌ {model}: {result['error']}")
    
    # Recommend optimal model
    optimal_model = recommend_optimal_model(test_results)
    
    print(f"\n🎯 RECOMMENDATION")
    print("=" * 40)
    print(f"🥇 Optimal model for your setup: {optimal_model}")
    
    # Show performance comparison
    successful_tests = [r for r in test_results if r["success"]]
    if len(successful_tests) > 1:
        print(f"\n📊 Performance Comparison:")
        for result in sorted(successful_tests, key=lambda x: x.get("total_score", 0), reverse=True):
            print(f"   {result['model']}: {result['tokens_per_second']} tok/s, {result['response_time']}s")
    
    # Ask to update config
    update_choice = input(f"\n🔧 Update config.json to use {optimal_model}? (y/n): ").lower().strip()
    if update_choice == 'y':
        update_config_with_optimal_model(optimal_model)
    
    print(f"\n✅ Testing complete! Use '{optimal_model}' for best performance.")

if __name__ == "__main__":
    main()