#!/usr/bin/env python3
"""
🧠 Advanced Memory and Learning System for SetupAgent
Comprehensive SQLite-based memory system with learning capabilities, web search integration,
and intelligent context retrieval.
"""

import os
import json
import sqlite3
import hashlib
import logging
from typing import Dict, Any, List, Optional, Tuple, Union
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
import re
from urllib.parse import urlparse, urljoin
import time

# Optional dependencies
try:
    import requests
    _HAS_REQUESTS = True
except ImportError:
    _HAS_REQUESTS = False
    requests = None  # type: ignore

logger = logging.getLogger(__name__)

# Configuration
MEMORY_DB_FILE = os.path.join(os.path.dirname(__file__), 'memory_data', 'advanced_memory.db')
MEMORY_DATA_DIR = os.path.dirname(MEMORY_DB_FILE)

# Ensure memory data directory exists
os.makedirs(MEMORY_DATA_DIR, exist_ok=True)

class MemoryType(Enum):
    """Types of memory entries."""
    CONVERSATION = "conversation"
    FACTUAL_KNOWLEDGE = "factual_knowledge"
    PROCEDURAL_KNOWLEDGE = "procedural_knowledge"
    USER_PREFERENCE = "user_preference"
    WEB_CONTENT = "web_content"
    COMMAND_EXECUTION = "command_execution"
    LEARNING_PATTERN = "learning_pattern"
    KNOWLEDGE_SNIPPET = "knowledge_snippet"

class ConfidenceLevel(Enum):
    """Confidence levels for stored knowledge."""
    VERY_LOW = 0.2
    LOW = 0.4
    MEDIUM = 0.6
    HIGH = 0.8
    VERY_HIGH = 1.0

class SourceType(Enum):
    """Types of information sources."""
    USER_INPUT = "user_input"
    WEB_SEARCH = "web_search"
    OFFICIAL_DOCS = "official_docs"
    COMMUNITY_FORUM = "community_forum"
    AI_GENERATED = "ai_generated"
    VERIFIED_FACT = "verified_fact"

@dataclass
class MemoryEntry:
    """Represents a memory entry."""
    id: str
    memory_type: MemoryType
    content: str
    metadata: Dict[str, Any]
    confidence: float
    source_type: SourceType
    created_at: datetime
    last_accessed: datetime
    access_count: int
    tags: List[str]
    related_entries: List[str]

@dataclass
class WebContent:
    """Represents extracted web content."""
    url: str
    title: str
    content: str
    summary: str
    key_facts: List[str]
    code_examples: List[str]
    tags: List[str]
    extracted_at: datetime
    source_reliability: float

class AdvancedMemorySystem:
    """Advanced memory and learning system for SetupAgent."""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.db_path = MEMORY_DB_FILE
        self.embedding_manager = None
        self._init_database()
        self._init_embeddings()
        
        # Memory retention policies
        self.retention_policies = {
            MemoryType.CONVERSATION: timedelta(days=30),
            MemoryType.FACTUAL_KNOWLEDGE: timedelta(days=365),
            MemoryType.PROCEDURAL_KNOWLEDGE: timedelta(days=180),
            MemoryType.USER_PREFERENCE: timedelta(days=365),
            MemoryType.WEB_CONTENT: timedelta(days=90),
            MemoryType.COMMAND_EXECUTION: timedelta(days=60),
            MemoryType.LEARNING_PATTERN: timedelta(days=180),
            MemoryType.KNOWLEDGE_SNIPPET: timedelta(days=120)
        }
        
        # Source reliability scores
        self.source_reliability = {
            SourceType.VERIFIED_FACT: 1.0,
            SourceType.OFFICIAL_DOCS: 0.9,
            SourceType.USER_INPUT: 0.8,
            SourceType.WEB_SEARCH: 0.6,
            SourceType.COMMUNITY_FORUM: 0.5,
            SourceType.AI_GENERATED: 0.4
        }
    
    def _init_database(self):
        """Initialize the SQLite database with comprehensive schema."""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Main memory entries table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS memory_entries (
                id TEXT PRIMARY KEY,
                memory_type TEXT NOT NULL,
                content TEXT NOT NULL,
                metadata TEXT,
                confidence REAL DEFAULT 0.6,
                source_type TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_accessed TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                access_count INTEGER DEFAULT 0,
                tags TEXT,
                related_entries TEXT,
                embedding_id TEXT,
                is_archived BOOLEAN DEFAULT FALSE,
                importance_score REAL DEFAULT 0.5
            )
        ''')
        
        # Conversation history table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS conversations (
                id TEXT PRIMARY KEY,
                session_id TEXT,
                user_input TEXT NOT NULL,
                ai_response TEXT NOT NULL,
                context_used TEXT,
                feedback_score INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                project_context TEXT,
                memory_entry_id TEXT,
                FOREIGN KEY (memory_entry_id) REFERENCES memory_entries (id)
            )
        ''')
        
        # Web search results and learned content
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS web_content (
                id TEXT PRIMARY KEY,
                url TEXT UNIQUE NOT NULL,
                title TEXT,
                content TEXT,
                summary TEXT,
                key_facts TEXT,
                code_examples TEXT,
                tags TEXT,
                source_reliability REAL DEFAULT 0.6,
                last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                access_count INTEGER DEFAULT 0,
                validation_status TEXT DEFAULT 'pending',
                memory_entry_id TEXT,
                FOREIGN KEY (memory_entry_id) REFERENCES memory_entries (id)
            )
        ''')
        
        # User preferences and learned patterns
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS user_preferences (
                id TEXT PRIMARY KEY,
                preference_type TEXT NOT NULL,
                preference_key TEXT NOT NULL,
                preference_value TEXT NOT NULL,
                confidence REAL DEFAULT 0.6,
                learned_from TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_confirmed TIMESTAMP,
                memory_entry_id TEXT,
                FOREIGN KEY (memory_entry_id) REFERENCES memory_entries (id),
                UNIQUE(preference_type, preference_key)
            )
        ''')
        
        # Command execution history with learning
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS command_executions (
                id TEXT PRIMARY KEY,
                command TEXT NOT NULL,
                output TEXT,
                success BOOLEAN NOT NULL,
                execution_time REAL,
                context TEXT,
                user_feedback INTEGER,
                pattern_learned BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                memory_entry_id TEXT,
                FOREIGN KEY (memory_entry_id) REFERENCES memory_entries (id)
            )
        ''')
        
        # Knowledge validation and cross-referencing
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS knowledge_validation (
                id TEXT PRIMARY KEY,
                memory_entry_id TEXT NOT NULL,
                validation_source TEXT,
                validation_result TEXT,
                confidence_adjustment REAL DEFAULT 0.0,
                validated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                validator_type TEXT,
                FOREIGN KEY (memory_entry_id) REFERENCES memory_entries (id)
            )
        ''')
        
        # Learning patterns and feedback
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS learning_patterns (
                id TEXT PRIMARY KEY,
                pattern_type TEXT NOT NULL,
                pattern_data TEXT NOT NULL,
                frequency INTEGER DEFAULT 1,
                confidence REAL DEFAULT 0.5,
                last_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                effectiveness_score REAL DEFAULT 0.5,
                memory_entry_id TEXT,
                FOREIGN KEY (memory_entry_id) REFERENCES memory_entries (id)
            )
        ''')
        
        # Knowledge graph connections
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS knowledge_connections (
                id TEXT PRIMARY KEY,
                source_entry_id TEXT NOT NULL,
                target_entry_id TEXT NOT NULL,
                connection_type TEXT NOT NULL,
                strength REAL DEFAULT 0.5,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (source_entry_id) REFERENCES memory_entries (id),
                FOREIGN KEY (target_entry_id) REFERENCES memory_entries (id),
                UNIQUE(source_entry_id, target_entry_id, connection_type)
            )
        ''')
        
        # Create indexes for performance
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_memory_type ON memory_entries (memory_type)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_created_at ON memory_entries (created_at)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_confidence ON memory_entries (confidence)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_tags ON memory_entries (tags)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_web_url ON web_content (url)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_command ON command_executions (command)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_pattern_type ON learning_patterns (pattern_type)')
        
        conn.commit()
        conn.close()
        logger.info("🗄️ Advanced memory database initialized")
    
    def _init_embeddings(self):
        """Initialize embeddings integration."""
        try:
            from embeddings import EmbeddingManager
            self.embedding_manager = EmbeddingManager(self.config)
            logger.info("🧠 Embeddings integration enabled for advanced memory")
        except Exception as e:
            logger.warning(f"Embeddings not available for advanced memory: {e}")
    
    def _generate_id(self, content: str, prefix: str = "") -> str:
        """Generate unique ID for content."""
        timestamp = datetime.now().isoformat()
        combined = f"{prefix}_{content}_{timestamp}"
        return hashlib.sha256(combined.encode()).hexdigest()[:16]

    def store_memory(self, memory_type: MemoryType, content: str,
                    metadata: Optional[Dict[str, Any]] = None,
                    source_type: SourceType = SourceType.USER_INPUT,
                    confidence: float = 0.6,
                    tags: Optional[List[str]] = None) -> str:
        """Store a memory entry in the database."""
        try:
            memory_id = self._generate_id(content, memory_type.value)
            now = datetime.now()

            # Store in embeddings if available
            embedding_id = None
            if self.embedding_manager:
                try:
                    embedding_metadata = {
                        'memory_id': memory_id,
                        'memory_type': memory_type.value,
                        'source_type': source_type.value,
                        'confidence': confidence,
                        **(metadata or {})
                    }
                    self.embedding_manager.store_interaction(
                        content,
                        f"Memory entry: {memory_type.value}",
                        embedding_metadata
                    )
                    embedding_id = memory_id
                except Exception as e:
                    logger.debug(f"Failed to store embedding: {e}")

            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO memory_entries
                (id, memory_type, content, metadata, confidence, source_type,
                 created_at, last_accessed, tags, embedding_id)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                memory_id, memory_type.value, content,
                json.dumps(metadata or {}), confidence, source_type.value,
                now.isoformat(), now.isoformat(),
                json.dumps(tags or []), embedding_id
            ))

            conn.commit()
            conn.close()

            logger.debug(f"Stored memory entry: {memory_id}")
            return memory_id

        except Exception as e:
            logger.error(f"Failed to store memory: {e}")
            return ""

    def retrieve_memories(self, query: Optional[str] = None,
                         memory_type: Optional[MemoryType] = None,
                         max_results: int = 10,
                         min_confidence: float = 0.0) -> List[MemoryEntry]:
        """Retrieve memories based on query and filters."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # Build query conditions
            conditions = ["is_archived = FALSE"]
            params = []

            if memory_type:
                conditions.append("memory_type = ?")
                params.append(memory_type.value)

            if min_confidence > 0:
                conditions.append("confidence >= ?")
                params.append(min_confidence)

            # Use semantic search if embeddings available and query provided
            if query and self.embedding_manager:
                try:
                    similar_entries = self.embedding_manager.find_similar_interactions(
                        query, max_results * 2
                    )

                    if similar_entries:
                        # Extract memory IDs from embedding results
                        memory_ids = []
                        for entry in similar_entries:
                            metadata = entry.get('metadata', {})
                            if 'memory_id' in metadata:
                                memory_ids.append(metadata['memory_id'])

                        if memory_ids:
                            placeholders = ','.join(['?' for _ in memory_ids])
                            conditions.append(f"id IN ({placeholders})")
                            params.extend(memory_ids)
                except Exception as e:
                    logger.debug(f"Semantic search failed, using text search: {e}")

            # Fallback to text search if no semantic search
            if query and not any('id IN' in cond for cond in conditions):
                conditions.append("(content LIKE ? OR tags LIKE ?)")
                params.extend([f"%{query}%", f"%{query}%"])

            where_clause = " AND ".join(conditions)
            sql = f'''
                SELECT id, memory_type, content, metadata, confidence, source_type,
                       created_at, last_accessed, access_count, tags, related_entries
                FROM memory_entries
                WHERE {where_clause}
                ORDER BY confidence DESC, last_accessed DESC
                LIMIT ?
            '''
            params.append(max_results)

            cursor.execute(sql, params)
            results = cursor.fetchall()

            # Update access counts
            if results:
                memory_ids = [row[0] for row in results]
                placeholders = ','.join(['?' for _ in memory_ids])
                cursor.execute(f'''
                    UPDATE memory_entries
                    SET access_count = access_count + 1, last_accessed = ?
                    WHERE id IN ({placeholders})
                ''', [datetime.now().isoformat()] + memory_ids)
                conn.commit()

            conn.close()

            # Convert to MemoryEntry objects
            memories = []
            for row in results:
                memories.append(MemoryEntry(
                    id=row[0],
                    memory_type=MemoryType(row[1]),
                    content=row[2],
                    metadata=json.loads(row[3] or '{}'),
                    confidence=row[4],
                    source_type=SourceType(row[5]),
                    created_at=datetime.fromisoformat(row[6]),
                    last_accessed=datetime.fromisoformat(row[7]),
                    access_count=row[8],
                    tags=json.loads(row[9] or '[]'),
                    related_entries=json.loads(row[10] or '[]')
                ))

            return memories

        except Exception as e:
            logger.error(f"Failed to retrieve memories: {e}")
            return []

    def store_conversation(self, user_input: str, ai_response: str,
                          context_used: Optional[str] = None,
                          session_id: Optional[str] = None,
                          project_context: Optional[str] = None) -> str:
        """Store a conversation with enhanced metadata."""
        try:
            # Store as memory entry
            metadata = {
                'session_id': session_id or 'default',
                'project_context': project_context or os.getcwd(),
                'context_used': context_used or '',
                'conversation_type': 'interactive'
            }

            memory_id = self.store_memory(
                MemoryType.CONVERSATION,
                f"User: {user_input}\nAssistant: {ai_response}",
                metadata,
                SourceType.USER_INPUT,
                confidence=0.8
            )

            # Store in conversations table
            conv_id = self._generate_id(user_input, "conv")
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO conversations
                (id, session_id, user_input, ai_response, context_used,
                 created_at, project_context, memory_entry_id)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                conv_id, session_id or 'default', user_input, ai_response,
                context_used or '', datetime.now().isoformat(),
                project_context or os.getcwd(), memory_id
            ))

            conn.commit()
            conn.close()

            return conv_id

        except Exception as e:
            logger.error(f"Failed to store conversation: {e}")
            return ""

    def extract_web_content(self, url: str, timeout: int = 10) -> Optional[WebContent]:
        """Extract and analyze content from a web page."""
        if not _HAS_REQUESTS or requests is None:
            logger.warning("Requests library not available - cannot extract web content")
            return None
            
        try:
            headers = {
                'User-Agent': 'SetupAgent/1.0 (Educational Purpose)',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
            }

            response = requests.get(url, headers=headers, timeout=timeout)
            response.raise_for_status()

            content = response.text

            # Extract title
            title_match = re.search(r'<title[^>]*>([^<]+)</title>', content, re.IGNORECASE)
            title = title_match.group(1).strip() if title_match else urlparse(url).netloc

            # Extract main content (simple heuristic)
            # Remove script and style tags
            content_clean = re.sub(r'<script[^>]*>.*?</script>', '', content, flags=re.DOTALL | re.IGNORECASE)
            content_clean = re.sub(r'<style[^>]*>.*?</style>', '', content_clean, flags=re.DOTALL | re.IGNORECASE)

            # Extract text content
            text_content = re.sub(r'<[^>]+>', ' ', content_clean)
            text_content = re.sub(r'\s+', ' ', text_content).strip()

            # Limit content length
            if len(text_content) > 5000:
                text_content = text_content[:5000] + "..."

            # Extract code examples
            code_examples = []
            code_patterns = [
                r'<code[^>]*>(.*?)</code>',
                r'<pre[^>]*>(.*?)</pre>',
                r'```([^`]+)```'
            ]

            for pattern in code_patterns:
                matches = re.findall(pattern, content, re.DOTALL | re.IGNORECASE)
                for match in matches:
                    clean_code = re.sub(r'<[^>]+>', '', match).strip()
                    if clean_code and len(clean_code) > 10:
                        code_examples.append(clean_code[:500])

            # Generate summary (first few sentences)
            sentences = re.split(r'[.!?]+', text_content)
            summary = '. '.join(sentences[:3]).strip()
            if len(summary) > 300:
                summary = summary[:300] + "..."

            # Extract key facts (simple heuristic)
            key_facts = []
            fact_patterns = [
                r'(?:is|are|was|were|will be|can be|should be|must be)\s+([^.!?]{10,100})',
                r'(?:install|run|execute|use|configure)\s+([^.!?]{10,100})',
                r'(?:version|requires|supports|compatible)\s+([^.!?]{10,100})'
            ]

            for pattern in fact_patterns:
                matches = re.findall(pattern, text_content, re.IGNORECASE)
                for match in matches[:5]:  # Limit to 5 facts
                    fact = match.strip()
                    if fact and len(fact) > 10:
                        key_facts.append(fact)

            # Generate tags based on content
            tags = self._generate_content_tags(title, text_content, url)

            # Assess source reliability
            reliability = self._assess_source_reliability(url, title, content)

            return WebContent(
                url=url,
                title=title,
                content=text_content,
                summary=summary,
                key_facts=key_facts,
                code_examples=code_examples,
                tags=tags,
                extracted_at=datetime.now(),
                source_reliability=reliability
            )

        except Exception as e:
            logger.error(f"Failed to extract content from {url}: {e}")
            return None

    def _generate_content_tags(self, title: str, content: str, url: str) -> List[str]:
        """Generate tags based on content analysis."""
        tags = []

        # URL-based tags
        domain = urlparse(url).netloc.lower()
        if 'github' in domain:
            tags.append('github')
        elif 'stackoverflow' in domain:
            tags.append('stackoverflow')
        elif 'docs' in domain or 'documentation' in domain:
            tags.append('documentation')

        # Content-based tags
        content_lower = (title + ' ' + content).lower()

        tech_keywords = {
            'python': ['python', 'pip', 'conda', 'virtualenv'],
            'javascript': ['javascript', 'node', 'npm', 'js'],
            'docker': ['docker', 'container', 'dockerfile'],
            'git': ['git', 'github', 'repository', 'commit'],
            'database': ['database', 'sql', 'mysql', 'postgresql'],
            'web': ['html', 'css', 'web', 'browser'],
            'api': ['api', 'rest', 'endpoint', 'json'],
            'setup': ['install', 'setup', 'configure', 'installation'],
            'troubleshooting': ['error', 'fix', 'problem', 'issue', 'debug']
        }

        for tag, keywords in tech_keywords.items():
            if any(keyword in content_lower for keyword in keywords):
                tags.append(tag)

        return list(set(tags))  # Remove duplicates

    def _assess_source_reliability(self, url: str, title: str, content: str) -> float:
        """Assess the reliability of a web source."""
        reliability = 0.5  # Base reliability

        domain = urlparse(url).netloc.lower()

        # High reliability domains
        high_reliability_domains = [
            'docs.python.org', 'developer.mozilla.org', 'docs.microsoft.com',
            'docs.docker.com', 'kubernetes.io', 'github.com', 'gitlab.com'
        ]

        # Medium reliability domains
        medium_reliability_domains = [
            'stackoverflow.com', 'medium.com', 'dev.to', 'reddit.com'
        ]

        if any(trusted in domain for trusted in high_reliability_domains):
            reliability = 0.9
        elif any(medium in domain for medium in medium_reliability_domains):
            reliability = 0.7
        elif 'wiki' in domain:
            reliability = 0.6

        # Adjust based on content quality indicators
        if len(content) > 1000:  # Substantial content
            reliability += 0.05

        if 'example' in content.lower() or 'tutorial' in content.lower():
            reliability += 0.05

        if any(word in title.lower() for word in ['official', 'documentation', 'guide']):
            reliability += 0.1

        return min(reliability, 1.0)

    def store_web_content(self, web_content: WebContent) -> str:
        """Store extracted web content in the database."""
        try:
            # Store as memory entry
            metadata = {
                'url': web_content.url,
                'title': web_content.title,
                'source_reliability': web_content.source_reliability,
                'extracted_at': web_content.extracted_at.isoformat(),
                'key_facts_count': len(web_content.key_facts),
                'code_examples_count': len(web_content.code_examples)
            }

            memory_id = self.store_memory(
                MemoryType.WEB_CONTENT,
                web_content.content,
                metadata,
                SourceType.WEB_SEARCH,
                confidence=web_content.source_reliability,
                tags=web_content.tags
            )

            # Store in web_content table
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT OR REPLACE INTO web_content
                (id, url, title, content, summary, key_facts, code_examples,
                 tags, source_reliability, last_updated, memory_entry_id)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                self._generate_id(web_content.url, "web"),
                web_content.url,
                web_content.title,
                web_content.content,
                web_content.summary,
                json.dumps(web_content.key_facts),
                json.dumps(web_content.code_examples),
                json.dumps(web_content.tags),
                web_content.source_reliability,
                web_content.extracted_at.isoformat(),
                memory_id
            ))

            conn.commit()
            conn.close()

            logger.info(f"Stored web content: {web_content.url}")
            return memory_id

        except Exception as e:
            logger.error(f"Failed to store web content: {e}")
            return ""

    def search_and_learn(self, query: str, max_results: int = 5) -> List[WebContent]:
        """Perform web search and automatically learn from results."""
        try:
            # Import search functions from modular architecture
            from setup_agent.search.web_search import search_manager
            
            # Convert search results to the expected format
            search_results_raw = search_manager.search(query, max_results=max_results)
            search_results = []
            for result in search_results_raw:
                search_results.append({
                    'title': result.title,
                    'url': result.url,
                    'snippet': result.snippet,
                    'engine': result.engine
                })
            learned_content = []

            for result in search_results:
                if 'error' in result:
                    continue

                url = result.get('url', '')
                if not url:
                    continue

                # Check if we already have this content
                existing = self._get_existing_web_content(url)
                if existing and (datetime.now() - existing['last_updated']).days < 7:
                    continue  # Skip if recently updated

                # Extract content
                web_content = self.extract_web_content(url)
                if web_content:
                    # Store the content
                    self.store_web_content(web_content)
                    learned_content.append(web_content)

                    # Add search context
                    self._link_search_to_content(query, web_content)

                # Rate limiting
                time.sleep(1)

            logger.info(f"Learned from {len(learned_content)} web sources for query: {query}")
            return learned_content

        except Exception as e:
            logger.error(f"Failed to search and learn: {e}")
            return []

    def _get_existing_web_content(self, url: str) -> Optional[Dict[str, Any]]:
        """Check if web content already exists."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT last_updated, source_reliability
                FROM web_content
                WHERE url = ?
            ''', (url,))

            result = cursor.fetchone()
            conn.close()

            if result:
                return {
                    'last_updated': datetime.fromisoformat(result[0]),
                    'source_reliability': result[1]
                }
            return None

        except Exception as e:
            logger.debug(f"Error checking existing content: {e}")
            return None

    def _link_search_to_content(self, query: str, web_content: WebContent):
        """Create connections between search queries and found content."""
        try:
            # Store the search query as a learning pattern
            pattern_id = self.store_learning_pattern(
                "search_query",
                {
                    'query': query,
                    'found_url': web_content.url,
                    'relevance_score': web_content.source_reliability
                }
            )

            # Create knowledge connections if we find related content
            related_memories = self.retrieve_memories(query, max_results=3)
            for memory in related_memories:
                self.create_knowledge_connection(
                    pattern_id, memory.id, "search_related", 0.6
                )

        except Exception as e:
            logger.debug(f"Failed to link search to content: {e}")

    def store_learning_pattern(self, pattern_type: str, pattern_data: Dict[str, Any]) -> str:
        """Store a learning pattern for future reference."""
        try:
            pattern_id = self._generate_id(str(pattern_data), "pattern")

            # Store as memory entry
            metadata = {
                'pattern_type': pattern_type,
                'learned_at': datetime.now().isoformat()
            }

            memory_id = self.store_memory(
                MemoryType.LEARNING_PATTERN,
                json.dumps(pattern_data),
                metadata,
                SourceType.AI_GENERATED,
                confidence=0.7
            )

            # Store in learning_patterns table
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT OR REPLACE INTO learning_patterns
                (id, pattern_type, pattern_data, frequency, confidence,
                 last_seen, memory_entry_id)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (
                pattern_id, pattern_type, json.dumps(pattern_data),
                1, 0.7, datetime.now().isoformat(), memory_id
            ))

            conn.commit()
            conn.close()

            return pattern_id

        except Exception as e:
            logger.error(f"Failed to store learning pattern: {e}")
            return ""

    def create_knowledge_connection(self, source_id: str, target_id: str,
                                  connection_type: str, strength: float = 0.5) -> bool:
        """Create a connection between two knowledge entries."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT OR REPLACE INTO knowledge_connections
                (id, source_entry_id, target_entry_id, connection_type, strength)
                VALUES (?, ?, ?, ?, ?)
            ''', (
                self._generate_id(f"{source_id}_{target_id}", "conn"),
                source_id, target_id, connection_type, strength
            ))

            conn.commit()
            conn.close()

            return True

        except Exception as e:
            logger.error(f"Failed to create knowledge connection: {e}")
            return False

    def record_feedback(self, memory_id: str, feedback_score: int,
                       feedback_type: str = "general") -> bool:
        """Record user feedback on a memory entry."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # Update memory confidence based on feedback
            if feedback_score >= 4:  # Positive feedback
                confidence_adjustment = 0.1
            elif feedback_score <= 2:  # Negative feedback
                confidence_adjustment = -0.1
            else:  # Neutral feedback
                confidence_adjustment = 0.0

            cursor.execute('''
                UPDATE memory_entries
                SET confidence = MIN(1.0, MAX(0.1, confidence + ?)),
                    access_count = access_count + 1,
                    last_accessed = ?
                WHERE id = ?
            ''', (confidence_adjustment, datetime.now().isoformat(), memory_id))

            # Store feedback in validation table
            cursor.execute('''
                INSERT INTO knowledge_validation
                (id, memory_entry_id, validation_source, validation_result,
                 confidence_adjustment, validator_type)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (
                self._generate_id(f"feedback_{memory_id}", "validation"),
                memory_id, "user_feedback", str(feedback_score),
                confidence_adjustment, feedback_type
            ))

            conn.commit()
            conn.close()

            logger.info(f"Recorded feedback for memory {memory_id}: {feedback_score}")
            return True

        except Exception as e:
            logger.error(f"Failed to record feedback: {e}")
            return False

    def learn_from_correction(self, original_response: str, corrected_response: str,
                             context: str = "") -> str:
        """Learn from user corrections to improve future responses."""
        try:
            # Store the correction as a learning pattern
            correction_data = {
                'original_response': original_response,
                'corrected_response': corrected_response,
                'context': context,
                'correction_type': 'user_correction',
                'learned_at': datetime.now().isoformat()
            }

            pattern_id = self.store_learning_pattern("correction", correction_data)

            # Store as high-confidence procedural knowledge
            metadata = {
                'correction_source': 'user',
                'original_response': original_response,
                'context': context
            }

            memory_id = self.store_memory(
                MemoryType.PROCEDURAL_KNOWLEDGE,
                corrected_response,
                metadata,
                SourceType.USER_INPUT,
                confidence=0.9,  # High confidence for user corrections
                tags=['correction', 'user_feedback']
            )

            # Create connection between correction pattern and memory
            self.create_knowledge_connection(
                pattern_id, memory_id, "correction_link", 0.9
            )

            logger.info(f"Learned from user correction: {memory_id}")
            return memory_id

        except Exception as e:
            logger.error(f"Failed to learn from correction: {e}")
            return ""

    def identify_frequent_patterns(self, min_frequency: int = 3) -> List[Dict[str, Any]]:
        """Identify frequently occurring patterns for optimization."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT pattern_type, pattern_data, frequency, confidence,
                       effectiveness_score, last_seen
                FROM learning_patterns
                WHERE frequency >= ?
                ORDER BY frequency DESC, effectiveness_score DESC
                LIMIT 20
            ''', (min_frequency,))

            results = cursor.fetchall()
            conn.close()

            patterns = []
            for row in results:
                patterns.append({
                    'pattern_type': row[0],
                    'pattern_data': json.loads(row[1]),
                    'frequency': row[2],
                    'confidence': row[3],
                    'effectiveness_score': row[4],
                    'last_seen': row[5]
                })

            return patterns

        except Exception as e:
            logger.error(f"Failed to identify patterns: {e}")
            return []

    def get_intelligent_context(self, query: str, max_context_items: int = 5) -> str:
        """Get intelligent context by combining multiple memory sources."""
        try:
            context_parts = []

            # 1. Get semantically similar memories
            similar_memories = self.retrieve_memories(
                query, max_results=max_context_items, min_confidence=0.5
            )

            if similar_memories:
                context_parts.append("📚 **Relevant Knowledge:**")
                for i, memory in enumerate(similar_memories[:3], 1):
                    confidence_pct = memory.confidence * 100
                    context_parts.append(
                        f"{i}. [{memory.memory_type.value}] {memory.content[:200]}... "
                        f"(Confidence: {confidence_pct:.0f}%)"
                    )
                context_parts.append("")

            # 2. Get related web content
            web_content = self._get_related_web_content(query, max_results=2)
            if web_content:
                context_parts.append("🌐 **Web Knowledge:**")
                for content in web_content:
                    context_parts.append(f"• {content['title']}: {content['summary']}")
                    if content['key_facts']:
                        facts = json.loads(content['key_facts'])
                        for fact in facts[:2]:
                            context_parts.append(f"  - {fact}")
                context_parts.append("")

            # 3. Get learning patterns
            patterns = self._get_relevant_patterns(query)
            if patterns:
                context_parts.append("🧠 **Learned Patterns:**")
                for pattern in patterns[:2]:
                    pattern_data = json.loads(pattern['pattern_data'])
                    context_parts.append(
                        f"• {pattern['pattern_type']}: Used {pattern['frequency']} times"
                    )
                context_parts.append("")

            # 4. Get user preferences
            preferences = self._get_relevant_preferences(query)
            if preferences:
                context_parts.append("⚙️ **User Preferences:**")
                for pref in preferences:
                    context_parts.append(
                        f"• {pref['preference_key']}: {pref['preference_value']}"
                    )
                context_parts.append("")

            return "\n".join(context_parts) if context_parts else ""

        except Exception as e:
            logger.error(f"Failed to get intelligent context: {e}")
            return ""

    def _get_related_web_content(self, query: str, max_results: int = 3) -> List[Dict[str, Any]]:
        """Get web content related to the query."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT title, summary, key_facts, source_reliability, url
                FROM web_content
                WHERE (title LIKE ? OR summary LIKE ? OR tags LIKE ?)
                  AND source_reliability > 0.5
                ORDER BY source_reliability DESC, last_updated DESC
                LIMIT ?
            ''', (f"%{query}%", f"%{query}%", f"%{query}%", max_results))

            results = cursor.fetchall()
            conn.close()

            return [
                {
                    'title': row[0],
                    'summary': row[1],
                    'key_facts': row[2],
                    'source_reliability': row[3],
                    'url': row[4]
                }
                for row in results
            ]

        except Exception as e:
            logger.debug(f"Failed to get related web content: {e}")
            return []

    def _get_relevant_patterns(self, query: str) -> List[Dict[str, Any]]:
        """Get learning patterns relevant to the query."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT pattern_type, pattern_data, frequency, effectiveness_score
                FROM learning_patterns
                WHERE pattern_data LIKE ?
                  AND effectiveness_score > 0.5
                ORDER BY frequency DESC, effectiveness_score DESC
                LIMIT 3
            ''', (f"%{query}%",))

            results = cursor.fetchall()
            conn.close()

            return [
                {
                    'pattern_type': row[0],
                    'pattern_data': row[1],
                    'frequency': row[2],
                    'effectiveness_score': row[3]
                }
                for row in results
            ]

        except Exception as e:
            logger.debug(f"Failed to get relevant patterns: {e}")
            return []

    def _get_relevant_preferences(self, query: str) -> List[Dict[str, Any]]:
        """Get user preferences relevant to the query."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT preference_key, preference_value, confidence
                FROM user_preferences
                WHERE (preference_key LIKE ? OR preference_value LIKE ?)
                  AND confidence > 0.5
                ORDER BY confidence DESC
                LIMIT 3
            ''', (f"%{query}%", f"%{query}%"))

            results = cursor.fetchall()
            conn.close()

            return [
                {
                    'preference_key': row[0],
                    'preference_value': row[1],
                    'confidence': row[2]
                }
                for row in results
            ]

        except Exception as e:
            logger.debug(f"Failed to get relevant preferences: {e}")
            return []

    def cleanup_old_memories(self) -> Dict[str, int]:
        """Clean up old memories based on retention policies."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cleanup_stats = {}

            for memory_type, retention_period in self.retention_policies.items():
                cutoff_date = datetime.now() - retention_period

                # Archive old memories instead of deleting
                cursor.execute('''
                    UPDATE memory_entries
                    SET is_archived = TRUE
                    WHERE memory_type = ?
                      AND created_at < ?
                      AND is_archived = FALSE
                      AND access_count < 5  -- Keep frequently accessed memories
                      AND confidence < 0.8  -- Keep high-confidence memories
                ''', (memory_type.value, cutoff_date.isoformat()))

                archived_count = cursor.rowcount
                cleanup_stats[memory_type.value] = archived_count

            # Clean up very old, low-value entries
            very_old_date = datetime.now() - timedelta(days=365)
            cursor.execute('''
                DELETE FROM memory_entries
                WHERE created_at < ?
                  AND access_count = 0
                  AND confidence < 0.3
                  AND is_archived = TRUE
            ''', (very_old_date.isoformat(),))

            cleanup_stats['deleted'] = cursor.rowcount

            conn.commit()
            conn.close()

            logger.info(f"Memory cleanup completed: {cleanup_stats}")
            return cleanup_stats

        except Exception as e:
            logger.error(f"Failed to cleanup memories: {e}")
            return {}

    def consolidate_similar_memories(self, similarity_threshold: float = 0.9) -> int:
        """Consolidate very similar memories to reduce redundancy."""
        try:
            if not self.embedding_manager:
                logger.warning("Embeddings not available for memory consolidation")
                return 0

            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # Get all non-archived memories
            cursor.execute('''
                SELECT id, content, confidence, access_count, created_at
                FROM memory_entries
                WHERE is_archived = FALSE
                ORDER BY confidence DESC, access_count DESC
            ''')

            memories = cursor.fetchall()
            consolidated_count = 0

            # Compare memories for similarity
            for i, memory1 in enumerate(memories):
                if i % 10 == 0:  # Progress indicator
                    logger.debug(f"Consolidation progress: {i}/{len(memories)}")

                for memory2 in memories[i+1:]:
                    # Use embedding similarity if available
                    try:
                        similar_entries = self.embedding_manager.find_similar_interactions(
                            memory1[1], max_results=5
                        )

                        # Check if memory2 is in similar entries with high similarity
                        for entry in similar_entries:
                            if (entry.get('similarity', 0) > similarity_threshold and
                                memory2[0] in str(entry.get('metadata', {}))):

                                # Consolidate: keep the better memory, archive the other
                                if memory1[2] >= memory2[2]:  # Compare confidence
                                    keep_id, archive_id = memory1[0], memory2[0]
                                else:
                                    keep_id, archive_id = memory2[0], memory1[0]

                                # Update the kept memory with combined access count
                                cursor.execute('''
                                    UPDATE memory_entries
                                    SET access_count = access_count + ?,
                                        confidence = MIN(1.0, confidence + 0.05)
                                    WHERE id = ?
                                ''', (memory2[3], keep_id))

                                # Archive the redundant memory
                                cursor.execute('''
                                    UPDATE memory_entries
                                    SET is_archived = TRUE
                                    WHERE id = ?
                                ''', (archive_id,))

                                consolidated_count += 1
                                break

                    except Exception as e:
                        logger.debug(f"Error in similarity comparison: {e}")
                        continue

            conn.commit()
            conn.close()

            logger.info(f"Consolidated {consolidated_count} similar memories")
            return consolidated_count

        except Exception as e:
            logger.error(f"Failed to consolidate memories: {e}")
            return 0

    def get_memory_statistics(self) -> Dict[str, Any]:
        """Get comprehensive statistics about the memory system."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            stats = {}

            # Memory counts by type
            cursor.execute('''
                SELECT memory_type, COUNT(*), AVG(confidence), AVG(access_count)
                FROM memory_entries
                WHERE is_archived = FALSE
                GROUP BY memory_type
            ''')

            memory_stats = {}
            for row in cursor.fetchall():
                memory_stats[row[0]] = {
                    'count': row[1],
                    'avg_confidence': round(row[2], 2),
                    'avg_access_count': round(row[3], 2)
                }

            stats['memory_by_type'] = memory_stats

            # Overall statistics
            cursor.execute('''
                SELECT
                    COUNT(*) as total_memories,
                    COUNT(CASE WHEN is_archived = FALSE THEN 1 END) as active_memories,
                    COUNT(CASE WHEN is_archived = TRUE THEN 1 END) as archived_memories,
                    AVG(confidence) as avg_confidence,
                    MAX(access_count) as max_access_count
                FROM memory_entries
            ''')

            overall = cursor.fetchone()
            stats['overall'] = {
                'total_memories': overall[0],
                'active_memories': overall[1],
                'archived_memories': overall[2],
                'avg_confidence': round(overall[3] or 0, 2),
                'max_access_count': overall[4] or 0
            }

            # Web content statistics
            cursor.execute('''
                SELECT COUNT(*), AVG(source_reliability), COUNT(DISTINCT url)
                FROM web_content
            ''')

            web_stats = cursor.fetchone()
            stats['web_content'] = {
                'total_pages': web_stats[0],
                'avg_reliability': round(web_stats[1] or 0, 2),
                'unique_urls': web_stats[2]
            }

            # Learning patterns
            cursor.execute('''
                SELECT COUNT(*), AVG(frequency), AVG(effectiveness_score)
                FROM learning_patterns
            ''')

            pattern_stats = cursor.fetchone()
            stats['learning_patterns'] = {
                'total_patterns': pattern_stats[0],
                'avg_frequency': round(pattern_stats[1] or 0, 2),
                'avg_effectiveness': round(pattern_stats[2] or 0, 2)
            }

            conn.close()
            return stats

        except Exception as e:
            logger.error(f"Failed to get memory statistics: {e}")
            return {}

    def export_knowledge_base(self, export_path: str) -> bool:
        """Export the knowledge base to a JSON file."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            export_data = {
                'export_timestamp': datetime.now().isoformat(),
                'version': '1.0',
                'memories': [],
                'web_content': [],
                'learning_patterns': [],
                'user_preferences': []
            }

            # Export memories
            cursor.execute('''
                SELECT id, memory_type, content, metadata, confidence,
                       source_type, created_at, tags
                FROM memory_entries
                WHERE is_archived = FALSE AND confidence > 0.5
            ''')

            for row in cursor.fetchall():
                export_data['memories'].append({
                    'id': row[0],
                    'type': row[1],
                    'content': row[2],
                    'metadata': json.loads(row[3] or '{}'),
                    'confidence': row[4],
                    'source_type': row[5],
                    'created_at': row[6],
                    'tags': json.loads(row[7] or '[]')
                })

            # Export web content
            cursor.execute('''
                SELECT url, title, summary, key_facts, source_reliability, tags
                FROM web_content
                WHERE source_reliability > 0.6
            ''')

            for row in cursor.fetchall():
                export_data['web_content'].append({
                    'url': row[0],
                    'title': row[1],
                    'summary': row[2],
                    'key_facts': json.loads(row[3] or '[]'),
                    'source_reliability': row[4],
                    'tags': json.loads(row[5] or '[]')
                })

            conn.close()

            # Write to file
            with open(export_path, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, indent=2, ensure_ascii=False)

            logger.info(f"Knowledge base exported to {export_path}")
            return True

        except Exception as e:
            logger.error(f"Failed to export knowledge base: {e}")
            return False
