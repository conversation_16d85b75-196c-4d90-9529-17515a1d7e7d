"""
🤖 SetupAgent - LLM-Powered Intelligent Assistant

A comprehensive Python-based intelligent assistant that integrates with multiple LLM providers
for command execution, web search, and memory management with GPU optimization.
"""

__version__ = "2.0.0"
__author__ = "SetupAgent Development Team"

# Core imports for easy access
from .core.config import Config
from .core.exceptions import SetupAgentError
from .llm.factory import LLMProviderFactory
from .memory.manager import MemoryManager

__all__ = [
    "Config",
    "SetupAgentError", 
    "LLMProviderFactory",
    "MemoryManager"
]
