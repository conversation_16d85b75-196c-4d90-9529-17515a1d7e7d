#!/usr/bin/env python3
"""
🔗 Memory Integration Module for SetupAgent
Integrates the advanced memory system with the existing SetupAgent functionality.
"""

import os
import json
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime

logger = logging.getLogger(__name__)

# Global memory system instance
advanced_memory = None

def initialize_advanced_memory(config: Dict[str, Any]) -> bool:
    """Initialize the advanced memory system."""
    global advanced_memory
    
    try:
        from advanced_memory import AdvancedMemorySystem
        advanced_memory = AdvancedMemorySystem(config)
        logger.info("🧠 Advanced memory system initialized")
        return True
    except Exception as e:
        logger.error(f"Failed to initialize advanced memory: {e}")
        return False

def enhanced_conversation_handler(user_input: str, ai_response: str, 
                                context_used: str = "", 
                                session_id: str = "default") -> str:
    """Enhanced conversation handler with advanced memory integration."""
    try:
        if not advanced_memory:
            return ai_response
        
        # Store the conversation
        conv_id = advanced_memory.store_conversation(
            user_input, ai_response, context_used, session_id
        )
        
        # Learn from patterns in the conversation
        _learn_from_conversation(user_input, ai_response)
        
        return ai_response
        
    except Exception as e:
        logger.error(f"Error in enhanced conversation handler: {e}")
        return ai_response

def get_enhanced_context(user_input: str, max_context_items: int = 5) -> str:
    """Get enhanced context using the advanced memory system."""
    try:
        if not advanced_memory:
            return ""
        
        # Get intelligent context from memory system
        context = advanced_memory.get_intelligent_context(user_input, max_context_items)
        
        # If we have web search capabilities, learn from relevant searches
        if _should_search_web(user_input):
            _perform_learning_search(user_input)
        
        return context
        
    except Exception as e:
        logger.error(f"Error getting enhanced context: {e}")
        return ""

def store_command_execution(command: str, output: str, success: bool, 
                          execution_time: float = 0.0) -> str:
    """Store command execution with learning."""
    try:
        if not advanced_memory:
            return ""
        
        # Store in advanced memory
        from advanced_memory import MemoryType, SourceType
        
        metadata = {
            'command': command,
            'success': success,
            'execution_time': execution_time,
            'output_length': len(output),
            'timestamp': datetime.now().isoformat()
        }
        
        memory_id = advanced_memory.store_memory(
            MemoryType.COMMAND_EXECUTION,
            f"Command: {command}\nOutput: {output[:500]}...",
            metadata,
            SourceType.USER_INPUT,
            confidence=0.9 if success else 0.5
        )
        
        # Learn patterns from command execution
        _learn_command_patterns(command, output, success)
        
        return memory_id
        
    except Exception as e:
        logger.error(f"Error storing command execution: {e}")
        return ""

def handle_user_feedback(memory_id: str, feedback_score: int, 
                        feedback_text: str = "") -> bool:
    """Handle user feedback on responses."""
    try:
        if not advanced_memory:
            return False
        
        # Record the feedback
        success = advanced_memory.record_feedback(memory_id, feedback_score)
        
        # If it's a correction, learn from it
        if feedback_text and feedback_score <= 2:
            advanced_memory.learn_from_correction(
                "Previous response", feedback_text, "User correction"
            )
        
        return success
        
    except Exception as e:
        logger.error(f"Error handling user feedback: {e}")
        return False

def search_and_learn_from_web(query: str, max_results: int = 3) -> List[Dict[str, Any]]:
    """Perform web search and learn from results."""
    try:
        if not advanced_memory:
            return []
        
        learned_content = advanced_memory.search_and_learn(query, max_results)
        
        # Convert to simple dict format for compatibility
        results = []
        for content in learned_content:
            results.append({
                'url': content.url,
                'title': content.title,
                'summary': content.summary,
                'key_facts': content.key_facts,
                'reliability': content.source_reliability,
                'tags': content.tags
            })
        
        return results
        
    except Exception as e:
        logger.error(f"Error in search and learn: {e}")
        return []

def get_memory_statistics() -> Dict[str, Any]:
    """Get memory system statistics."""
    try:
        if not advanced_memory:
            return {}
        
        return advanced_memory.get_memory_statistics()
        
    except Exception as e:
        logger.error(f"Error getting memory statistics: {e}")
        return {}

def cleanup_memory_system() -> Dict[str, int]:
    """Perform memory cleanup and maintenance."""
    try:
        if not advanced_memory:
            return {}
        
        # Cleanup old memories
        cleanup_stats = advanced_memory.cleanup_old_memories()
        
        # Consolidate similar memories
        consolidated = advanced_memory.consolidate_similar_memories()
        cleanup_stats['consolidated'] = consolidated
        
        return cleanup_stats
        
    except Exception as e:
        logger.error(f"Error in memory cleanup: {e}")
        return {}

def export_knowledge_base(export_path: str = None) -> bool:
    """Export the knowledge base."""
    try:
        if not advanced_memory:
            return False
        
        if not export_path:
            export_path = f"knowledge_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        return advanced_memory.export_knowledge_base(export_path)
        
    except Exception as e:
        logger.error(f"Error exporting knowledge base: {e}")
        return False

# Helper functions

def _learn_from_conversation(user_input: str, ai_response: str):
    """Learn patterns from conversation."""
    try:
        # Detect question patterns
        if any(word in user_input.lower() for word in ['how', 'what', 'why', 'when', 'where']):
            pattern_data = {
                'question_type': 'information_seeking',
                'user_input': user_input[:100],
                'response_length': len(ai_response),
                'contains_code': 'code' in ai_response.lower() or '```' in ai_response
            }
            advanced_memory.store_learning_pattern('question_pattern', pattern_data)
        
        # Detect help requests
        if any(word in user_input.lower() for word in ['help', 'assist', 'support', 'problem']):
            pattern_data = {
                'request_type': 'help_seeking',
                'urgency': 'high' if any(word in user_input.lower() for word in ['urgent', 'critical', 'emergency']) else 'normal'
            }
            advanced_memory.store_learning_pattern('help_pattern', pattern_data)
            
    except Exception as e:
        logger.debug(f"Error learning from conversation: {e}")

def _learn_command_patterns(command: str, output: str, success: bool):
    """Learn patterns from command execution."""
    try:
        # Extract command type
        cmd_parts = command.strip().split()
        if cmd_parts:
            base_command = cmd_parts[0]
            
            pattern_data = {
                'base_command': base_command,
                'success_rate': 1.0 if success else 0.0,
                'output_length': len(output),
                'has_error': 'error' in output.lower() or 'failed' in output.lower()
            }
            
            advanced_memory.store_learning_pattern('command_pattern', pattern_data)
            
    except Exception as e:
        logger.debug(f"Error learning command patterns: {e}")

def _should_search_web(user_input: str) -> bool:
    """Determine if we should perform a web search for learning."""
    search_indicators = [
        'latest', 'current', 'new', 'recent', 'update', 'version',
        'how to', 'tutorial', 'guide', 'documentation', 'example'
    ]
    
    return any(indicator in user_input.lower() for indicator in search_indicators)

def _perform_learning_search(query: str):
    """Perform a learning-focused web search."""
    try:
        # Limit learning searches to avoid overwhelming the system
        if hasattr(_perform_learning_search, 'last_search'):
            time_since_last = datetime.now() - _perform_learning_search.last_search
            if time_since_last.total_seconds() < 300:  # 5 minutes cooldown
                return
        
        _perform_learning_search.last_search = datetime.now()
        
        # Perform the search and learning
        search_and_learn_from_web(query, max_results=2)
        
    except Exception as e:
        logger.debug(f"Error in learning search: {e}")

# Integration with existing setup_agent functions

def patch_setup_agent():
    """Patch the existing setup_agent with enhanced memory capabilities."""
    try:
        import setup_agent
        
        # Store original functions
        original_add_to_conversation = setup_agent.add_to_conversation
        original_handle_conversation = setup_agent.handle_conversation
        
        def enhanced_add_to_conversation(user_input: str, ai_response: str):
            """Enhanced version of add_to_conversation."""
            # Call original function
            original_add_to_conversation(user_input, ai_response)
            
            # Add advanced memory integration
            enhanced_conversation_handler(user_input, ai_response)
        
        def enhanced_handle_conversation(user_input: str, model: str = None):
            """Enhanced version of handle_conversation."""
            # Get enhanced context
            enhanced_context = get_enhanced_context(user_input)
            
            # Call original function (it will use the enhanced context through embeddings)
            response = original_handle_conversation(user_input, model)
            
            return response
        
        # Patch the functions
        setup_agent.add_to_conversation = enhanced_add_to_conversation
        setup_agent.handle_conversation = enhanced_handle_conversation
        
        logger.info("✅ SetupAgent patched with advanced memory capabilities")
        return True
        
    except Exception as e:
        logger.error(f"Failed to patch setup_agent: {e}")
        return False
