2025-06-06 15:56:22,401 INFO Loaded plugin: example_plugin
2025-06-06 15:58:56,315 INFO Loaded plugin: example_plugin
2025-06-06 15:59:57,368 INFO Loaded plugin: example_plugin
2025-06-06 16:00:47,524 INFO Loaded plugin: example_plugin
2025-06-06 17:06:07,526 WARNING Skipped malformed chat entry: {'invalid': 'entry'}
2025-06-06 17:06:07,526 WARNING Skipped malformed chat entry: {'timestamp': 'invalid', 'user_message': 'test', 'ai_response': 'test'}
2025-06-07 16:15:24,096 [    INFO] setup_agent.utils.logging_utils: Logging configured: level=INFO, file=agent.log
2025-06-07 16:15:25,184 [    INFO] faiss.loader: Loading faiss with AVX2 support.
2025-06-07 16:15:25,610 [    INFO] faiss.loader: Successfully loaded faiss with AVX2 support.
2025-06-07 16:15:25,619 [    INFO] faiss: Failed to load GPU Faiss: name 'GpuIndexIV<PERSON>lat' is not defined. Will not load constructor refs for GPU indexes. This is only an error if you're trying to use GPU Faiss.
2025-06-07 16:15:25,621 [    INFO] embeddings: 💻 FAISS using CPU (no GPU acceleration available)
2025-06-07 16:15:54,389 [    INFO] embeddings: ✅ Ollama model nomic-embed-text is available
2025-06-07 16:15:54,390 [    INFO] embeddings: Initialized Ollama embeddings with nomic-embed-text
2025-06-07 16:15:54,402 [    INFO] embeddings: Loaded existing FAISS index with 50 vectors
2025-06-07 16:15:54,402 [    INFO] embeddings: Initialized FAISS vector store
2025-06-07 16:15:54,402 [    INFO] setup_agent.memory.lazy_embeddings: 🧠 Embeddings system initialized successfully (lazy loaded)
2025-06-07 16:16:04,567 [    INFO] advanced_memory: 🗄️ Advanced memory database initialized
2025-06-07 16:16:04,567 [ WARNING] advanced_memory: Embeddings not available for advanced memory: 'str' object has no attribute 'get'
2025-06-07 16:16:04,567 [    INFO] setup_agent.memory.manager: 🧠 Advanced memory system initialized
2025-06-07 16:17:32,705 [    INFO] setup_agent.utils.logging_utils: Logging configured: level=INFO, file=agent.log
2025-06-07 16:17:32,724 [    INFO] setup_agent.utils.logging_utils: Logging configured: level=INFO, file=agent.log
2025-06-07 16:17:32,724 [    INFO] __main__: 🚀 Initializing SetupAgent v2.0.0
2025-06-07 16:17:32,725 [    INFO] setup_agent.llm.factory: 🤖 Created ollama LLM provider
2025-06-07 16:17:34,764 [    INFO] setup_agent.llm.factory: 🤖 Created openai LLM provider
2025-06-07 16:17:34,764 [    INFO] __main__: ✅ LLM Provider: ollama with model mistral
2025-06-07 16:17:34,959 [    INFO] faiss.loader: Loading faiss with AVX2 support.
2025-06-07 16:17:34,981 [    INFO] faiss.loader: Successfully loaded faiss with AVX2 support.
2025-06-07 16:17:34,993 [    INFO] faiss: Failed to load GPU Faiss: name 'GpuIndexIVFFlat' is not defined. Will not load constructor refs for GPU indexes. This is only an error if you're trying to use GPU Faiss.
2025-06-07 16:17:34,993 [    INFO] embeddings: 💻 FAISS using CPU (no GPU acceleration available)
2025-06-07 16:17:44,611 [    INFO] embeddings: ✅ Ollama model nomic-embed-text is available
2025-06-07 16:17:44,614 [    INFO] embeddings: Initialized Ollama embeddings with nomic-embed-text
2025-06-07 16:17:44,614 [    INFO] embeddings: Loaded existing FAISS index with 50 vectors
2025-06-07 16:17:44,614 [    INFO] embeddings: Initialized FAISS vector store
2025-06-07 16:17:44,614 [    INFO] setup_agent.memory.lazy_embeddings: 🧠 Embeddings system initialized successfully (lazy loaded)
2025-06-07 16:17:44,614 [    INFO] __main__: 🧠 Memory System: 4 messages, 0 commands
2025-06-07 16:17:48,898 [    INFO] advanced_memory: 🗄️ Advanced memory database initialized
2025-06-07 16:17:48,898 [ WARNING] advanced_memory: Embeddings not available for advanced memory: 'str' object has no attribute 'get'
2025-06-07 16:17:48,899 [    INFO] setup_agent.memory.manager: 🧠 Advanced memory system initialized
2025-06-07 16:18:02,578 [    INFO] setup_agent.utils.logging_utils: Logging configured: level=INFO, file=agent.log
2025-06-07 16:18:02,595 [    INFO] setup_agent.utils.logging_utils: Logging configured: level=INFO, file=agent.log
2025-06-07 16:18:02,595 [    INFO] __main__: 🚀 Initializing SetupAgent v2.0.0
2025-06-07 16:18:02,595 [    INFO] setup_agent.llm.factory: 🤖 Created ollama LLM provider
2025-06-07 16:18:04,643 [    INFO] setup_agent.llm.factory: 🤖 Created openai LLM provider
2025-06-07 16:18:04,647 [    INFO] __main__: ✅ LLM Provider: ollama with model mistral
2025-06-07 16:18:04,810 [    INFO] faiss.loader: Loading faiss with AVX2 support.
2025-06-07 16:18:04,848 [    INFO] faiss.loader: Successfully loaded faiss with AVX2 support.
2025-06-07 16:18:04,848 [    INFO] faiss: Failed to load GPU Faiss: name 'GpuIndexIVFFlat' is not defined. Will not load constructor refs for GPU indexes. This is only an error if you're trying to use GPU Faiss.
2025-06-07 16:18:04,848 [    INFO] embeddings: 💻 FAISS using CPU (no GPU acceleration available)
2025-06-07 16:18:14,703 [    INFO] embeddings: ✅ Ollama model nomic-embed-text is available
2025-06-07 16:18:14,704 [    INFO] embeddings: Initialized Ollama embeddings with nomic-embed-text
2025-06-07 16:18:14,704 [    INFO] embeddings: Loaded existing FAISS index with 50 vectors
2025-06-07 16:18:14,704 [    INFO] embeddings: Initialized FAISS vector store
2025-06-07 16:18:14,704 [    INFO] setup_agent.memory.lazy_embeddings: 🧠 Embeddings system initialized successfully (lazy loaded)
2025-06-07 16:18:14,704 [    INFO] __main__: 🧠 Memory System: 4 messages, 0 commands
2025-07-04 21:50:17,389 [    INFO] setup_agent.utils.logging_utils: Logging configured: level=INFO, file=agent.log
2025-07-04 21:50:50,393 [    INFO] setup_agent.utils.logging_utils: Logging configured: level=INFO, file=agent.log
2025-07-04 21:50:50,419 [    INFO] setup_agent.utils.logging_utils: Logging configured: level=INFO, file=agent.log
2025-07-04 21:50:50,425 [    INFO] __main__: 🚀 Initializing SetupAgent v2.0.0
2025-07-04 21:50:50,426 [    INFO] setup_agent.llm.factory: 🤖 Created ollama LLM provider
2025-07-04 21:50:54,492 [    INFO] setup_agent.llm.factory: 🤖 Created openai LLM provider
2025-07-04 21:50:54,493 [ WARNING] __main__: ⚠️ LLM Provider initialization failed: No LLM providers are available
2025-07-04 21:50:54,493 [    INFO] __main__: 💡 You can still use other features without LLM
2025-07-04 21:50:55,028 [    INFO] faiss.loader: Loading faiss with AVX2 support.
2025-07-04 21:50:55,872 [    INFO] faiss.loader: Successfully loaded faiss with AVX2 support.
2025-07-04 21:50:55,879 [    INFO] faiss: Failed to load GPU Faiss: name 'GpuIndexIVFFlat' is not defined. Will not load constructor refs for GPU indexes. This is only an error if you're trying to use GPU Faiss.
2025-07-04 21:50:55,880 [    INFO] embeddings: 💻 FAISS using CPU (no GPU acceleration available)
2025-07-04 21:51:34,440 [ WARNING] embeddings: Could not verify Ollama connection: HTTPConnectionPool(host='localhost', port=11434): Max retries exceeded with url: /api/tags (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000022A077874D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-07-04 21:51:34,441 [    INFO] embeddings: Initialized Ollama embeddings with nomic-embed-text
2025-07-04 21:51:34,483 [    INFO] embeddings: Loaded existing FAISS index with 50 vectors
2025-07-04 21:51:34,483 [    INFO] embeddings: Initialized FAISS vector store
2025-07-04 21:51:34,483 [    INFO] setup_agent.memory.lazy_embeddings: 🧠 Embeddings system initialized successfully (lazy loaded)
2025-07-04 21:51:34,484 [    INFO] __main__: 🧠 Memory System: 4 messages, 0 commands
2025-07-04 21:51:38,557 [   ERROR] embeddings: Failed to get Ollama embedding: HTTPConnectionPool(host='localhost', port=11434): Max retries exceeded with url: /api/embeddings (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000022A07960190>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-07-04 21:51:42,631 [   ERROR] embeddings: Failed to get Ollama embedding: HTTPConnectionPool(host='localhost', port=11434): Max retries exceeded with url: /api/embeddings (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000022A079315B0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-07-04 21:51:42,656 [    INFO] advanced_memory: 🗄️ Advanced memory database initialized
2025-07-04 21:51:42,657 [ WARNING] advanced_memory: Embeddings not available for advanced memory: 'str' object has no attribute 'get'
2025-07-04 21:51:42,657 [    INFO] setup_agent.memory.manager: 🧠 Advanced memory system initialized
2025-07-04 21:53:49,125 [    INFO] setup_agent.utils.logging_utils: Logging configured: level=INFO, file=agent.log
2025-07-04 21:53:49,388 [    INFO] faiss.loader: Loading faiss with AVX2 support.
2025-07-04 21:53:49,411 [    INFO] faiss.loader: Successfully loaded faiss with AVX2 support.
2025-07-04 21:53:49,421 [    INFO] faiss: Failed to load GPU Faiss: name 'GpuIndexIVFFlat' is not defined. Will not load constructor refs for GPU indexes. This is only an error if you're trying to use GPU Faiss.
2025-07-04 21:53:49,422 [    INFO] embeddings: 💻 FAISS using CPU (no GPU acceleration available)
2025-07-04 22:01:36,891 [    INFO] setup_agent.utils.logging_utils: Logging configured: level=INFO, file=agent.log
2025-07-04 22:01:36,914 [    INFO] setup_agent.utils.logging_utils: Logging configured: level=INFO, file=agent.log
2025-07-04 22:01:36,914 [    INFO] __main__: 🚀 Initializing SetupAgent v2.0.0
2025-07-04 22:01:36,915 [    INFO] setup_agent.llm.factory: 🤖 Created ollama LLM provider
2025-07-04 22:01:41,011 [    INFO] setup_agent.llm.factory: 🤖 Created openai LLM provider
2025-07-04 22:01:41,011 [ WARNING] __main__: ⚠️ LLM Provider initialization failed: No LLM providers are available
2025-07-04 22:01:41,011 [    INFO] __main__: 💡 You can still use other features without LLM
2025-07-04 22:01:41,260 [    INFO] faiss.loader: Loading faiss with AVX2 support.
2025-07-04 22:01:41,287 [    INFO] faiss.loader: Successfully loaded faiss with AVX2 support.
2025-07-04 22:01:41,297 [    INFO] faiss: Failed to load GPU Faiss: name 'GpuIndexIVFFlat' is not defined. Will not load constructor refs for GPU indexes. This is only an error if you're trying to use GPU Faiss.
2025-07-04 22:01:41,298 [    INFO] embeddings: 💻 FAISS using CPU (no GPU acceleration available)
2025-07-04 22:01:55,576 [ WARNING] embeddings: Could not verify Ollama connection: HTTPConnectionPool(host='localhost', port=11434): Max retries exceeded with url: /api/tags (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000011111166C10>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-07-04 22:01:55,577 [    INFO] embeddings: Initialized Ollama embeddings with nomic-embed-text
2025-07-04 22:01:55,580 [    INFO] embeddings: Loaded existing FAISS index with 50 vectors
2025-07-04 22:01:55,580 [    INFO] embeddings: Initialized FAISS vector store
2025-07-04 22:01:55,581 [    INFO] setup_agent.memory.lazy_embeddings: 🧠 Embeddings system initialized successfully (lazy loaded)
2025-07-04 22:01:55,581 [    INFO] __main__: 🧠 Memory System: 4 messages, 0 commands
2025-07-04 22:01:59,643 [   ERROR] embeddings: Failed to get Ollama embedding: HTTPConnectionPool(host='localhost', port=11434): Max retries exceeded with url: /api/embeddings (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000011111167890>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-07-04 22:02:03,715 [   ERROR] embeddings: Failed to get Ollama embedding: HTTPConnectionPool(host='localhost', port=11434): Max retries exceeded with url: /api/embeddings (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000111112FD480>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-07-04 22:02:03,736 [    INFO] advanced_memory: 🗄️ Advanced memory database initialized
2025-07-04 22:02:07,806 [ WARNING] embeddings: Could not verify Ollama connection: HTTPConnectionPool(host='localhost', port=11434): Max retries exceeded with url: /api/tags (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000111112FD0F0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-07-04 22:02:07,806 [    INFO] embeddings: Initialized Ollama embeddings with nomic-embed-text
2025-07-04 22:02:07,808 [    INFO] embeddings: Loaded existing FAISS index with 50 vectors
2025-07-04 22:02:07,809 [    INFO] embeddings: Initialized FAISS vector store
2025-07-04 22:02:07,809 [    INFO] advanced_memory: 🧠 Embeddings integration enabled for advanced memory
2025-07-04 22:02:07,810 [    INFO] setup_agent.memory.manager: 🧠 Advanced memory system initialized
2025-07-04 22:02:11,881 [   ERROR] embeddings: Failed to get Ollama embedding: HTTPConnectionPool(host='localhost', port=11434): Max retries exceeded with url: /api/embeddings (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001111131CB90>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-07-04 22:02:15,944 [   ERROR] embeddings: Failed to get Ollama embedding: HTTPConnectionPool(host='localhost', port=11434): Max retries exceeded with url: /api/embeddings (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000011111324050>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-07-04 22:17:13,199 [    INFO] setup_agent.utils.logging_utils: Logging configured: level=INFO, file=agent.log
2025-07-04 22:17:13,223 [    INFO] setup_agent.utils.logging_utils: Logging configured: level=INFO, file=agent.log
2025-07-04 22:17:13,223 [    INFO] __main__: 🚀 Initializing SetupAgent v2.0.0
2025-07-04 22:17:13,223 [    INFO] setup_agent.llm.factory: 🤖 Created ollama LLM provider
2025-07-04 22:17:17,293 [    INFO] setup_agent.llm.factory: 🤖 Created openai LLM provider
2025-07-04 22:17:17,293 [ WARNING] __main__: ⚠️ LLM Provider initialization failed: No LLM providers are available
2025-07-04 22:17:17,294 [    INFO] __main__: 💡 You can still use other features without LLM
2025-07-04 22:17:17,542 [    INFO] faiss.loader: Loading faiss with AVX2 support.
2025-07-04 22:17:17,568 [    INFO] faiss.loader: Successfully loaded faiss with AVX2 support.
2025-07-04 22:17:17,576 [    INFO] faiss: Failed to load GPU Faiss: name 'GpuIndexIVFFlat' is not defined. Will not load constructor refs for GPU indexes. This is only an error if you're trying to use GPU Faiss.
2025-07-04 22:17:17,576 [    INFO] embeddings: 💻 FAISS using CPU (no GPU acceleration available)
2025-07-04 22:17:31,633 [ WARNING] embeddings: Could not verify Ollama connection: HTTPConnectionPool(host='localhost', port=11434): Max retries exceeded with url: /api/tags (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000207EC896C10>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-07-04 22:17:31,634 [    INFO] embeddings: Initialized Ollama embeddings with nomic-embed-text
2025-07-04 22:17:31,636 [    INFO] embeddings: Loaded existing FAISS index with 50 vectors
2025-07-04 22:17:31,636 [    INFO] embeddings: Initialized FAISS vector store
2025-07-04 22:17:31,637 [    INFO] setup_agent.memory.lazy_embeddings: 🧠 Embeddings system initialized successfully (lazy loaded)
2025-07-04 22:17:31,637 [    INFO] __main__: 🧠 Memory System: 4 messages, 0 commands
2025-07-04 22:17:35,722 [   ERROR] embeddings: Failed to get Ollama embedding: HTTPConnectionPool(host='localhost', port=11434): Max retries exceeded with url: /api/embeddings (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000207EC897890>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-07-04 22:17:39,803 [   ERROR] embeddings: Failed to get Ollama embedding: HTTPConnectionPool(host='localhost', port=11434): Max retries exceeded with url: /api/embeddings (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000207ECA2D480>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-07-04 22:17:39,811 [    INFO] advanced_memory: 🗄️ Advanced memory database initialized
2025-07-04 22:17:43,894 [ WARNING] embeddings: Could not verify Ollama connection: HTTPConnectionPool(host='localhost', port=11434): Max retries exceeded with url: /api/tags (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000207EC51A190>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-07-04 22:17:43,895 [    INFO] embeddings: Initialized Ollama embeddings with nomic-embed-text
2025-07-04 22:17:43,897 [    INFO] embeddings: Loaded existing FAISS index with 50 vectors
2025-07-04 22:17:43,897 [    INFO] embeddings: Initialized FAISS vector store
2025-07-04 22:17:43,897 [    INFO] advanced_memory: 🧠 Embeddings integration enabled for advanced memory
2025-07-04 22:17:43,898 [    INFO] setup_agent.memory.manager: 🧠 Advanced memory system initialized
2025-07-04 22:17:47,949 [   ERROR] embeddings: Failed to get Ollama embedding: HTTPConnectionPool(host='localhost', port=11434): Max retries exceeded with url: /api/embeddings (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000207ECA4C290>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-07-04 22:17:52,029 [   ERROR] embeddings: Failed to get Ollama embedding: HTTPConnectionPool(host='localhost', port=11434): Max retries exceeded with url: /api/embeddings (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000207ECA54270>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-07-08 14:07:59,902 [    INFO] setup_agent.utils.logging_utils: Logging configured: level=INFO, file=agent.log
2025-07-08 14:07:59,954 [    INFO] setup_agent.utils.logging_utils: Logging configured: level=INFO, file=agent.log
2025-07-08 14:07:59,954 [    INFO] __main__: 🚀 Initializing SetupAgent v2.0.0
2025-07-08 14:07:59,954 [    INFO] setup_agent.llm.factory: 🤖 Created ollama LLM provider
2025-07-08 14:08:02,196 [    INFO] setup_agent.llm.factory: 🤖 Created openai LLM provider
2025-07-08 14:08:02,196 [    INFO] __main__: ✅ LLM Provider: ollama with model qwen2.5:7b-instruct
2025-07-08 14:08:02,626 [ WARNING] embeddings: FAISS not available - FAISS vector search will be disabled
2025-07-08 14:08:02,627 [ WARNING] embeddings: SentenceTransformers not available - local embedding models will be disabled
2025-07-08 14:08:02,630 [ WARNING] embeddings: ChromaDB not available - ChromaDB vector search will be disabled
2025-07-08 14:08:04,658 [    INFO] embeddings: ✅ Ollama model nomic-embed-text is available
2025-07-08 14:08:04,659 [    INFO] embeddings: Initialized Ollama embeddings with nomic-embed-text
2025-07-08 14:08:04,659 [ WARNING] embeddings: Unknown vector backend: faiss
2025-07-08 14:08:04,659 [    INFO] setup_agent.memory.lazy_embeddings: 🧠 Embeddings system initialized successfully (lazy loaded)
2025-07-08 14:08:04,660 [    INFO] __main__: 🧠 Memory System: 4 messages, 0 commands
2025-07-08 14:08:04,679 [    INFO] advanced_memory: 🗄️ Advanced memory database initialized
2025-07-08 14:08:06,726 [    INFO] embeddings: ✅ Ollama model nomic-embed-text is available
2025-07-08 14:08:06,727 [    INFO] embeddings: Initialized Ollama embeddings with nomic-embed-text
2025-07-08 14:08:06,727 [ WARNING] embeddings: Unknown vector backend: faiss
2025-07-08 14:08:06,728 [    INFO] advanced_memory: 🧠 Embeddings integration enabled for advanced memory
2025-07-08 14:08:06,728 [    INFO] setup_agent.memory.manager: 🧠 Advanced memory system initialized
2025-07-08 14:12:55,872 [    INFO] setup_agent.utils.logging_utils: Logging configured: level=INFO, file=agent.log
2025-07-08 14:12:55,904 [    INFO] setup_agent.utils.logging_utils: Logging configured: level=INFO, file=agent.log
2025-07-08 14:12:55,905 [    INFO] __main__: 🚀 Initializing SetupAgent v2.0.0
2025-07-08 14:12:55,905 [    INFO] setup_agent.llm.factory: 🤖 Created ollama LLM provider
2025-07-08 14:12:57,960 [    INFO] setup_agent.llm.factory: 🤖 Created openai LLM provider
2025-07-08 14:12:57,961 [    INFO] __main__: ✅ LLM Provider: ollama with model qwen2.5:7b-instruct
2025-07-08 14:12:58,118 [    INFO] faiss.loader: Loading faiss with AVX2 support.
2025-07-08 14:12:58,153 [    INFO] faiss.loader: Successfully loaded faiss with AVX2 support.
2025-07-08 14:12:58,153 [    INFO] faiss: Failed to load GPU Faiss: name 'GpuIndexIVFFlat' is not defined. Will not load constructor refs for GPU indexes. This is only an error if you're trying to use GPU Faiss.
2025-07-08 14:12:58,153 [    INFO] embeddings: 💻 FAISS using CPU (no GPU acceleration available)
2025-07-08 14:13:12,958 [    INFO] embeddings: ✅ Ollama model nomic-embed-text is available
2025-07-08 14:13:12,959 [    INFO] embeddings: Initialized Ollama embeddings with nomic-embed-text
2025-07-08 14:13:12,960 [    INFO] embeddings: Loaded existing FAISS index with 50 vectors
2025-07-08 14:13:12,960 [    INFO] embeddings: Initialized FAISS vector store
2025-07-08 14:13:12,960 [    INFO] setup_agent.memory.lazy_embeddings: 🧠 Embeddings system initialized successfully (lazy loaded)
2025-07-08 14:13:12,960 [    INFO] __main__: 🧠 Memory System: 4 messages, 0 commands
2025-07-08 14:13:34,944 [    INFO] advanced_memory: 🗄️ Advanced memory database initialized
2025-07-08 14:13:37,024 [    INFO] embeddings: ✅ Ollama model nomic-embed-text is available
2025-07-08 14:13:37,026 [    INFO] embeddings: Initialized Ollama embeddings with nomic-embed-text
2025-07-08 14:13:37,027 [    INFO] embeddings: Loaded existing FAISS index with 50 vectors
2025-07-08 14:13:37,027 [    INFO] embeddings: Initialized FAISS vector store
2025-07-08 14:13:37,028 [    INFO] advanced_memory: 🧠 Embeddings integration enabled for advanced memory
2025-07-08 14:13:37,028 [    INFO] setup_agent.memory.manager: 🧠 Advanced memory system initialized
2025-07-08 14:15:09,918 [    INFO] setup_agent.utils.logging_utils: Logging configured: level=INFO, file=agent.log
2025-07-08 14:15:09,952 [    INFO] setup_agent.utils.logging_utils: Logging configured: level=INFO, file=agent.log
2025-07-08 14:15:09,952 [    INFO] __main__: 🚀 Initializing SetupAgent v2.0.0
2025-07-08 14:15:09,952 [    INFO] setup_agent.llm.factory: 🤖 Created ollama LLM provider
2025-07-08 14:15:12,032 [    INFO] setup_agent.llm.factory: 🤖 Created openai LLM provider
2025-07-08 14:15:12,033 [    INFO] __main__: ✅ LLM Provider: ollama with model qwen2.5:7b-instruct
2025-07-08 14:15:12,183 [    INFO] faiss.loader: Loading faiss with AVX2 support.
2025-07-08 14:15:12,208 [    INFO] faiss.loader: Successfully loaded faiss with AVX2 support.
2025-07-08 14:15:12,216 [    INFO] faiss: Failed to load GPU Faiss: name 'GpuIndexIVFFlat' is not defined. Will not load constructor refs for GPU indexes. This is only an error if you're trying to use GPU Faiss.
2025-07-08 14:15:12,217 [    INFO] embeddings: 💻 FAISS using CPU (no GPU acceleration available)
2025-07-08 14:15:23,096 [    INFO] embeddings: ✅ Ollama model nomic-embed-text is available
2025-07-08 14:15:23,097 [    INFO] embeddings: Initialized Ollama embeddings with nomic-embed-text
2025-07-08 14:15:23,098 [    INFO] embeddings: Loaded existing FAISS index with 50 vectors
2025-07-08 14:15:23,099 [    INFO] embeddings: Initialized FAISS vector store
2025-07-08 14:15:23,099 [    INFO] setup_agent.memory.lazy_embeddings: 🧠 Embeddings system initialized successfully (lazy loaded)
2025-07-08 14:15:23,100 [    INFO] __main__: 🧠 Memory System: 4 messages, 0 commands
