"""
Ollama LLM provider implementation.
"""

import json
import logging
from typing import Dict, Any, Optional, Iterator, List
from urllib.parse import urljoin

from .base import BaseLLMProvider, LLMResponse, LLMStreamResponse
from ..core.exceptions import LLMProviderError

# Optional dependencies
try:
    import requests
    HAS_REQUESTS = True
except ImportError:
    import urllib.request
    import urllib.parse
    HAS_REQUESTS = False

logger = logging.getLogger(__name__)


class OllamaProvider(BaseLLMProvider):
    """Ollama LLM provider implementation."""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.base_url = config.get('url', 'http://localhost:11434')
        self.timeout = config.get('timeout', 90)
        self.default_options = config.get('options', {})
        
    def is_available(self) -> bool:
        """Check if Ollama is available."""
        try:
            url = urljoin(self.base_url, '/api/tags')
            
            if HAS_REQUESTS:
                response = requests.get(url, timeout=5)
                return response.status_code == 200
            else:
                req = urllib.request.Request(url)
                with urllib.request.urlopen(req, timeout=5) as response:
                    return response.status == 200
        except Exception as e:
            logger.debug(f"Ollama not available: {e}")
            return False
    
    def list_models(self) -> List[str]:
        """List available Ollama models."""
        try:
            url = urljoin(self.base_url, '/api/tags')
            
            if HAS_REQUESTS:
                response = requests.get(url, timeout=10)
                response.raise_for_status()
                data = response.json()
            else:
                req = urllib.request.Request(url)
                with urllib.request.urlopen(req, timeout=10) as response:
                    data = json.loads(response.read().decode())
            
            models = []
            for model in data.get('models', []):
                models.append(model.get('name', ''))
            
            return [m for m in models if m]
            
        except Exception as e:
            logger.error(f"Failed to list Ollama models: {e}")
            return []
    
    def generate(
        self, 
        prompt: str, 
        model: Optional[str] = None,
        **kwargs
    ) -> LLMResponse:
        """Generate a response from Ollama."""
        if not self.is_available():
            raise LLMProviderError("Ollama is not available")
        
        model = model or self.get_default_model()
        
        # Prepare request
        url = urljoin(self.base_url, '/api/generate')
        
        # Merge options
        options = {**self.default_options, **kwargs.get('options', {})}
        
        payload = {
            'model': model,
            'prompt': prompt,
            'stream': False,
            'options': options
        }
        
        try:
            if HAS_REQUESTS:
                response = requests.post(
                    url, 
                    json=payload, 
                    timeout=self.timeout
                )
                response.raise_for_status()
                data = response.json()
            else:
                req_data = json.dumps(payload).encode('utf-8')
                req = urllib.request.Request(
                    url, 
                    data=req_data,
                    headers={'Content-Type': 'application/json'}
                )
                with urllib.request.urlopen(req, timeout=self.timeout) as response:
                    data = json.loads(response.read().decode())
            
            return LLMResponse(
                content=data.get('response', ''),
                model=model,
                provider='ollama',
                metadata={
                    'total_duration': data.get('total_duration'),
                    'load_duration': data.get('load_duration'),
                    'prompt_eval_count': data.get('prompt_eval_count'),
                    'eval_count': data.get('eval_count'),
                    'eval_duration': data.get('eval_duration')
                }
            )
            
        except Exception as e:
            logger.error(f"Ollama generation failed: {e}")
            return LLMResponse(
                content='',
                model=model,
                provider='ollama',
                metadata={},
                error=str(e)
            )
    
    def generate_stream(
        self, 
        prompt: str, 
        model: Optional[str] = None,
        **kwargs
    ) -> Iterator[LLMStreamResponse]:
        """Generate a streaming response from Ollama."""
        if not self.is_available():
            raise LLMProviderError("Ollama is not available")
        
        model = model or self.get_default_model()
        
        # Prepare request
        url = urljoin(self.base_url, '/api/generate')
        
        # Merge options
        options = {**self.default_options, **kwargs.get('options', {})}
        
        payload = {
            'model': model,
            'prompt': prompt,
            'stream': True,
            'options': options
        }
        
        try:
            if HAS_REQUESTS:
                response = requests.post(
                    url, 
                    json=payload, 
                    timeout=self.timeout,
                    stream=True
                )
                response.raise_for_status()
                
                for line in response.iter_lines():
                    if line:
                        try:
                            data = json.loads(line.decode('utf-8'))
                            yield LLMStreamResponse(
                                content=data.get('response', ''),
                                is_complete=data.get('done', False),
                                metadata={
                                    'total_duration': data.get('total_duration'),
                                    'eval_count': data.get('eval_count')
                                }
                            )
                        except json.JSONDecodeError:
                            continue
            else:
                # Fallback for urllib (no streaming support)
                response = self.generate(prompt, model, **kwargs)
                yield LLMStreamResponse(
                    content=response.content,
                    is_complete=True,
                    metadata=response.metadata,
                    error=response.error
                )
                
        except Exception as e:
            logger.error(f"Ollama streaming failed: {e}")
            yield LLMStreamResponse(
                content='',
                is_complete=True,
                metadata={},
                error=str(e)
            )
