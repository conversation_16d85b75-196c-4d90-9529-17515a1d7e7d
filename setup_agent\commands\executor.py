"""
Safe command execution with validation and monitoring.
"""

import subprocess
import logging
import time
import threading
from typing import Dict, Any, List, Optional, Tuple, Union
from dataclasses import dataclass
from pathlib import Path

from ..core.config import config
from ..core.exceptions import CommandExecutionError
from .safety import SafetyValidator, CommandSafetyLevel
from .whitelist import CommandWhitelist

logger = logging.getLogger(__name__)


@dataclass
class ExecutionResult:
    """Result of command execution."""
    command: str
    success: bool
    return_code: int
    stdout: str
    stderr: str
    execution_time: float
    safety_level: CommandSafetyLevel
    metadata: Dict[str, Any]


class CommandExecutor:
    """Safe command executor with validation and monitoring."""
    
    def __init__(self):
        self.safety_validator = SafetyValidator()
        self.whitelist = CommandWhitelist()
        
        # Configuration
        self.enabled = config.get(['commands', 'execution_enabled'], True)
        self.timeout = config.get(['commands', 'default_timeout'], 30)
        self.max_output_size = config.get(['commands', 'max_output_size'], 1024 * 1024)  # 1MB
        self.working_directory = config.get(['commands', 'working_directory'], '.')
        
        # Security settings
        self.require_confirmation = config.get(['commands', 'require_confirmation'], True)
        self.log_all_commands = config.get(['commands', 'log_all_commands'], True)
        
        # Execution statistics
        self.stats = {
            'total_executed': 0,
            'successful': 0,
            'failed': 0,
            'blocked': 0,
            'total_time': 0.0
        }
    
    def execute(
        self, 
        command: str, 
        timeout: Optional[int] = None,
        working_dir: Optional[str] = None,
        confirm: bool = False
    ) -> ExecutionResult:
        """Execute a command with safety validation."""
        if not self.enabled:
            raise CommandExecutionError("Command execution is disabled")
        
        command = command.strip()
        if not command:
            raise CommandExecutionError("Empty command")
        
        # Validate command safety
        safety_result = self.safety_validator.validate_command(command)
        
        if not safety_result.allowed:
            self.stats['blocked'] += 1
            raise CommandExecutionError(f"Command blocked: {safety_result.reason}")
        
        # Check whitelist
        if not self.whitelist.is_allowed(command):
            self.stats['blocked'] += 1
            raise CommandExecutionError("Command not in whitelist")
        
        # Require confirmation for dangerous commands
        if (safety_result.level == CommandSafetyLevel.DANGEROUS and 
            self.require_confirmation and not confirm):
            raise CommandExecutionError(
                f"Dangerous command requires confirmation: {safety_result.reason}"
            )
        
        # Log command execution
        if self.log_all_commands:
            logger.info(f"Executing command: {command}")
        
        # Execute command
        start_time = time.time()
        try:
            result = self._execute_subprocess(
                command, 
                timeout or self.timeout,
                working_dir or self.working_directory
            )
            
            execution_time = time.time() - start_time
            
            # Update statistics
            self.stats['total_executed'] += 1
            self.stats['total_time'] += execution_time
            
            if result.success:
                self.stats['successful'] += 1
            else:
                self.stats['failed'] += 1
            
            # Create execution result
            execution_result = ExecutionResult(
                command=command,
                success=result.success,
                return_code=result.return_code,
                stdout=result.stdout,
                stderr=result.stderr,
                execution_time=execution_time,
                safety_level=safety_result.level,
                metadata={
                    'working_directory': working_dir or self.working_directory,
                    'timeout_used': timeout or self.timeout,
                    'safety_reason': safety_result.reason
                }
            )
            
            logger.info(f"Command completed in {execution_time:.2f}s: {command}")
            return execution_result
            
        except Exception as e:
            self.stats['failed'] += 1
            logger.error(f"Command execution failed: {e}")
            raise CommandExecutionError(f"Execution failed: {e}")
    
    def _execute_subprocess(
        self, 
        command: str, 
        timeout: int, 
        working_dir: str
    ) -> ExecutionResult:
        """Execute command using subprocess."""
        try:
            # Prepare working directory
            work_path = Path(working_dir)
            if not work_path.exists():
                work_path.mkdir(parents=True, exist_ok=True)
            
            # Execute command
            process = subprocess.Popen(
                command,
                shell=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                cwd=str(work_path),
                text=True,
                bufsize=1,
                universal_newlines=True
            )
            
            # Wait for completion with timeout
            try:
                stdout, stderr = process.communicate(timeout=timeout)
            except subprocess.TimeoutExpired:
                process.kill()
                stdout, stderr = process.communicate()
                stderr = f"Command timed out after {timeout} seconds\n{stderr}"
            
            # Limit output size
            if len(stdout) > self.max_output_size:
                stdout = stdout[:self.max_output_size] + "\n... (output truncated)"
            
            if len(stderr) > self.max_output_size:
                stderr = stderr[:self.max_output_size] + "\n... (output truncated)"
            
            return ExecutionResult(
                command=command,
                success=process.returncode == 0,
                return_code=process.returncode,
                stdout=stdout,
                stderr=stderr,
                execution_time=0.0,  # Will be set by caller
                safety_level=CommandSafetyLevel.SAFE,  # Will be set by caller
                metadata={}
            )
            
        except Exception as e:
            logger.error(f"Subprocess execution failed: {e}")
            raise CommandExecutionError(f"Subprocess failed: {e}")
    
    def execute_async(
        self, 
        command: str, 
        callback: Optional[callable] = None,
        timeout: Optional[int] = None,
        working_dir: Optional[str] = None
    ) -> threading.Thread:
        """Execute command asynchronously."""
        def run_async():
            try:
                result = self.execute(command, timeout, working_dir)
                if callback:
                    callback(result)
            except Exception as e:
                if callback:
                    callback(ExecutionResult(
                        command=command,
                        success=False,
                        return_code=-1,
                        stdout="",
                        stderr=str(e),
                        execution_time=0.0,
                        safety_level=CommandSafetyLevel.BLOCKED,
                        metadata={'error': str(e)}
                    ))
        
        thread = threading.Thread(target=run_async, daemon=True)
        thread.start()
        return thread
    
    def validate_only(self, command: str) -> Dict[str, Any]:
        """Validate command without executing."""
        safety_result = self.safety_validator.validate_command(command)
        whitelist_allowed = self.whitelist.is_allowed(command)
        
        return {
            'command': command,
            'safety_level': safety_result.level.value,
            'safety_allowed': safety_result.allowed,
            'safety_reason': safety_result.reason,
            'whitelist_allowed': whitelist_allowed,
            'overall_allowed': safety_result.allowed and whitelist_allowed,
            'requires_confirmation': safety_result.metadata.get('requires_confirmation', False),
            'suggestions': safety_result.suggestions
        }
    
    def get_stats(self) -> Dict[str, Any]:
        """Get execution statistics."""
        stats = self.stats.copy()
        if stats['total_executed'] > 0:
            stats['success_rate'] = stats['successful'] / stats['total_executed']
            stats['average_time'] = stats['total_time'] / stats['total_executed']
        else:
            stats['success_rate'] = 0.0
            stats['average_time'] = 0.0
        
        return stats
    
    def clear_stats(self) -> None:
        """Clear execution statistics."""
        self.stats = {
            'total_executed': 0,
            'successful': 0,
            'failed': 0,
            'blocked': 0,
            'total_time': 0.0
        }
        logger.info("Command execution statistics cleared")


# Global command executor instance
command_executor = CommandExecutor()
