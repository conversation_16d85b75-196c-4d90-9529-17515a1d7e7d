"""
Command whitelist management for security.
"""

import re
import logging
from typing import List, Set, Dict, Any, Optional
from pathlib import Path

from ..core.config import config

logger = logging.getLogger(__name__)


class CommandWhitelist:
    """Manages allowed commands and patterns."""
    
    def __init__(self):
        self.enabled = config.get(['security', 'whitelist_enabled'], True)
        self.strict_mode = config.get(['security', 'whitelist_strict'], False)
        
        # Load whitelist configuration
        self._load_whitelist()
        
    def _load_whitelist(self) -> None:
        """Load whitelist from configuration."""
        # Default safe commands
        default_commands = [
            # File operations (safe)
            'ls', 'dir', 'pwd', 'cd', 'cat', 'head', 'tail', 'less', 'more',
            'find', 'locate', 'which', 'where', 'file', 'stat', 'wc', 'sort',
            'uniq', 'grep', 'awk', 'sed', 'cut', 'tr', 'tee',
            
            # System info (safe)
            'whoami', 'id', 'groups', 'date', 'uptime', 'uname', 'hostname',
            'ps', 'top', 'htop', 'df', 'du', 'free', 'lscpu', 'lsblk',
            'mount', 'lsof', 'netstat', 'ss', 'ifconfig', 'ip',
            
            # Development tools
            'git', 'python', 'python3', 'pip', 'pip3', 'node', 'npm', 'yarn',
            'make', 'cmake', 'gcc', 'g++', 'javac', 'java', 'rustc', 'cargo',
            'go', 'dotnet', 'mvn', 'gradle',
            
            # Text editors (safe)
            'nano', 'vim', 'emacs', 'code', 'notepad',
            
            # Archive operations
            'tar', 'zip', 'unzip', 'gzip', 'gunzip', '7z',
            
            # Network (if enabled)
            'ping', 'traceroute', 'nslookup', 'dig', 'host',
            
            # Package managers (read-only operations)
            'apt list', 'apt search', 'apt show', 'yum list', 'yum search',
            'brew list', 'brew search', 'brew info', 'choco list',
        ]
        
        # Load from configuration
        configured_commands = config.get(['security', 'allowed_commands'], [])
        
        # Combine default and configured
        self.allowed_commands: Set[str] = set(default_commands + configured_commands)
        
        # Command patterns (regex)
        default_patterns = [
            r'^ls\s+.*',           # ls with any arguments
            r'^dir\s+.*',          # dir with any arguments
            r'^cat\s+[^/|>]+$',    # cat single file (no pipes/redirects)
            r'^head\s+[^/|>]+$',   # head single file
            r'^tail\s+[^/|>]+$',   # tail single file
            r'^grep\s+.*',         # grep operations
            r'^find\s+\.\s+.*',    # find in current directory
            r'^git\s+(status|log|diff|show|branch).*', # safe git commands
            r'^python\s+[^-].*\.py$', # run python scripts
            r'^node\s+[^-].*\.js$',   # run node scripts
        ]
        
        configured_patterns = config.get(['security', 'allowed_patterns'], [])
        
        # Compile patterns
        all_patterns = default_patterns + configured_patterns
        self.allowed_patterns = [re.compile(p, re.IGNORECASE) for p in all_patterns]
        
        # Blocked commands (always deny)
        self.blocked_commands: Set[str] = set(config.get(['security', 'blocked_commands'], [
            'rm', 'del', 'rmdir', 'format', 'fdisk', 'mkfs', 'dd',
            'shutdown', 'reboot', 'halt', 'poweroff', 'init',
            'kill', 'killall', 'pkill', 'taskkill',
            'chmod 777', 'chown', 'passwd', 'su', 'sudo',
            'crontab', 'at', 'batch', 'systemctl', 'service'
        ]))
        
        logger.info(f"Whitelist loaded: {len(self.allowed_commands)} commands, "
                   f"{len(self.allowed_patterns)} patterns, "
                   f"{len(self.blocked_commands)} blocked")
    
    def is_allowed(self, command: str) -> bool:
        """Check if command is allowed by whitelist."""
        if not self.enabled:
            return True
        
        command = command.strip()
        if not command:
            return False
        
        # Check blocked commands first
        if self._is_blocked(command):
            logger.warning(f"Command blocked by blacklist: {command}")
            return False
        
        # In strict mode, must be explicitly allowed
        if self.strict_mode:
            return self._is_explicitly_allowed(command)
        
        # In non-strict mode, check if it's safe
        return self._is_safe_command(command)
    
    def _is_blocked(self, command: str) -> bool:
        """Check if command is explicitly blocked."""
        command_lower = command.lower()
        
        # Check exact matches
        for blocked in self.blocked_commands:
            if command_lower.startswith(blocked.lower()):
                return True
        
        return False
    
    def _is_explicitly_allowed(self, command: str) -> bool:
        """Check if command is explicitly allowed."""
        command_lower = command.lower()
        
        # Check exact command matches
        first_word = command_lower.split()[0] if command_lower.split() else ''
        if first_word in {cmd.lower() for cmd in self.allowed_commands}:
            return True
        
        # Check pattern matches
        for pattern in self.allowed_patterns:
            if pattern.match(command):
                return True
        
        return False
    
    def _is_safe_command(self, command: str) -> bool:
        """Check if command appears safe (non-strict mode)."""
        # In non-strict mode, allow if not blocked and appears safe
        if self._is_blocked(command):
            return False
        
        # Check if it's in allowed list or matches patterns
        if self._is_explicitly_allowed(command):
            return True
        
        # Additional safety checks for non-strict mode
        dangerous_indicators = [
            '>', '>>', '|', '&', ';', '$(', '`',  # Shell operators
            'rm ', 'del ', 'format', 'shutdown', 'reboot',  # Dangerous commands
            'sudo', 'su ', 'chmod 777', 'chown',  # Privilege operations
        ]
        
        command_lower = command.lower()
        for indicator in dangerous_indicators:
            if indicator in command_lower:
                return False
        
        # If no dangerous indicators found, allow
        return True
    
    def add_command(self, command: str) -> bool:
        """Add command to whitelist."""
        if command and command not in self.blocked_commands:
            self.allowed_commands.add(command)
            logger.info(f"Added command to whitelist: {command}")
            return True
        return False
    
    def remove_command(self, command: str) -> bool:
        """Remove command from whitelist."""
        if command in self.allowed_commands:
            self.allowed_commands.remove(command)
            logger.info(f"Removed command from whitelist: {command}")
            return True
        return False
    
    def add_pattern(self, pattern: str) -> bool:
        """Add regex pattern to whitelist."""
        try:
            compiled_pattern = re.compile(pattern, re.IGNORECASE)
            self.allowed_patterns.append(compiled_pattern)
            logger.info(f"Added pattern to whitelist: {pattern}")
            return True
        except re.error as e:
            logger.error(f"Invalid regex pattern: {pattern} - {e}")
            return False
    
    def get_allowed_commands(self) -> List[str]:
        """Get list of allowed commands."""
        return sorted(list(self.allowed_commands))
    
    def get_blocked_commands(self) -> List[str]:
        """Get list of blocked commands."""
        return sorted(list(self.blocked_commands))
    
    def get_patterns(self) -> List[str]:
        """Get list of allowed patterns."""
        return [p.pattern for p in self.allowed_patterns]
    
    def get_stats(self) -> Dict[str, Any]:
        """Get whitelist statistics."""
        return {
            'enabled': self.enabled,
            'strict_mode': self.strict_mode,
            'allowed_commands_count': len(self.allowed_commands),
            'blocked_commands_count': len(self.blocked_commands),
            'allowed_patterns_count': len(self.allowed_patterns)
        }
    
    def export_config(self) -> Dict[str, Any]:
        """Export whitelist configuration."""
        return {
            'allowed_commands': list(self.allowed_commands),
            'blocked_commands': list(self.blocked_commands),
            'allowed_patterns': [p.pattern for p in self.allowed_patterns],
            'enabled': self.enabled,
            'strict_mode': self.strict_mode
        }


# Global whitelist instance
command_whitelist = CommandWhitelist()
