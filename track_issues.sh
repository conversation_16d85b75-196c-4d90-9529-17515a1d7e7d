#!/bin/bash
# SetupAgent Issue Tracker - Unix Shell Script
# Usage: ./track_issues.sh [command]

case "${1:-dashboard}" in
    "help")
        echo "🔧 SetupAgent Issue Tracker Commands:"
        echo
        echo "  ./track_issues.sh                 - Show dashboard"
        echo "  ./track_issues.sh dashboard       - Show dashboard"
        echo "  ./track_issues.sh report          - Generate progress report"
        echo "  ./track_issues.sh verify-all      - Run all verification checks"
        echo "  ./track_issues.sh list            - List all issues"
        echo "  ./track_issues.sh pending         - List pending issues"
        echo "  ./track_issues.sh resolved        - List resolved issues"
        echo "  ./track_issues.sh help            - Show this help"
        echo
        echo "Advanced usage:"
        echo "  python issue_tracker.py --verify ISSUE-001"
        echo "  python issue_tracker.py --update ISSUE-001 resolved \"Fixed the bug\""
        echo
        ;;
    "dashboard")
        echo "🔧 SetupAgent Issue Dashboard"
        echo "============================="
        python issue_tracker.py --dashboard
        ;;
    "report")
        echo "📊 Generating Progress Report..."
        python issue_tracker.py --report > issue_progress_report.txt
        echo "✅ Report saved to issue_progress_report.txt"
        cat issue_progress_report.txt
        ;;
    "verify-all")
        echo "🧪 Running All Verification Checks..."
        python issue_tracker.py --verify-all
        ;;
    "list")
        echo "📋 All Issues:"
        python issue_tracker.py --list all
        ;;
    "pending")
        echo "⏳ Pending Issues:"
        python issue_tracker.py --list pending
        ;;
    "resolved")
        echo "✅ Resolved Issues:"
        python issue_tracker.py --list resolved
        ;;
    *)
        echo "🔧 SetupAgent Issue Dashboard"
        echo "============================="
        python issue_tracker.py --dashboard
        ;;
esac