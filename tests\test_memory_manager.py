"""
Tests for memory management.
"""

import json
import tempfile
import pytest
from pathlib import Path
from unittest.mock import patch, Mock

from setup_agent.memory.manager import MemoryManager


class TestMemoryManager:
    """Test memory management functionality."""
    
    def setup_method(self):
        """Setup for each test."""
        # Use temporary directory for test files
        self.temp_dir = tempfile.mkdtemp()
        self.temp_path = Path(self.temp_dir)
        
        # Create memory manager with test paths
        self.memory_manager = MemoryManager()
        self.memory_manager.chat_history_file = self.temp_path / 'test_chat.json'
        self.memory_manager.command_history_file = self.temp_path / 'test_commands.json'
        self.memory_manager.important_file = self.temp_path / 'test_important.json'
        
        # Reset loading flags
        self.memory_manager._chat_loaded = False
        self.memory_manager._command_loaded = False
        self.memory_manager._important_loaded = False
        
        # Clear data
        self.memory_manager.chat_history = []
        self.memory_manager.command_history = []
        self.memory_manager.important_conversations = []
    
    def test_add_chat_message(self):
        """Test adding chat messages."""
        self.memory_manager.add_chat_message("Hello", "Hi there!")
        
        assert len(self.memory_manager.chat_history) == 1
        message = self.memory_manager.chat_history[0]
        assert message['user_input'] == "Hello"
        assert message['response'] == "Hi there!"
        assert 'timestamp' in message
    
    def test_add_command_execution(self):
        """Test adding command executions."""
        self.memory_manager.add_command_execution("ls -la", "file1.txt\nfile2.txt", True)
        
        assert len(self.memory_manager.command_history) == 1
        execution = self.memory_manager.command_history[0]
        assert execution['command'] == "ls -la"
        assert execution['result'] == "file1.txt\nfile2.txt"
        assert execution['success'] is True
        assert 'timestamp' in execution
    
    def test_chat_history_trimming(self):
        """Test that chat history is trimmed when it exceeds max size."""
        # Set small max size for testing
        self.memory_manager.max_chat_history = 3
        
        # Add more messages than the limit
        for i in range(5):
            self.memory_manager.add_chat_message(f"Message {i}", f"Response {i}")
        
        # Should only keep the last 3
        assert len(self.memory_manager.chat_history) == 3
        assert self.memory_manager.chat_history[0]['user_input'] == "Message 2"
        assert self.memory_manager.chat_history[-1]['user_input'] == "Message 4"
    
    def test_command_history_trimming(self):
        """Test that command history is trimmed when it exceeds max size."""
        # Set small max size for testing
        self.memory_manager.max_command_history = 2
        
        # Add more commands than the limit
        for i in range(4):
            self.memory_manager.add_command_execution(f"command{i}", f"result{i}", True)
        
        # Should only keep the last 2
        assert len(self.memory_manager.command_history) == 2
        assert self.memory_manager.command_history[0]['command'] == "command2"
        assert self.memory_manager.command_history[-1]['command'] == "command3"
    
    def test_mark_conversation_important(self):
        """Test marking conversations as important."""
        # Add a chat message first
        self.memory_manager.add_chat_message("Important question", "Important answer")
        
        # Mark it as important
        result = self.memory_manager.mark_conversation_important()
        
        assert result is True
        assert len(self.memory_manager.important_conversations) == 1
        important = self.memory_manager.important_conversations[0]
        assert important['user_input'] == "Important question"
        assert important['response'] == "Important answer"
        assert 'marked_important' in important
    
    def test_get_recent_context(self):
        """Test getting recent conversation context."""
        # Add some chat messages
        self.memory_manager.add_chat_message("First question", "First answer")
        self.memory_manager.add_chat_message("Second question", "Second answer")
        
        context = self.memory_manager.get_recent_context(max_messages=2)
        
        assert "User: First question" in context
        assert "Assistant: First answer" in context
        assert "User: Second question" in context
        assert "Assistant: Second answer" in context
    
    def test_search_history_fallback(self):
        """Test history search with text fallback."""
        # Add some chat messages
        self.memory_manager.add_chat_message("How to install Python?", "Use pip install")
        self.memory_manager.add_chat_message("What is Docker?", "Container platform")
        
        # Mock embeddings to return empty (test fallback)
        with patch('setup_agent.memory.manager.lazy_embeddings') as mock_embeddings:
            mock_embeddings.search_similar.return_value = []
            
            results = self.memory_manager.search_history("Python", max_results=5)
            
            assert len(results) == 1
            assert results[0]['user_input'] == "How to install Python?"
    
    def test_save_and_load_chat_history(self):
        """Test saving and loading chat history."""
        # Add some data
        self.memory_manager.add_chat_message("Test question", "Test answer")
        
        # Save
        self.memory_manager.save_all()
        
        # Create new manager and load
        new_manager = MemoryManager()
        new_manager.chat_history_file = self.memory_manager.chat_history_file
        new_manager._load_chat_history()
        
        assert len(new_manager.chat_history) == 1
        assert new_manager.chat_history[0]['user_input'] == "Test question"
    
    def test_get_memory_stats(self):
        """Test getting memory statistics."""
        # Add some data
        self.memory_manager.add_chat_message("Question", "Answer")
        self.memory_manager.add_command_execution("ls", "files", True)
        self.memory_manager.mark_conversation_important()
        
        stats = self.memory_manager.get_memory_stats()
        
        assert stats['chat_messages'] == 1
        assert stats['command_executions'] == 1
        assert stats['important_conversations'] == 1
        assert 'embeddings' in stats
    
    def test_lazy_loading(self):
        """Test that data is loaded lazily."""
        # Create test data file
        test_data = [{"user_input": "test", "response": "test", "timestamp": "2023-01-01T00:00:00"}]
        with open(self.memory_manager.chat_history_file, 'w') as f:
            json.dump(test_data, f)
        
        # Data should not be loaded yet
        assert not self.memory_manager._chat_loaded
        assert len(self.memory_manager.chat_history) == 0
        
        # Trigger loading
        self.memory_manager._load_chat_history()
        
        # Now data should be loaded
        assert self.memory_manager._chat_loaded
        assert len(self.memory_manager.chat_history) == 1
