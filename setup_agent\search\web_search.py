"""
Web search functionality with multiple engines and caching.
"""

import logging
import time
from typing import Dict, Any, List, Optional
from enum import Enum
from dataclasses import dataclass

from ..core.config import config
from ..core.exceptions import SearchError
from .cache import SearchCache

# Optional dependencies
try:
    import requests
    _HAS_REQUESTS = True
except ImportError:
    _HAS_REQUESTS = False
    requests = None  # type: ignore

logger = logging.getLogger(__name__)


class SearchEngine(Enum):
    """Available search engines."""
    DUCKDUCKGO = "duckduckgo"
    BING = "bing"
    GOOGLE = "google"


@dataclass
class SearchResult:
    """Search result data structure."""
    title: str
    url: str
    snippet: str
    engine: str
    timestamp: float
    metadata: Dict[str, Any]


class WebSearchManager:
    """Manages web search across multiple engines with caching."""
    
    def __init__(self):
        self.cache = SearchCache()
        self.engines = {
            SearchEngine.DUCKDUCKGO: self._search_duckduckgo,
            SearchEngine.BING: self._search_bing,
            SearchEngine.GOOGLE: self._search_google
        }
        
        # Configuration
        self.timeout = config.get(['search', 'timeout'], 10)
        self.max_results = config.get(['search', 'max_results'], 10)
        self.rate_limit = config.get(['search', 'rate_limit_seconds'], 1)
        
        # API keys
        self.bing_api_key = config.get(['search', 'bing_api_key'], '')
        self.google_api_key = config.get(['search', 'google_api_key'], '')
        self.google_engine_id = config.get(['search', 'google_engine_id'], '')
        
        self._last_search_time = 0
    
    def search(
        self, 
        query: str, 
        engine: Optional[SearchEngine] = None,
        max_results: Optional[int] = None,
        use_cache: bool = True
    ) -> List[SearchResult]:
        """Perform web search with specified engine."""
        if not query.strip():
            raise SearchError("Search query cannot be empty")
        
        # Ensure max_results is always an integer
        max_results = max_results or self.max_results
        if max_results is None:
            max_results = 10  # fallback default
        
        # Try cache first
        if use_cache:
            cached_results = self.cache.get(query, engine)
            if cached_results:
                logger.debug(f"Cache hit for query: {query}")
                return cached_results[:max_results]
        
        # Auto-select engine if not specified
        if engine is None:
            engine = self._select_best_engine()
        
        # Rate limiting
        self._apply_rate_limit()
        
        try:
            # Perform search
            results = self._perform_search(query, engine, max_results)
            
            # Cache results
            if use_cache and results:
                self.cache.set(query, engine, results)
            
            logger.info(f"Search completed: {len(results)} results for '{query}' using {engine.value}")
            return results
            
        except Exception as e:
            logger.error(f"Search failed for '{query}' using {engine.value}: {e}")
            raise SearchError(f"Search failed: {e}")
    
    def search_multiple_engines(
        self, 
        query: str, 
        engines: Optional[List[SearchEngine]] = None,
        max_results_per_engine: int = 5
    ) -> Dict[SearchEngine, List[SearchResult]]:
        """Search using multiple engines and return combined results."""
        engines = engines or [SearchEngine.DUCKDUCKGO, SearchEngine.BING]
        results = {}
        
        for engine in engines:
            try:
                engine_results = self.search(query, engine, max_results_per_engine)
                results[engine] = engine_results
            except Exception as e:
                logger.warning(f"Search failed for engine {engine.value}: {e}")
                results[engine] = []
        
        return results
    
    def _perform_search(self, query: str, engine: SearchEngine, max_results: int) -> List[SearchResult]:
        """Perform search with specified engine."""
        search_func = self.engines.get(engine)
        if not search_func:
            raise SearchError(f"Unsupported search engine: {engine}")
        
        return search_func(query, max_results)
    
    def _search_duckduckgo(self, query: str, max_results: int) -> List[SearchResult]:
        """Search using DuckDuckGo."""
        if not _HAS_REQUESTS or requests is None:
            raise SearchError("Requests library not available")
        
        try:
            url = "https://api.duckduckgo.com/"
            params = {
                'q': query,
                'format': 'json',
                'no_html': '1',
                'skip_disambig': '1'
            }
            
            response = requests.get(url, params=params, timeout=self.timeout)
            response.raise_for_status()
            data = response.json()
            
            results = []
            
            # Process instant answer
            if data.get('Abstract'):
                results.append(SearchResult(
                    title=data.get('Heading', 'DuckDuckGo Instant Answer'),
                    url=data.get('AbstractURL', ''),
                    snippet=data.get('Abstract', ''),
                    engine=SearchEngine.DUCKDUCKGO.value,
                    timestamp=time.time(),
                    metadata={'type': 'instant_answer'}
                ))
            
            # Process related topics
            for topic in data.get('RelatedTopics', [])[:max_results]:
                if isinstance(topic, dict) and 'Text' in topic:
                    results.append(SearchResult(
                        title=topic.get('Text', '').split(' - ')[0],
                        url=topic.get('FirstURL', ''),
                        snippet=topic.get('Text', ''),
                        engine=SearchEngine.DUCKDUCKGO.value,
                        timestamp=time.time(),
                        metadata={'type': 'related_topic'}
                    ))
            
            return results[:max_results]
            
        except Exception as e:
            logger.error(f"DuckDuckGo search failed: {e}")
            return []
    
    def _search_bing(self, query: str, max_results: int) -> List[SearchResult]:
        """Search using Bing Search API."""
        if not self.bing_api_key:
            logger.warning("Bing API key not configured")
            return []
        
        if not _HAS_REQUESTS or requests is None:
            raise SearchError("Requests library not available")
        
        try:
            url = "https://api.bing.microsoft.com/v7.0/search"
            headers = {'Ocp-Apim-Subscription-Key': self.bing_api_key}
            params = {
                'q': query,
                'count': max_results,
                'responseFilter': 'Webpages'
            }
            
            response = requests.get(url, headers=headers, params=params, timeout=self.timeout)
            response.raise_for_status()
            data = response.json()
            
            results = []
            for item in data.get('webPages', {}).get('value', []):
                results.append(SearchResult(
                    title=item.get('name', ''),
                    url=item.get('url', ''),
                    snippet=item.get('snippet', ''),
                    engine=SearchEngine.BING.value,
                    timestamp=time.time(),
                    metadata={'dateLastCrawled': item.get('dateLastCrawled')}
                ))
            
            return results
            
        except Exception as e:
            logger.error(f"Bing search failed: {e}")
            return []
    
    def _search_google(self, query: str, max_results: int) -> List[SearchResult]:
        """Search using Google Custom Search API."""
        if not self.google_api_key or not self.google_engine_id:
            logger.warning("Google Custom Search API not configured")
            return []
        
        if not _HAS_REQUESTS or requests is None:
            raise SearchError("Requests library not available")
        
        try:
            url = "https://www.googleapis.com/customsearch/v1"
            params = {
                'key': self.google_api_key,
                'cx': self.google_engine_id,
                'q': query,
                'num': min(max_results, 10)  # Google API limit
            }
            
            response = requests.get(url, params=params, timeout=self.timeout)
            response.raise_for_status()
            data = response.json()
            
            results = []
            for item in data.get('items', []):
                results.append(SearchResult(
                    title=item.get('title', ''),
                    url=item.get('link', ''),
                    snippet=item.get('snippet', ''),
                    engine=SearchEngine.GOOGLE.value,
                    timestamp=time.time(),
                    metadata={
                        'displayLink': item.get('displayLink'),
                        'formattedUrl': item.get('formattedUrl')
                    }
                ))
            
            return results
            
        except Exception as e:
            logger.error(f"Google search failed: {e}")
            return []
    
    def _select_best_engine(self) -> SearchEngine:
        """Select the best available search engine."""
        # Prefer engines with API keys configured
        if self.google_api_key and self.google_engine_id:
            return SearchEngine.GOOGLE
        elif self.bing_api_key:
            return SearchEngine.BING
        else:
            return SearchEngine.DUCKDUCKGO
    
    def _apply_rate_limit(self) -> None:
        """Apply rate limiting between searches."""
        current_time = time.time()
        time_since_last = current_time - self._last_search_time
        
        if time_since_last < self.rate_limit:
            sleep_time = self.rate_limit - time_since_last
            logger.debug(f"Rate limiting: sleeping for {sleep_time:.2f} seconds")
            time.sleep(sleep_time)
        
        self._last_search_time = time.time()
    
    def get_available_engines(self) -> List[SearchEngine]:
        """Get list of available search engines."""
        available = [SearchEngine.DUCKDUCKGO]  # Always available
        
        if self.bing_api_key:
            available.append(SearchEngine.BING)
        
        if self.google_api_key and self.google_engine_id:
            available.append(SearchEngine.GOOGLE)
        
        return available
    
    def clear_cache(self) -> None:
        """Clear search cache."""
        self.cache.clear()
        logger.info("Search cache cleared")


# Global search manager instance
search_manager = WebSearchManager()
