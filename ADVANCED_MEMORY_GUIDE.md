# 🧠 Advanced Memory and Learning System Guide

## Overview

SetupAgent now includes a comprehensive advanced memory and learning system that provides:

- **SQLite Memory Database**: Persistent storage for conversations, knowledge, and learning patterns
- **Web Search Integration**: Automatic learning from web content with intelligent extraction
- **Adaptive Learning**: Feedback-based improvement and pattern recognition
- **Intelligent Context Retrieval**: Enhanced responses using past knowledge and experiences

## 🚀 Quick Start

### 1. Verify Installation
```bash
# Run the setup script
python setup_advanced_memory.py

# Run comprehensive tests
python test_advanced_memory.py
```

### 2. Basic Usage
```python
from memory_integration import initialize_advanced_memory, get_enhanced_context

# Load configuration
with open('config.json', 'r') as f:
    config = json.load(f)

# Initialize advanced memory
initialize_advanced_memory(config)

# Get enhanced context for any query
context = get_enhanced_context("How to install Python packages?")
print(context)
```

### 3. Integration with SetupAgent
```python
import setup_agent
from memory_integration import patch_setup_agent

# Patch SetupAgent with advanced memory
patch_setup_agent()

# Now use SetupAgent normally - it automatically uses advanced memory
response = setup_agent.handle_conversation("How do I set up a development environment?")
```

## 📊 Features Overview

### 1. SQLite Memory Database System

**Tables Created:**
- `memory_entries` - Core memory storage with embeddings
- `conversations` - Detailed conversation history
- `web_content` - Extracted and analyzed web content
- `user_preferences` - Learned user preferences and patterns
- `command_executions` - Command history with success tracking
- `knowledge_validation` - Feedback and validation data
- `learning_patterns` - Identified patterns and behaviors
- `knowledge_connections` - Relationships between knowledge items

**Memory Types:**
- `CONVERSATION` - Chat interactions (30-day retention)
- `FACTUAL_KNOWLEDGE` - Facts and information (365-day retention)
- `PROCEDURAL_KNOWLEDGE` - How-to information (180-day retention)
- `USER_PREFERENCE` - User preferences (365-day retention)
- `WEB_CONTENT` - Web-sourced information (90-day retention)
- `COMMAND_EXECUTION` - Command results (60-day retention)
- `LEARNING_PATTERN` - Behavioral patterns (180-day retention)
- `KNOWLEDGE_SNIPPET` - Code examples and snippets (120-day retention)

### 2. Web Search Integration with Learning

**Automatic Content Extraction:**
```python
# Automatically triggered for queries containing:
# "latest", "current", "new", "how to", "tutorial", "guide"

memory_system.search_and_learn("Python virtual environment setup", max_results=3)
```

**Content Analysis:**
- Title and summary extraction
- Key facts identification
- Code example extraction
- Automatic tagging
- Source reliability assessment
- Cross-reference validation

**Trusted Domains (High Reliability):**
- docs.python.org
- developer.mozilla.org
- docs.microsoft.com
- kubernetes.io
- docs.docker.com

### 3. Adaptive Learning System

**Feedback Integration:**
```python
# Record user feedback (1-5 scale)
memory_system.record_feedback(memory_id, feedback_score=5)

# Learn from corrections
memory_system.learn_from_correction(
    original_response="Incorrect information",
    corrected_response="Correct information",
    context="User correction"
)
```

**Pattern Recognition:**
- Question type patterns
- Command success patterns
- User preference patterns
- Help-seeking behaviors
- Error patterns and solutions

**Confidence Scoring:**
- Source-based reliability (0.1 - 1.0)
- User feedback adjustments
- Cross-validation scoring
- Access frequency weighting

### 4. Memory Retrieval and Context Enhancement

**Intelligent Context Generation:**
```python
context = memory_system.get_intelligent_context("Python setup", max_context_items=5)
```

**Context Sources:**
- Semantically similar past conversations
- Related web content with key facts
- Learned patterns and behaviors
- User preferences and settings
- Connected knowledge items

**Search Capabilities:**
- Semantic search using embeddings
- Filtered search by memory type
- Confidence-based filtering
- Tag-based categorization
- Time-based relevance

## ⚙️ Configuration

### Advanced Memory Settings (`config.json`)

```json
{
  "advanced_memory": {
    "enabled": true,
    "database_path": "memory_data/advanced_memory.db",
    "auto_cleanup": true,
    "cleanup_interval_hours": 24,
    "retention_policies": {
      "conversation": 30,
      "factual_knowledge": 365,
      "procedural_knowledge": 180,
      "user_preference": 365,
      "web_content": 90,
      "command_execution": 60,
      "learning_pattern": 180,
      "knowledge_snippet": 120
    },
    "web_learning": {
      "enabled": true,
      "auto_search_threshold": 0.7,
      "max_pages_per_search": 3,
      "content_extraction_timeout": 15,
      "rate_limit_seconds": 2
    },
    "learning_system": {
      "enabled": true,
      "pattern_detection": true,
      "auto_feedback_learning": true,
      "confidence_adjustment_rate": 0.1,
      "min_pattern_frequency": 3,
      "consolidation_similarity_threshold": 0.9
    },
    "context_enhancement": {
      "max_context_items": 5,
      "include_web_content": true,
      "include_learning_patterns": true,
      "include_user_preferences": true,
      "context_relevance_threshold": 0.5
    }
  }
}
```

## 🔧 Advanced Usage

### Direct Memory System Access

```python
from advanced_memory import AdvancedMemorySystem, MemoryType, SourceType

# Initialize
memory_system = AdvancedMemorySystem(config)

# Store knowledge
memory_id = memory_system.store_memory(
    MemoryType.FACTUAL_KNOWLEDGE,
    "Docker containers are lightweight, portable environments",
    metadata={"topic": "containerization"},
    source_type=SourceType.WEB_SEARCH,
    confidence=0.9,
    tags=["docker", "containers", "devops"]
)

# Retrieve memories
memories = memory_system.retrieve_memories(
    query="container technology",
    memory_type=MemoryType.FACTUAL_KNOWLEDGE,
    min_confidence=0.7
)

# Get statistics
stats = memory_system.get_memory_statistics()
print(f"Total memories: {stats['overall']['total_memories']}")
```

### Web Content Learning

```python
# Extract content from URL
web_content = memory_system.extract_web_content(
    "https://docs.docker.com/get-started/"
)

if web_content:
    # Store extracted content
    memory_system.store_web_content(web_content)
    
    # Content includes:
    # - web_content.title
    # - web_content.summary
    # - web_content.key_facts
    # - web_content.code_examples
    # - web_content.tags
    # - web_content.source_reliability
```

### Learning Pattern Analysis

```python
# Store learning patterns
pattern_id = memory_system.store_learning_pattern(
    "user_behavior",
    {
        "preferred_explanation_style": "detailed_with_examples",
        "common_topics": ["python", "docker", "git"],
        "help_frequency": "high"
    }
)

# Identify frequent patterns
patterns = memory_system.identify_frequent_patterns(min_frequency=3)
for pattern in patterns:
    print(f"Pattern: {pattern['pattern_type']}")
    print(f"Frequency: {pattern['frequency']}")
    print(f"Effectiveness: {pattern['effectiveness_score']}")
```

### Memory Maintenance

```python
# Cleanup old memories
cleanup_stats = memory_system.cleanup_old_memories()
print(f"Archived memories: {cleanup_stats}")

# Consolidate similar memories
consolidated = memory_system.consolidate_similar_memories()
print(f"Consolidated {consolidated} similar memories")

# Export knowledge base
memory_system.export_knowledge_base("knowledge_backup.json")
```

## 📈 Monitoring and Analytics

### Memory Statistics

```python
stats = memory_system.get_memory_statistics()

# Overall statistics
print(f"Total memories: {stats['overall']['total_memories']}")
print(f"Active memories: {stats['overall']['active_memories']}")
print(f"Average confidence: {stats['overall']['avg_confidence']}")

# Memory by type
for mem_type, type_stats in stats['memory_by_type'].items():
    print(f"{mem_type}: {type_stats['count']} memories")
    print(f"  Avg confidence: {type_stats['avg_confidence']}")
    print(f"  Avg access count: {type_stats['avg_access_count']}")

# Web content statistics
web_stats = stats['web_content']
print(f"Web pages stored: {web_stats['total_pages']}")
print(f"Average reliability: {web_stats['avg_reliability']}")

# Learning patterns
pattern_stats = stats['learning_patterns']
print(f"Learning patterns: {pattern_stats['total_patterns']}")
print(f"Average effectiveness: {pattern_stats['avg_effectiveness']}")
```

## 🛠️ Troubleshooting

### Common Issues

1. **Database Connection Errors**
   ```bash
   # Check if memory_data directory exists
   ls -la memory_data/
   
   # Reinitialize if needed
   python setup_advanced_memory.py
   ```

2. **Embeddings Not Working**
   ```bash
   # Verify Ollama is running
   ollama list
   
   # Check if nomic-embed-text is installed
   ollama pull nomic-embed-text
   ```

3. **Web Content Extraction Failing**
   - Check network connectivity
   - Verify target website allows scraping
   - Increase timeout in configuration

4. **Memory Performance Issues**
   ```python
   # Run cleanup
   cleanup_stats = memory_system.cleanup_old_memories()
   
   # Consolidate similar memories
   consolidated = memory_system.consolidate_similar_memories()
   ```

### Debug Mode

```python
import logging
logging.basicConfig(level=logging.DEBUG)

# This will show detailed logs for all memory operations
```

## 🔒 Privacy and Security

- All data stored locally in SQLite database
- No external API calls except for web content extraction
- User data never leaves your system
- Configurable retention policies
- Automatic cleanup of old data
- Export capabilities for data portability

## 🎯 Best Practices

1. **Regular Maintenance**: Run cleanup weekly
2. **Feedback Training**: Provide feedback on responses to improve learning
3. **Configuration Tuning**: Adjust retention policies based on usage
4. **Monitoring**: Check statistics regularly for insights
5. **Backup**: Export knowledge base periodically

---

The advanced memory system transforms SetupAgent into a truly intelligent assistant that learns from every interaction and continuously improves its responses based on accumulated knowledge and user feedback.
