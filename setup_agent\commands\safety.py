"""
Command safety validation and security checks.
"""

import re
import logging
from typing import List, Dict, Any, Optional, Set
from enum import Enum
from dataclasses import dataclass

from ..core.config import config
from ..core.exceptions import SecurityError

logger = logging.getLogger(__name__)


class CommandSafetyLevel(Enum):
    """Command safety levels."""
    SAFE = "safe"
    CAUTION = "caution"
    DANGEROUS = "dangerous"
    BLOCKED = "blocked"


@dataclass
class SafetyResult:
    """Result of safety validation."""
    level: CommandSafetyLevel
    allowed: bool
    reason: str
    suggestions: List[str]
    metadata: Dict[str, Any]


class SafetyValidator:
    """Validates command safety and prevents dangerous operations."""
    
    def __init__(self):
        # Load safety configuration
        self.enabled = config.get(['security', 'command_validation'], True)
        self.strict_mode = config.get(['security', 'strict_mode'], False)
        
        # Command patterns
        self._load_patterns()
        
        # File system protection
        self.protected_paths = set(config.get(['security', 'protected_paths'], [
            '/', '/bin', '/sbin', '/usr', '/etc', '/var', '/sys', '/proc',
            'C:\\Windows', 'C:\\Program Files', 'C:\\Program Files (x86)',
            '/System', '/Library', '/Applications'
        ]))
        
        # Network restrictions
        self.allow_network = config.get(['security', 'allow_network_commands'], False)
        
    def validate_command(self, command: str, context: Optional[Dict[str, Any]] = None) -> SafetyResult:
        """Validate command safety."""
        if not self.enabled:
            return SafetyResult(
                level=CommandSafetyLevel.SAFE,
                allowed=True,
                reason="Safety validation disabled",
                suggestions=[],
                metadata={}
            )
        
        command = command.strip()
        context = context or {}
        
        # Check for blocked patterns
        blocked_result = self._check_blocked_patterns(command)
        if blocked_result:
            return blocked_result
        
        # Check for dangerous patterns
        dangerous_result = self._check_dangerous_patterns(command)
        if dangerous_result:
            return dangerous_result
        
        # Check file system access
        fs_result = self._check_filesystem_access(command)
        if fs_result:
            return fs_result
        
        # Check network access
        network_result = self._check_network_access(command)
        if network_result:
            return network_result
        
        # Check for privilege escalation
        privilege_result = self._check_privilege_escalation(command)
        if privilege_result:
            return privilege_result
        
        # Command appears safe
        return SafetyResult(
            level=CommandSafetyLevel.SAFE,
            allowed=True,
            reason="Command passed all safety checks",
            suggestions=[],
            metadata={'validated': True}
        )
    
    def _load_patterns(self) -> None:
        """Load command patterns from configuration."""
        # Blocked patterns (never allow)
        self.blocked_patterns = [
            r'\brm\s+-rf\s+/',  # rm -rf /
            r'\bformat\s+[c-z]:', # format C:
            r'\bdel\s+/[sq]',   # del /s /q
            r'\bshutdown\s',    # shutdown
            r'\breboot\s',      # reboot
            r'\bmkfs\.',        # mkfs.ext4, etc.
            r'\bdd\s+if=.*of=/dev/', # dd to device
            r'>\s*/dev/sd[a-z]', # redirect to disk
            r'\bkill\s+-9\s+1\b', # kill init
        ]
        
        # Dangerous patterns (require confirmation)
        self.dangerous_patterns = [
            r'\brm\s+.*-r',     # recursive delete
            r'\bdel\s+.*[/\\]', # delete with path
            r'\bchmod\s+777',   # open permissions
            r'\bchown\s+.*:',   # change ownership
            r'\bsudo\s+',       # privilege escalation
            r'\bsu\s+',         # switch user
            r'\bcurl.*\|\s*sh', # pipe to shell
            r'\bwget.*\|\s*sh', # pipe to shell
            r'>\s*/etc/',       # write to system config
        ]
        
        # Network patterns
        self.network_patterns = [
            r'\bcurl\s+',
            r'\bwget\s+',
            r'\bscp\s+',
            r'\brsync\s+.*:',
            r'\bssh\s+',
            r'\bftp\s+',
            r'\btelnet\s+',
        ]
        
        # Compile patterns for performance
        self.compiled_blocked = [re.compile(p, re.IGNORECASE) for p in self.blocked_patterns]
        self.compiled_dangerous = [re.compile(p, re.IGNORECASE) for p in self.dangerous_patterns]
        self.compiled_network = [re.compile(p, re.IGNORECASE) for p in self.network_patterns]
    
    def _check_blocked_patterns(self, command: str) -> Optional[SafetyResult]:
        """Check for blocked command patterns."""
        for pattern in self.compiled_blocked:
            if pattern.search(command):
                return SafetyResult(
                    level=CommandSafetyLevel.BLOCKED,
                    allowed=False,
                    reason=f"Command matches blocked pattern: {pattern.pattern}",
                    suggestions=["This command is permanently blocked for safety"],
                    metadata={'pattern': pattern.pattern}
                )
        return None
    
    def _check_dangerous_patterns(self, command: str) -> Optional[SafetyResult]:
        """Check for dangerous command patterns."""
        for pattern in self.compiled_dangerous:
            if pattern.search(command):
                allowed = not self.strict_mode  # Allow in non-strict mode with warning
                return SafetyResult(
                    level=CommandSafetyLevel.DANGEROUS,
                    allowed=allowed,
                    reason=f"Command matches dangerous pattern: {pattern.pattern}",
                    suggestions=[
                        "Review the command carefully before execution",
                        "Consider using safer alternatives",
                        "Make sure you have backups"
                    ],
                    metadata={'pattern': pattern.pattern, 'requires_confirmation': True}
                )
        return None
    
    def _check_filesystem_access(self, command: str) -> Optional[SafetyResult]:
        """Check for dangerous filesystem access."""
        # Extract potential file paths
        paths = self._extract_paths(command)
        
        for path in paths:
            # Check against protected paths
            for protected in self.protected_paths:
                if path.startswith(protected):
                    return SafetyResult(
                        level=CommandSafetyLevel.DANGEROUS,
                        allowed=not self.strict_mode,
                        reason=f"Command accesses protected path: {path}",
                        suggestions=[
                            f"Avoid modifying system directory: {protected}",
                            "Use a safer location for file operations"
                        ],
                        metadata={'protected_path': protected, 'accessed_path': path}
                    )
        
        return None
    
    def _check_network_access(self, command: str) -> Optional[SafetyResult]:
        """Check for network access."""
        if not self.allow_network:
            for pattern in self.compiled_network:
                if pattern.search(command):
                    return SafetyResult(
                        level=CommandSafetyLevel.CAUTION,
                        allowed=False,
                        reason="Network commands are disabled",
                        suggestions=[
                            "Enable network commands in configuration if needed",
                            "Use the built-in web search functionality instead"
                        ],
                        metadata={'network_command': True}
                    )
        return None
    
    def _check_privilege_escalation(self, command: str) -> Optional[SafetyResult]:
        """Check for privilege escalation attempts."""
        escalation_patterns = [
            r'\bsudo\s+',
            r'\bsu\s+',
            r'\brunas\s+',
            r'\belevate\s+',
        ]
        
        for pattern_str in escalation_patterns:
            pattern = re.compile(pattern_str, re.IGNORECASE)
            if pattern.search(command):
                return SafetyResult(
                    level=CommandSafetyLevel.DANGEROUS,
                    allowed=not self.strict_mode,
                    reason="Command attempts privilege escalation",
                    suggestions=[
                        "Avoid using elevated privileges when possible",
                        "Ensure you understand the security implications"
                    ],
                    metadata={'privilege_escalation': True}
                )
        
        return None
    
    def _extract_paths(self, command: str) -> List[str]:
        """Extract potential file paths from command."""
        # Simple path extraction - could be improved
        try:
            import shlex
            tokens = shlex.split(command)
        except (ValueError, ImportError):
            # Fallback for malformed commands or missing shlex
            tokens = command.split()

        paths = []
        for token in tokens:
            # Look for path-like tokens
            if ('/' in token or '\\' in token) and not token.startswith('-'):
                paths.append(token)

        return paths
    
    def get_safety_summary(self) -> Dict[str, Any]:
        """Get safety validator configuration summary."""
        return {
            'enabled': self.enabled,
            'strict_mode': self.strict_mode,
            'protected_paths_count': len(self.protected_paths),
            'allow_network': self.allow_network,
            'blocked_patterns_count': len(self.blocked_patterns),
            'dangerous_patterns_count': len(self.dangerous_patterns),
            'network_patterns_count': len(self.network_patterns)
        }


# Global safety validator instance
safety_validator = SafetyValidator()
