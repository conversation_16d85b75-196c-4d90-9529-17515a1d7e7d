"""
Observer pattern implementation for event handling.
"""

import logging
from typing import Dict, List, Any, Callable, Optional, Set
from enum import Enum
from dataclasses import dataclass
from abc import ABC, abstractmethod
import threading
import time

logger = logging.getLogger(__name__)


class EventType(Enum):
    """System event types."""
    # LLM Events
    LLM_REQUEST_START = "llm_request_start"
    LLM_REQUEST_COMPLETE = "llm_request_complete"
    LLM_REQUEST_ERROR = "llm_request_error"
    
    # Command Events
    COMMAND_EXECUTE_START = "command_execute_start"
    COMMAND_EXECUTE_COMPLETE = "command_execute_complete"
    COMMAND_EXECUTE_ERROR = "command_execute_error"
    COMMAND_BLOCKED = "command_blocked"
    
    # Memory Events
    MEMORY_STORE = "memory_store"
    MEMORY_RETRIEVE = "memory_retrieve"
    MEMORY_CLEANUP = "memory_cleanup"
    
    # Search Events
    SEARCH_START = "search_start"
    SEARCH_COMPLETE = "search_complete"
    SEARCH_ERROR = "search_error"
    
    # System Events
    SYSTEM_START = "system_start"
    SYSTEM_SHUTDOWN = "system_shutdown"
    SYSTEM_ERROR = "system_error"
    
    # Configuration Events
    CONFIG_CHANGED = "config_changed"
    CONFIG_RELOAD = "config_reload"


@dataclass
class Event:
    """Event data structure."""
    type: EventType
    source: str
    timestamp: float
    data: Dict[str, Any]
    metadata: Dict[str, Any]


class Observer(ABC):
    """Abstract observer interface."""
    
    @abstractmethod
    def handle_event(self, event: Event) -> None:
        """Handle an event."""
        pass
    
    def get_interested_events(self) -> Set[EventType]:
        """Return set of event types this observer is interested in."""
        return set(EventType)  # Default: interested in all events


class EventManager:
    """Central event manager implementing observer pattern."""
    
    def __init__(self):
        self._observers: Dict[EventType, List[Observer]] = {}
        self._global_observers: List[Observer] = []
        self._event_history: List[Event] = []
        self._max_history = 1000
        self._lock = threading.RLock()
        
        # Statistics
        self._stats = {
            'events_published': 0,
            'events_handled': 0,
            'errors': 0
        }
    
    def subscribe(self, observer: Observer, event_types: Optional[Set[EventType]] = None) -> None:
        """Subscribe observer to specific event types."""
        with self._lock:
            if event_types is None:
                # Subscribe to all events the observer is interested in
                event_types = observer.get_interested_events()
            
            if not event_types:
                # Global observer (receives all events)
                self._global_observers.append(observer)
                logger.debug(f"Registered global observer: {observer.__class__.__name__}")
            else:
                # Specific event type observers
                for event_type in event_types:
                    if event_type not in self._observers:
                        self._observers[event_type] = []
                    self._observers[event_type].append(observer)
                
                logger.debug(f"Registered observer {observer.__class__.__name__} for events: {event_types}")
    
    def unsubscribe(self, observer: Observer, event_types: Optional[Set[EventType]] = None) -> None:
        """Unsubscribe observer from event types."""
        with self._lock:
            if event_types is None:
                # Remove from all subscriptions
                if observer in self._global_observers:
                    self._global_observers.remove(observer)
                
                for observers_list in self._observers.values():
                    if observer in observers_list:
                        observers_list.remove(observer)
            else:
                # Remove from specific event types
                for event_type in event_types:
                    if event_type in self._observers and observer in self._observers[event_type]:
                        self._observers[event_type].remove(observer)
            
            logger.debug(f"Unsubscribed observer: {observer.__class__.__name__}")
    
    def publish(self, event_type: EventType, source: str, data: Dict[str, Any], metadata: Optional[Dict[str, Any]] = None) -> None:
        """Publish an event to all interested observers."""
        event = Event(
            type=event_type,
            source=source,
            timestamp=time.time(),
            data=data,
            metadata=metadata or {}
        )
        
        self._publish_event(event)
    
    def _publish_event(self, event: Event) -> None:
        """Internal method to publish event."""
        with self._lock:
            # Add to history
            self._event_history.append(event)
            if len(self._event_history) > self._max_history:
                self._event_history.pop(0)
            
            self._stats['events_published'] += 1
            
            # Notify observers
            observers_to_notify = []
            
            # Add global observers
            observers_to_notify.extend(self._global_observers)
            
            # Add specific event type observers
            if event.type in self._observers:
                observers_to_notify.extend(self._observers[event.type])
            
            # Notify all observers
            for observer in observers_to_notify:
                try:
                    observer.handle_event(event)
                    self._stats['events_handled'] += 1
                except Exception as e:
                    self._stats['errors'] += 1
                    logger.error(f"Observer {observer.__class__.__name__} failed to handle event {event.type}: {e}")
    
    def get_event_history(self, event_type: Optional[EventType] = None, limit: Optional[int] = None) -> List[Event]:
        """Get event history, optionally filtered by type."""
        with self._lock:
            events = self._event_history.copy()
            
            if event_type:
                events = [e for e in events if e.type == event_type]
            
            if limit:
                events = events[-limit:]
            
            return events
    
    def get_stats(self) -> Dict[str, Any]:
        """Get event manager statistics."""
        with self._lock:
            return {
                **self._stats,
                'total_observers': len(self._global_observers) + sum(len(obs) for obs in self._observers.values()),
                'global_observers': len(self._global_observers),
                'event_type_observers': {et.value: len(obs) for et, obs in self._observers.items()},
                'history_size': len(self._event_history)
            }
    
    def clear_history(self) -> None:
        """Clear event history."""
        with self._lock:
            self._event_history.clear()
            logger.info("Event history cleared")


class LoggingObserver(Observer):
    """Observer that logs all events."""
    
    def __init__(self, log_level: int = logging.INFO):
        self.log_level = log_level
        self.logger = logging.getLogger(f"{__name__}.LoggingObserver")
    
    def handle_event(self, event: Event) -> None:
        """Log the event."""
        self.logger.log(
            self.log_level,
            f"Event: {event.type.value} from {event.source} | {event.data}"
        )


class MetricsObserver(Observer):
    """Observer that collects metrics from events."""
    
    def __init__(self):
        self.metrics: Dict[str, Any] = {
            'event_counts': {},
            'source_counts': {},
            'error_counts': {},
            'performance_metrics': {}
        }
        self._lock = threading.RLock()
    
    def handle_event(self, event: Event) -> None:
        """Collect metrics from event."""
        with self._lock:
            # Count events by type
            event_type = event.type.value
            self.metrics['event_counts'][event_type] = self.metrics['event_counts'].get(event_type, 0) + 1
            
            # Count events by source
            source = event.source
            self.metrics['source_counts'][source] = self.metrics['source_counts'].get(source, 0) + 1
            
            # Track errors
            if 'error' in event_type.lower():
                self.metrics['error_counts'][event_type] = self.metrics['error_counts'].get(event_type, 0) + 1
            
            # Track performance metrics
            if 'execution_time' in event.data:
                if event_type not in self.metrics['performance_metrics']:
                    self.metrics['performance_metrics'][event_type] = []
                self.metrics['performance_metrics'][event_type].append(event.data['execution_time'])
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get collected metrics."""
        with self._lock:
            return self.metrics.copy()
    
    def get_interested_events(self) -> Set[EventType]:
        """Interested in all events for metrics."""
        return set(EventType)


# Global event manager instance
event_manager = EventManager()

# Register default observers
event_manager.subscribe(LoggingObserver(logging.DEBUG))
event_manager.subscribe(MetricsObserver())
