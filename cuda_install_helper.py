#!/usr/bin/env python3
"""
CUDA Installation Helper Script
Helps verify system readiness and post-installation validation
"""

import os
import subprocess
import sys
from typing import Dict, Any

def run_command(cmd: str, capture_output: bool = True) -> Dict[str, Any]:
    """Run command and return result."""
    try:
        result = subprocess.run(cmd, shell=True, capture_output=capture_output, text=True)
        return {
            "success": result.returncode == 0,
            "stdout": result.stdout.strip() if capture_output else "",
            "stderr": result.stderr.strip() if capture_output else "",
            "returncode": result.returncode
        }
    except Exception as e:
        return {
            "success": False,
            "stdout": "",
            "stderr": str(e),
            "returncode": -1
        }

def check_system_readiness() -> bool:
    """Check if system is ready for CUDA installation."""
    print("🔍 Checking System Readiness for CUDA Installation")
    print("=" * 60)

    # Check NVIDIA driver
    print("1. Checking NVIDIA Driver...")
    result = run_command("nvidia-smi")
    if result["success"]:
        print("   ✅ NVIDIA Driver: Available")
        # Extract driver version
        lines = result["stdout"].split('\n')
        for line in lines:
            if "Driver Version:" in line:
                driver_version = line.split("Driver Version:")[1].split()[0]
                cuda_version = line.split("CUDA Version:")[1].split()[0]
                print(f"   📊 Driver Version: {driver_version}")
                print(f"   📊 CUDA Runtime: {cuda_version}")
                break
    else:
        print("   ❌ NVIDIA Driver: Not found")
        return False
    
    # Check available disk space
    print("\n2. Checking Disk Space...")
    try:
        import shutil
        free_space = shutil.disk_usage("C:\\").free / (1024**3)  # GB
        print(f"   📊 Free space on C: drive: {free_space:.1f} GB")
        if free_space < 10:
            print("   ⚠️  Warning: Less than 10GB free space")
        else:
            print("   ✅ Sufficient disk space available")
    except Exception as e:
        print(f"   ⚠️  Could not check disk space: {e}")
    
    # Check if CUDA is already installed
    print("\n3. Checking Existing CUDA Installation...")
    cuda_paths = [
        "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA",
        "C:\\Program Files (x86)\\NVIDIA GPU Computing Toolkit\\CUDA"
    ]
    
    existing_cuda = False
    for path in cuda_paths:
        if os.path.exists(path):
            print(f"   📁 Found existing CUDA at: {path}")
            try:
                versions = os.listdir(path)
                for version in versions:
                    print(f"      • Version: {version}")
                existing_cuda = True
            except:
                pass
    
    if not existing_cuda:
        print("   ✅ No existing CUDA installation found")
    
    # Check nvcc
    print("\n4. Checking CUDA Compiler...")
    result = run_command("nvcc --version")
    if result["success"]:
        print("   ✅ NVCC: Available")
        print(f"   📊 {result['stdout'].split('release')[1].split(',')[0].strip()}")
    else:
        print("   ❌ NVCC: Not found (expected before installation)")
    
    print("\n" + "=" * 60)
    print("✅ System appears ready for CUDA installation!")
    return True

def verify_cuda_installation() -> bool:
    """Verify CUDA installation after installation."""
    print("🧪 Verifying CUDA Installation")
    print("=" * 50)

    # Check nvcc
    print("1. Testing CUDA Compiler...")
    result = run_command("nvcc --version")
    if result["success"]:
        print("   ✅ NVCC: Working")
        version_line = [line for line in result["stdout"].split('\n') if 'release' in line.lower()]
        if version_line:
            print(f"   📊 {version_line[0].strip()}")
    else:
        print("   ❌ NVCC: Not working")
        print("   💡 Try restarting your terminal/command prompt")
        return False
    
    # Check nvidia-smi
    print("\n2. Testing NVIDIA SMI...")
    result = run_command("nvidia-smi")
    if result["success"]:
        print("   ✅ nvidia-smi: Working")
    else:
        print("   ❌ nvidia-smi: Not working")
        return False
    
    # Check CUDA runtime
    print("\n3. Testing CUDA Runtime...")
    test_code = '''
import ctypes
try:
    cuda = ctypes.CDLL("nvcuda.dll")
    print("✅ CUDA Runtime: Available")
except:
    print("❌ CUDA Runtime: Not available")
'''
    
    result = run_command(f'python -c "{test_code}"')
    if result["success"] and "✅" in result["stdout"]:
        print("   ✅ CUDA Runtime: Available")
    else:
        print("   ⚠️  CUDA Runtime: May need system restart")
    
    print("\n" + "=" * 50)
    return True

def test_ollama_performance() -> None:
    """Test Ollama performance before/after CUDA installation."""
    print("🚀 Testing Ollama Performance")
    print("=" * 40)

    # Test basic Ollama connection
    print("Testing Ollama with GPU optimization...")

    try:
        # Import our setup agent to test
        sys.path.append('.')
        from setup_agent import call_ollama, DEFAULT_MODEL

        import time
        start_time = time.time()
        response = call_ollama("Hello, test CUDA performance", DEFAULT_MODEL, use_gpu_optimization=True)
        end_time = time.time()

        if not response.startswith("❌"):
            print(f"   ✅ Ollama Response Time: {end_time - start_time:.2f} seconds")
            print(f"   📝 Response: {response[:100]}...")
        else:
            print(f"   ❌ Ollama Error: {response}")

    except Exception as e:
        print(f"   ⚠️  Could not test Ollama: {e}")

def main() -> None:
    """Main function."""
    if len(sys.argv) > 1:
        if sys.argv[1] == "pre-check":
            check_system_readiness()
        elif sys.argv[1] == "verify":
            verify_cuda_installation()
        elif sys.argv[1] == "test-ollama":
            test_ollama_performance()
        else:
            print("Usage: python cuda_install_helper.py [pre-check|verify|test-ollama]")
    else:
        print("CUDA Installation Helper")
        print("Available commands:")
        print("  python cuda_install_helper.py pre-check    - Check system readiness")
        print("  python cuda_install_helper.py verify       - Verify installation")
        print("  python cuda_install_helper.py test-ollama  - Test Ollama performance")

if __name__ == "__main__":
    main()
