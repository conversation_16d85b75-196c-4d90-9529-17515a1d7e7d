"""
Input validation and security utilities.
"""

import re
import html
import logging
from typing import Any, Dict, List, Optional, Union, Callable
from dataclasses import dataclass
from pathlib import Path

from ..core.config import config
from ..core.exceptions import SecurityError

logger = logging.getLogger(__name__)


@dataclass
class ValidationResult:
    """Result of input validation."""
    valid: bool
    sanitized_input: Any
    errors: List[str]
    warnings: List[str]


class InputValidator:
    """Comprehensive input validation and sanitization."""
    
    def __init__(self):
        self.max_input_length = config.get(['security', 'max_input_length'], 10000)
        self.allow_html = config.get(['security', 'allow_html'], False)
        self.strict_mode = config.get(['security', 'strict_validation'], True)
        
        # Load validation patterns
        self._load_patterns()
    
    def _load_patterns(self) -> None:
        """Load validation patterns."""
        # Dangerous patterns to block
        self.dangerous_patterns = [
            r'<script[^>]*>.*?</script>',  # Script tags
            r'javascript:',                # JavaScript URLs
            r'on\w+\s*=',                 # Event handlers
            r'eval\s*\(',                 # eval() calls
            r'exec\s*\(',                 # exec() calls
            r'import\s+os',               # OS imports
            r'__import__',                # Dynamic imports
            r'subprocess',                # Subprocess calls
            r'system\s*\(',               # System calls
        ]
        
        # SQL injection patterns
        self.sql_injection_patterns = [
            r'(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER)\b)',
            r'(\b(UNION|OR|AND)\b.*\b(SELECT|INSERT|UPDATE|DELETE)\b)',
            r'(--|#|/\*|\*/)',  # SQL comments
            r"('[^']*'|\"[^\"]*\")",  # String literals
        ]
        
        # Path traversal patterns
        self.path_traversal_patterns = [
            r'\.\./+',     # Directory traversal
            r'\.\.\\+',    # Windows directory traversal
            r'/etc/',      # System directories
            r'/proc/',     # Process directories
            r'C:\\Windows', # Windows system
        ]
        
        # Compile patterns for performance
        self.compiled_dangerous = [re.compile(p, re.IGNORECASE | re.DOTALL) for p in self.dangerous_patterns]
        self.compiled_sql = [re.compile(p, re.IGNORECASE) for p in self.sql_injection_patterns]
        self.compiled_path = [re.compile(p, re.IGNORECASE) for p in self.path_traversal_patterns]
    
    def validate_text_input(self, text: str, max_length: Optional[int] = None) -> ValidationResult:
        """Validate and sanitize text input."""
        errors = []
        warnings = []
        
        if not isinstance(text, str):
            return ValidationResult(False, "", ["Input must be a string"], [])
        
        # Check length
        max_len = max_length or self.max_input_length
        if len(text) > max_len:
            errors.append(f"Input too long: {len(text)} > {max_len}")
            text = text[:max_len]
            warnings.append("Input truncated to maximum length")
        
        # Check for dangerous patterns
        for pattern in self.compiled_dangerous:
            if pattern.search(text):
                if self.strict_mode:
                    errors.append(f"Dangerous pattern detected: {pattern.pattern}")
                else:
                    warnings.append(f"Potentially dangerous pattern: {pattern.pattern}")
        
        # Sanitize HTML if not allowed
        if not self.allow_html:
            sanitized = html.escape(text)
            if sanitized != text:
                warnings.append("HTML entities escaped")
                text = sanitized
        
        # Check for SQL injection
        sql_detected = False
        for pattern in self.compiled_sql:
            if pattern.search(text):
                sql_detected = True
                break
        
        if sql_detected:
            if self.strict_mode:
                errors.append("Potential SQL injection detected")
            else:
                warnings.append("SQL-like patterns detected")
        
        return ValidationResult(
            valid=len(errors) == 0,
            sanitized_input=text,
            errors=errors,
            warnings=warnings
        )
    
    def validate_file_path(self, path: str) -> ValidationResult:
        """Validate file path for security."""
        errors = []
        warnings = []
        
        if not isinstance(path, str):
            return ValidationResult(False, "", ["Path must be a string"], [])
        
        # Check for path traversal
        for pattern in self.compiled_path:
            if pattern.search(path):
                errors.append(f"Path traversal detected: {pattern.pattern}")
        
        # Normalize path
        try:
            normalized_path = str(Path(path).resolve())
            if normalized_path != path:
                warnings.append("Path normalized")
                path = normalized_path
        except Exception as e:
            errors.append(f"Invalid path: {e}")
        
        # Check if path is within allowed directories
        allowed_dirs = config.get(['security', 'allowed_directories'], ['.', './data', './temp'])
        path_obj = Path(path)
        
        is_allowed = False
        for allowed_dir in allowed_dirs:
            try:
                allowed_path = Path(allowed_dir).resolve()
                if path_obj.is_relative_to(allowed_path):
                    is_allowed = True
                    break
            except (ValueError, OSError):
                continue
        
        if not is_allowed and self.strict_mode:
            errors.append(f"Path not in allowed directories: {allowed_dirs}")
        
        return ValidationResult(
            valid=len(errors) == 0,
            sanitized_input=path,
            errors=errors,
            warnings=warnings
        )
    
    def validate_command(self, command: str) -> ValidationResult:
        """Validate command input."""
        errors = []
        warnings = []
        
        # Basic text validation
        text_result = self.validate_text_input(command)
        errors.extend(text_result.errors)
        warnings.extend(text_result.warnings)
        command = text_result.sanitized_input
        
        # Command-specific validation
        dangerous_commands = [
            'rm -rf /', 'format c:', 'del /s /q', 'shutdown', 'reboot',
            'mkfs', 'dd if=', 'kill -9 1', '> /dev/sd'
        ]
        
        command_lower = command.lower()
        for dangerous in dangerous_commands:
            if dangerous in command_lower:
                errors.append(f"Dangerous command detected: {dangerous}")
        
        # Check for shell injection
        shell_chars = ['|', '&', ';', '$(', '`', '>', '<']
        for char in shell_chars:
            if char in command:
                warnings.append(f"Shell metacharacter detected: {char}")
        
        return ValidationResult(
            valid=len(errors) == 0,
            sanitized_input=command,
            errors=errors,
            warnings=warnings
        )
    
    def validate_json_input(self, data: Dict[str, Any]) -> ValidationResult:
        """Validate JSON/dictionary input."""
        errors = []
        warnings = []
        sanitized_data = {}
        
        if not isinstance(data, dict):
            return ValidationResult(False, {}, ["Input must be a dictionary"], [])
        
        # Validate each key-value pair
        for key, value in data.items():
            # Validate key
            key_result = self.validate_text_input(str(key), max_length=100)
            if not key_result.valid:
                errors.extend([f"Key '{key}': {error}" for error in key_result.errors])
                continue
            
            # Validate value based on type
            if isinstance(value, str):
                value_result = self.validate_text_input(value)
                if value_result.valid:
                    sanitized_data[key_result.sanitized_input] = value_result.sanitized_input
                else:
                    errors.extend([f"Value for '{key}': {error}" for error in value_result.errors])
            elif isinstance(value, (int, float, bool)):
                sanitized_data[key_result.sanitized_input] = value
            elif isinstance(value, list):
                # Validate list items
                sanitized_list = []
                for i, item in enumerate(value):
                    if isinstance(item, str):
                        item_result = self.validate_text_input(item)
                        if item_result.valid:
                            sanitized_list.append(item_result.sanitized_input)
                        else:
                            errors.extend([f"List item {i} for '{key}': {error}" for error in item_result.errors])
                    else:
                        sanitized_list.append(item)
                sanitized_data[key_result.sanitized_input] = sanitized_list
            else:
                warnings.append(f"Unsupported value type for '{key}': {type(value)}")
                sanitized_data[key_result.sanitized_input] = str(value)
        
        return ValidationResult(
            valid=len(errors) == 0,
            sanitized_input=sanitized_data,
            errors=errors,
            warnings=warnings
        )


class RateLimiter:
    """Rate limiting for API calls and user actions."""
    
    def __init__(self):
        self.limits: Dict[str, Dict[str, Any]] = {}
        self.enabled = config.get(['security', 'rate_limiting'], True)
    
    def check_rate_limit(self, identifier: str, action: str, limit: int, window_seconds: int) -> bool:
        """Check if action is within rate limit."""
        if not self.enabled:
            return True
        
        import time
        current_time = time.time()
        
        # Initialize tracking for this identifier/action
        key = f"{identifier}:{action}"
        if key not in self.limits:
            self.limits[key] = {'count': 0, 'window_start': current_time}
        
        limit_data = self.limits[key]
        
        # Check if we're in a new window
        if current_time - limit_data['window_start'] >= window_seconds:
            limit_data['count'] = 0
            limit_data['window_start'] = current_time
        
        # Check limit
        if limit_data['count'] >= limit:
            logger.warning(f"Rate limit exceeded for {identifier}:{action}")
            return False
        
        # Increment counter
        limit_data['count'] += 1
        return True
    
    def reset_limits(self, identifier: Optional[str] = None) -> None:
        """Reset rate limits."""
        if identifier:
            keys_to_remove = [key for key in self.limits.keys() if key.startswith(f"{identifier}:")]
            for key in keys_to_remove:
                del self.limits[key]
        else:
            self.limits.clear()


# Global instances
input_validator = InputValidator()
rate_limiter = RateLimiter()
