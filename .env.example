# SetupAgent Environment Configuration
# Copy this file to .env and fill in your actual values

# ===========================================
# LLM PROVIDER CONFIGURATIONS
# ===========================================

# Ollama Configuration
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_DEFAULT_MODEL=mistral
OLLAMA_TIMEOUT=90
OLLAMA_GPU_LAYERS=-1

# OpenAI Configuration (optional)
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_DEFAULT_MODEL=gpt-3.5-turbo
OPENAI_ORGANIZATION=your_org_id_here

# ===========================================
# SEARCH ENGINE API KEYS (optional)
# ===========================================

# Google Custom Search (optional)
GOOGLE_SEARCH_API_KEY=your_google_api_key_here
GOOGLE_SEARCH_ENGINE_ID=your_search_engine_id_here

# Bing Search (optional)
BING_SEARCH_API_KEY=your_bing_api_key_here

# ===========================================
# SECURITY & ENCRYPTION
# ===========================================

# Encryption key for sensitive data storage
# Generate with: python -c "from cryptography.fernet import Fernet; print(Fernet.generate_key().decode())"
ENCRYPTION_KEY=your_encryption_key_here

# ===========================================
# DATABASE & STORAGE
# ===========================================

# Advanced memory database path
MEMORY_DATABASE_PATH=memory_data/advanced_memory.db

# Embeddings data directory
EMBEDDINGS_DATA_DIR=embeddings_data

# ===========================================
# LOGGING & MONITORING
# ===========================================

# Log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
LOG_LEVEL=INFO
LOG_FILE=agent.log

# GPU monitoring settings
GPU_TARGET_DEVICE=0
GPU_MEMORY_RESERVE_MB=512

# ===========================================
# SEARCH CONFIGURATION
# ===========================================

# Search engine settings
SEARCH_TIMEOUT=10
SEARCH_MAX_RESULTS=10
SEARCH_RATE_LIMIT_SECONDS=1

# Search cache settings
SEARCH_CACHE_MAX_SIZE=1000
SEARCH_CACHE_TTL_SECONDS=3600

# ===========================================
# COMMAND EXECUTION SECURITY
# ===========================================

# Command execution settings
COMMANDS_EXECUTION_ENABLED=true
COMMANDS_DEFAULT_TIMEOUT=30
COMMANDS_MAX_OUTPUT_SIZE=1048576
COMMANDS_REQUIRE_CONFIRMATION=true
COMMANDS_LOG_ALL_COMMANDS=true

# Security settings
SECURITY_COMMAND_VALIDATION=true
SECURITY_STRICT_MODE=false
SECURITY_WHITELIST_ENABLED=true
SECURITY_WHITELIST_STRICT=false
SECURITY_ALLOW_NETWORK_COMMANDS=false

# Input validation
SECURITY_MAX_INPUT_LENGTH=10000
SECURITY_ALLOW_HTML=false
SECURITY_STRICT_VALIDATION=true
SECURITY_RATE_LIMITING=true

# ===========================================
# PERFORMANCE MONITORING
# ===========================================

# Memory monitoring
MEMORY_MONITORING_ENABLED=true
MEMORY_CHECK_INTERVAL=60.0

# Performance tracking
PERFORMANCE_MONITORING_ENABLED=true
PERFORMANCE_MAX_HISTORY=1000

# ===========================================
# DATABASE OPTIMIZATION
# ===========================================

# Connection pooling
DATABASE_MAX_CONNECTIONS=10
DATABASE_CONNECTION_TIMEOUT=30

# ===========================================
# DEVELOPMENT & TESTING
# ===========================================

# Development mode (enables additional debugging)
DEVELOPMENT_MODE=false

# Test database path (used during testing)
TEST_DATABASE_PATH=test_memory.db
