"""
Lazy loading embeddings manager to improve startup performance.
"""

import logging
from typing import Dict, Any, Optional, List
from ..core.config import config
from ..core.exceptions import EmbeddingsError

logger = logging.getLogger(__name__)


class LazyEmbeddingManager:
    """Lazy-loading wrapper for embeddings functionality."""
    
    def __init__(self):
        self._embedding_manager = None
        self._initialization_attempted = False
        self._is_available = False
        
    def _initialize(self) -> None:
        """Initialize the embeddings manager on first use."""
        if self._initialization_attempted:
            return
        
        self._initialization_attempted = True
        
        try:
            # Try to import the embeddings module
            from embeddings import EmbeddingManager
            
            # Initialize with configuration
            embedding_config = config.get_all()
            self._embedding_manager = EmbeddingManager(embedding_config)
            self._is_available = True
            
            logger.info("🧠 Embeddings system initialized successfully (lazy loaded)")
            
        except ImportError:
            logger.warning("📦 Embeddings module not available - enhanced memory features disabled")
            self._is_available = False
        except Exception as e:
            logger.error(f"❌ Failed to initialize embeddings: {e}")
            self._is_available = False
    
    def is_available(self) -> bool:
        """Check if embeddings are available."""
        if not self._initialization_attempted:
            self._initialize()
        return self._is_available
    
    def store_interaction(self, query: str, response: str, metadata: Optional[Dict[str, Any]] = None) -> bool:
        """Store an interaction in the embeddings system."""
        if not self.is_available():
            return False
        
        try:
            self._embedding_manager.store_interaction(query, response, metadata or {})
            return True
        except Exception as e:
            logger.error(f"Failed to store interaction: {e}")
            return False
    
    def search_similar(self, query: str, max_results: int = 5) -> List[Dict[str, Any]]:
        """Search for similar interactions."""
        if not self.is_available():
            return []
        
        try:
            return self._embedding_manager.search_similar(query, max_results)
        except Exception as e:
            logger.error(f"Failed to search similar: {e}")
            return []
    
    def get_context_for_query(self, query: str, max_context: int = 3) -> str:
        """Get relevant context for a query."""
        if not self.is_available():
            return ""
        
        try:
            similar_interactions = self.search_similar(query, max_context)
            
            if not similar_interactions:
                return ""
            
            context_parts = []
            for interaction in similar_interactions:
                context_parts.append(f"Q: {interaction.get('query', '')}")
                context_parts.append(f"A: {interaction.get('response', '')}")
            
            return "\n".join(context_parts)
            
        except Exception as e:
            logger.error(f"Failed to get context: {e}")
            return ""
    
    def cleanup_old_data(self, days: int = 30) -> bool:
        """Clean up old embeddings data."""
        if not self.is_available():
            return False
        
        try:
            if hasattr(self._embedding_manager, 'cleanup_old_data'):
                self._embedding_manager.cleanup_old_data(days)
            return True
        except Exception as e:
            logger.error(f"Failed to cleanup embeddings data: {e}")
            return False
    
    def get_stats(self) -> Dict[str, Any]:
        """Get embeddings statistics."""
        if not self.is_available():
            return {"available": False, "error": "Embeddings not available"}
        
        try:
            if hasattr(self._embedding_manager, 'get_stats'):
                stats = self._embedding_manager.get_stats()
                stats["available"] = True
                return stats
            else:
                return {"available": True, "stats": "Not implemented"}
        except Exception as e:
            logger.error(f"Failed to get embeddings stats: {e}")
            return {"available": False, "error": str(e)}


# Global lazy embeddings instance
lazy_embeddings = LazyEmbeddingManager()
